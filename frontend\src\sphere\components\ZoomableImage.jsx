// frontend/src/sphere/components/ZoomableImage.jsx
import { useState, useRef, useEffect, useCallback } from 'react';
import { FaSearchPlus, FaSearchMinus, FaHandPaper } from 'react-icons/fa';
import AuthenticatedImage from './AuthenticatedImage';
import '../styles/ZoomableImage.css';

const ZoomableImage = ({ srcUrl, alt }) => {
  const [scale, setScale] = useState(1);
  const [showZoomIndicator, setShowZoomIndicator] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isCtrlPressed, setIsCtrlPressed] = useState(false);
  const [transformOrigin, setTransformOrigin] = useState('center');
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [showDragIndicator, setShowDragIndicator] = useState(false);

  const containerRef = useRef(null);
  const wrapperRef = useRef(null);
  const indicatorTimeoutRef = useRef(null);
  const dragIndicatorTimeoutRef = useRef(null);

  // Generate a unique ID for this component instance
  const instanceId = useRef(`zoomable-image-${Math.random().toString(36).substring(2, 11)}`).current;

  // We no longer reset zoom when image changes
  // This allows users to maintain their zoom level when navigating between images
  useEffect(() => {
    // If you want to reset zoom when image changes, uncomment these lines:
    // setScale(1);
    // setTransformOrigin('center');
  }, [srcUrl]);

  // Reset position when scale changes to 1
  useEffect(() => {
    if (scale === 1) {
      setPosition({ x: 0, y: 0 });
    }
  }, [scale]);

  // Function to show zoom indicator with timeout
  const showZoomIndicatorWithTimeout = useCallback(() => {
    setShowZoomIndicator(true);

    if (indicatorTimeoutRef.current) {
      clearTimeout(indicatorTimeoutRef.current);
    }

    indicatorTimeoutRef.current = setTimeout(() => {
      setShowZoomIndicator(false);
    }, 1500);
  }, []);

  // Function to show drag indicator with timeout
  const showDragIndicatorWithTimeout = useCallback(() => {
    setShowDragIndicator(true);

    if (dragIndicatorTimeoutRef.current) {
      clearTimeout(dragIndicatorTimeoutRef.current);
    }

    dragIndicatorTimeoutRef.current = setTimeout(() => {
      setShowDragIndicator(false);
    }, 1500);
  }, []);

  // Handle mouse wheel events for zooming
  const handleWheel = useCallback((e) => {
    // Only handle zoom if CTRL is pressed
    if (e.ctrlKey) {
      // This is crucial - prevent default browser behavior
      e.preventDefault();
      e.stopPropagation();

      // Calculate new scale based on wheel direction
      const delta = e.deltaY < 0 ? 0.1 : -0.1;
      const newScale = Math.max(1, Math.min(3, scale + delta)); // Limit zoom between 1x and 3x

      // Calculate transform origin based on cursor position
      if (containerRef.current && newScale > 1) {
        const rect = containerRef.current.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;
        setTransformOrigin(`${x}% ${y}%`);

        // If we're zooming in, check if we need to adjust position to prevent empty space
        if (newScale > scale && wrapperRef.current) {
          const containerRect = containerRef.current.getBoundingClientRect();
          const imageEl = wrapperRef.current.querySelector('.loaded-image');
          const imageRect = imageEl ? imageEl.getBoundingClientRect() : wrapperRef.current.getBoundingClientRect();

          // Calculate the scaled dimensions
          const scaledWidth = imageRect.width * newScale;
          const scaledHeight = imageRect.height * newScale;

          // Calculate the current position after applying the new scale
          const currentX = position.x;
          const currentY = position.y;

          // Calculate the boundaries to ensure the image always fills the container
          let minX = 0;
          let maxX = 0;
          let minY = 0;
          let maxY = 0;

          // If the scaled image is wider than the container, calculate horizontal constraints
          if (scaledWidth > containerRect.width) {
            const overflow = (scaledWidth - containerRect.width) / (2 * newScale);
            minX = -overflow;
            maxX = overflow;
          }

          // If the scaled image is taller than the container, calculate vertical constraints
          if (scaledHeight > containerRect.height) {
            const overflow = (scaledHeight - containerRect.height) / (2 * newScale);
            minY = -overflow;
            maxY = overflow;
          }

          // Adjust position if needed to prevent empty space
          const adjustedX = Math.max(minX, Math.min(maxX, currentX));
          const adjustedY = Math.max(minY, Math.min(maxY, currentY));

          if (adjustedX !== currentX || adjustedY !== currentY) {
            setPosition({ x: adjustedX, y: adjustedY });
          }
        }
      } else {
        // Reset to center when zooming out to 1x
        setTransformOrigin('center');
        setPosition({ x: 0, y: 0 });
      }

      setScale(newScale);
      showZoomIndicatorWithTimeout();

      // If we're zooming in for the first time, show the drag indicator
      if (scale === 1 && newScale > 1) {
        showDragIndicatorWithTimeout();
      }

      return false;
    }
  }, [scale, position, showZoomIndicatorWithTimeout, showDragIndicatorWithTimeout]);

  // Handle double-click to reset zoom
  const handleDoubleClick = useCallback(() => {
    if (scale !== 1) {
      setScale(1);
      setTransformOrigin('center');
      setPosition({ x: 0, y: 0 });
      showZoomIndicatorWithTimeout();
    }
  }, [scale, showZoomIndicatorWithTimeout]);

  // Handle mouse enter/leave for hover state
  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
    if (scale > 1) {
      showDragIndicatorWithTimeout();
    }
  }, [scale, showDragIndicatorWithTimeout]);

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false);
    setIsDragging(false);
    // Don't reset zoom when mouse leaves the image
    // Just hide the zoom indicator
    setShowZoomIndicator(false);
    setShowDragIndicator(false);

    if (indicatorTimeoutRef.current) {
      clearTimeout(indicatorTimeoutRef.current);
    }

    if (dragIndicatorTimeoutRef.current) {
      clearTimeout(dragIndicatorTimeoutRef.current);
    }
  }, []);

  // Handle mouse down for dragging
  const handleMouseDown = useCallback((e) => {
    // Only enable dragging when zoomed in and not pressing ctrl (to avoid conflict with zoom)
    if (scale > 1 && !e.ctrlKey) {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  }, [scale]);

  // Handle mouse move for dragging
  const handleMouseMove = useCallback((e) => {
    if (isDragging && scale > 1) {
      e.preventDefault();

      const dx = e.clientX - dragStart.x;
      const dy = e.clientY - dragStart.y;

      // Calculate boundaries to constrain dragging
      const containerRect = containerRef.current.getBoundingClientRect();
      const wrapperRect = wrapperRef.current.getBoundingClientRect();
      const imageEl = wrapperRef.current.querySelector('.loaded-image');

      // Get the actual dimensions of the image element
      const imageRect = imageEl ? imageEl.getBoundingClientRect() : wrapperRect;

      // Calculate the scaled dimensions
      const scaledWidth = imageRect.width * scale;
      const scaledHeight = imageRect.height * scale;

      // Calculate the boundaries to ensure the image always fills the container
      // and doesn't leave empty space at the edges
      let minX = 0;
      let maxX = 0;
      let minY = 0;
      let maxY = 0;

      // If the scaled image is wider than the container, allow horizontal dragging
      if (scaledWidth > containerRect.width) {
        const overflow = (scaledWidth - containerRect.width) / (2 * scale);
        minX = -overflow;
        maxX = overflow;
      }

      // If the scaled image is taller than the container, allow vertical dragging
      if (scaledHeight > containerRect.height) {
        const overflow = (scaledHeight - containerRect.height) / (2 * scale);
        minY = -overflow;
        maxY = overflow;
      }

      // Calculate new position with constraints
      // This ensures the image edges never go inside the container edges
      const newX = Math.max(minX, Math.min(maxX, position.x + dx / scale));
      const newY = Math.max(minY, Math.min(maxY, position.y + dy / scale));

      setPosition({ x: newX, y: newY });
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  }, [isDragging, dragStart, position, scale]);

  // Handle mouse up to end dragging
  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
    }
  }, [isDragging]);

  // Add keyboard event listeners for CTRL key
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Control') {
        setIsCtrlPressed(true);
      }
    };

    const handleKeyUp = (e) => {
      if (e.key === 'Control') {
        setIsCtrlPressed(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  // Add global mouse event listeners for dragging
  useEffect(() => {
    // Add global mouse event listeners to handle dragging outside the component
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Add window resize handler to adjust position when window is resized
  useEffect(() => {
    // Only add resize listener when zoomed in
    if (scale > 1) {
      const handleResize = () => {
        // Recalculate constraints and adjust position if needed
        if (containerRef.current && wrapperRef.current) {
          const containerRect = containerRef.current.getBoundingClientRect();
          const imageEl = wrapperRef.current.querySelector('.loaded-image');
          const imageRect = imageEl ? imageEl.getBoundingClientRect() : wrapperRef.current.getBoundingClientRect();

          // Calculate the scaled dimensions
          const scaledWidth = imageRect.width * scale;
          const scaledHeight = imageRect.height * scale;

          // Calculate the current position
          const currentX = position.x;
          const currentY = position.y;

          // Calculate the boundaries to ensure the image always fills the container
          let minX = 0;
          let maxX = 0;
          let minY = 0;
          let maxY = 0;

          // If the scaled image is wider than the container, calculate horizontal constraints
          if (scaledWidth > containerRect.width) {
            const overflow = (scaledWidth - containerRect.width) / (2 * scale);
            minX = -overflow;
            maxX = overflow;
          }

          // If the scaled image is taller than the container, calculate vertical constraints
          if (scaledHeight > containerRect.height) {
            const overflow = (scaledHeight - containerRect.height) / (2 * scale);
            minY = -overflow;
            maxY = overflow;
          }

          // Adjust position if needed to prevent empty space
          const adjustedX = Math.max(minX, Math.min(maxX, currentX));
          const adjustedY = Math.max(minY, Math.min(maxY, currentY));

          if (adjustedX !== currentX || adjustedY !== currentY) {
            setPosition({ x: adjustedX, y: adjustedY });
          }
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [scale, position]);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      if (indicatorTimeoutRef.current) {
        clearTimeout(indicatorTimeoutRef.current);
      }
      if (dragIndicatorTimeoutRef.current) {
        clearTimeout(dragIndicatorTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      className={`zoomable-image-container ${isCtrlPressed ? 'ctrl-pressed' : ''} ${scale > 1 ? 'can-drag' : ''} ${isDragging ? 'dragging' : ''}`}
      ref={containerRef}
      onWheel={handleWheel}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
      data-instance-id={instanceId}
    >
      <div
        className={`zoomable-image-wrapper ${scale > 1 ? 'zoomed' : ''}`}
        ref={wrapperRef}
        style={{
          transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`,
          transformOrigin: transformOrigin,
          transition: isDragging ? 'none' : 'transform 0.1s ease-out'
        }}
      >
        <AuthenticatedImage srcUrl={srcUrl} alt={alt} />
      </div>

      {/* Zoom indicator */}
      <div className={`zoom-indicator ${isHovering ? 'visible' : ''} ${showZoomIndicator ? 'active' : ''}`}>
        <FaSearchPlus />
        <span className="zoom-text">CTRL + Scroll to zoom</span>
        {scale > 1 && <span className="zoom-tip">(Double-click to reset)</span>}
        {showZoomIndicator && <span className="zoom-level">{Math.round(scale * 100)}%</span>}
      </div>

      {/* Drag indicator - only visible when zoomed in */}
      {scale > 1 && (
        <div className={`drag-indicator ${isHovering ? 'visible' : ''} ${showDragIndicator ? 'active' : ''}`}>
          <FaHandPaper />
          <span className="drag-text">Click and drag to pan</span>
        </div>
      )}

      {/* Reset zoom button - only visible when zoomed in */}
      {scale > 1 && (
        <button
          className="reset-zoom-button"
          onClick={handleDoubleClick}
          title="Reset zoom"
          aria-label="Reset zoom"
        >
          <FaSearchMinus />
        </button>
      )}
    </div>
  );
};

export default ZoomableImage;
