# backend/app/config.py

import os
from dotenv import load_dotenv

# Load variables from /env
load_dotenv(override=True)


class Config:
    # Application & security settings
    SECRET_KEY = os.environ.get("SECRET_KEY")
    LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
    ALLOWED_ORIGINS = os.environ.get("ALLOWED_ORIGINS")

    # Logging config
    LOG_DIR = "logs"
    LOG_FILE_APP = "app.log"
    LOG_FILE_CELERY = "celery.log"
    LOG_MAX_BYTES = 5 * 1024 * 1024  # 5MB
    LOG_BACKUP_COUNT = 3

    # Supabase configuration
    SUPABASE_URL = os.environ.get("SUPABASE_URL")
    SUPABASE_KEY = os.environ.get("SUPABASE_KEY")
    SUPABASE_SERVICE_KEY = os.environ.get("SUPABASE_SERVICE_KEY")

    # SQL Alchemy Database
    SQLALCHEMY_DATABASE_URI = os.environ.get("SQLALCHEMY_DATABASE_URI")
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Email configuration
    OUTLOOK_SMTP_SERVER = os.environ.get("OUTLOOK_SMTP_SERVER")
    SMTP_PORT = os.environ.get("SMTP_PORT")
    SENDER_EMAIL = os.environ.get("SENDER_EMAIL")
    EMAIL_PASSWORD = os.environ.get("EMAIL_PASSWORD")

    # File setup
    UPLOAD_FOLDER = os.environ.get("UPLOAD_FOLDER", "/app/uploads")
    OUTPUT_FOLDER = os.environ.get("OUTPUT_FOLDER", "/app/output")
    MODEL_BASE_PATH = os.environ.get("MODEL_BASE_PATH")
    PRODUCT_CODES_TXT = os.environ.get("PRODUCT_CODES_TXT")
    NN_MODEL_PATH = os.environ.get("NN_MODEL_PATH")
    BERT_MODEL_PATH = os.environ.get("BERT_MODEL_PATH")


required_vars = [
    "SECRET_KEY",
    "ALLOWED_ORIGINS",
    "SUPABASE_URL",
    "SUPABASE_KEY",
    "SUPABASE_SERVICE_KEY",
    "SQLALCHEMY_DATABASE_URI",
    "OUTLOOK_SMTP_SERVER",
    "SMTP_PORT",
    "SENDER_EMAIL",
    "EMAIL_PASSWORD",
]

missing_vars = [var for var in required_vars if not os.environ.get(var)]
if missing_vars:
    raise EnvironmentError(
        f"Missing required environment variables: {', '.join(missing_vars)}"
    )
