// frontend/src/sphere/components/CustomerLookupModal.jsx
import {
    FaCheckCircle,
    FaTimesCircle,
    FaExclamation<PERSON><PERSON>gle,
    Fa<PERSON><PERSON>ner,
    FaSearch,
    FaUser,
    FaEnvelope,
    FaPhone,
    FaBuilding,
    FaMapMarkerAlt,
    FaTag
} from 'react-icons/fa';
import '../styles/CustomerLookupModal.css';

// Helper function to normalize postcodes (lowercase and remove whitespace)
const normalizePostcode = (postcode) => {
    if (!postcode || typeof postcode !== 'string') return '';
    return postcode.toLowerCase().replace(/\s+/g, '');
};

// Helper function to determine postcode match type
const getPostcodeMatchType = (customerPostcode, deliveryPostcode) => {
    if (!customerPostcode || !deliveryPostcode) return 'no-match';

    const normalizedCustomerPostcode = normalizePostcode(customerPostcode);
    const normalizedDeliveryPostcode = normalizePostcode(deliveryPostcode);

    if (normalizedCustomerPostcode === normalizedDeliveryPostcode) {
        return 'exact-match';
    }

    // Check if they match except for the last character
    if (normalizedCustomerPostcode.length > 1 && normalizedDeliveryPostcode.length > 1) {
        const customerWithoutLast = normalizedCustomerPostcode.slice(0, -1);
        const deliveryWithoutLast = normalizedDeliveryPostcode.slice(0, -1);

        if (customerWithoutLast === deliveryWithoutLast) {
            return 'partial-match';
        }
    }

    return 'no-match';
};

const CustomerLookupModal = ({
    isOpen,
    onClose,
    loading,
    customers,
    searchInput,
    onSearch,
    onSelectCustomer,
    error,
    setSearchInput,
    suggestedSearchValues = [],
    deliveryPostcode = ''
}) => {
    if (!isOpen) return null;

    // Handle clicking on a suggested search value
    const handleSuggestedValueClick = (value) => {
        setSearchInput(value);
    };

    // Render loading state
    if (loading) {
        return (
            <div className="customer-lookup-modal-overlay">
                <div className="customer-lookup-modal">
                    <div className="modal-header">
                        <h2>Finding Customer</h2>
                        <button className="close-button" onClick={onClose}>&times;</button>
                    </div>
                    <div className="modal-body loading-state">
                        <FaSpinner className="spinner" />
                        <p>Looking up customer information...</p>
                    </div>
                </div>
            </div>
        );
    }

    // Render error state
    if (error) {
        return (
            <div className="customer-lookup-modal-overlay">
                <div className="customer-lookup-modal">
                    <div className="modal-header">
                        <h2>Customer Lookup Failed</h2>
                        <button className="close-button" onClick={onClose}>&times;</button>
                    </div>
                    <div className="modal-body error-state">
                        <FaTimesCircle className="error-icon" />
                        <p>{error}</p>
                        <p className="error-hint">Please try again with a different phone number or email.</p>
                    </div>
                </div>
            </div>
        );
    }

    // Render search form and results
    return (
        <div className="customer-lookup-modal-overlay">
            <div className="customer-lookup-modal search-results-modal">
                <div className="modal-header">
                    <h2><FaUser /> Customer Lookup</h2>
                    <button className="close-button" onClick={onClose}>&times;</button>
                </div>
                <div className="modal-body">
                    <div className="customer-search-form">
                        <p>Enter a phone number, email address, or postcode to find existing customers:</p>
                        <div className="search-input-container">
                            <input
                                type="text"
                                value={searchInput}
                                onChange={(e) => setSearchInput(e.target.value)}
                                placeholder="Phone number, email address, or postcode"
                                className="customer-search-input"
                            />
                            <button
                                className="search-button"
                                onClick={onSearch}
                                disabled={!searchInput.trim()}
                            >
                                <FaSearch /> Search
                            </button>
                        </div>

                        {/* Suggested search values */}
                        {suggestedSearchValues.length > 0 && (
                            <div className="suggested-search-values">
                                <p className="suggested-values-label">
                                    <FaTag size={12} /> Suggested values from form:
                                </p>
                                <div className="suggested-values-container">
                                    {suggestedSearchValues.map((value, index) => (
                                        <button
                                            key={index}
                                            className="suggested-value-button"
                                            onClick={() => handleSuggestedValueClick(value)}
                                            title={`Search using ${value}`}
                                        >
                                            {value}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>

                    {customers && customers.length > 0 ? (
                        <>
                            <p className="results-count">Found <strong>{customers.length}</strong> matching customers:</p>
                            <div className="search-results-container">
                                {customers.map((customer, index) => (
                                    <div key={index} className="search-result-item">
                                        <div className="customer-result-details">
                                            <div className="customer-result-header">
                                                <div className="customer-result-name">
                                                    <FaUser size={14} /> {customer.Contact || 'N/A'}
                                                </div>
                                                <div className="customer-result-company">
                                                    <FaBuilding size={14} /> {customer.Company || 'N/A'}
                                                </div>
                                            </div>
                                            <div className="customer-result-contact-info">
                                                <div className="customer-result-email" title={customer.Email || 'N/A'}>
                                                    <FaEnvelope size={12} /> {customer.Email || 'N/A'}
                                                </div>
                                                <div className="customer-result-phone">
                                                    <FaPhone size={12} /> {customer.MobileTel || customer.HomeTel || customer.WorkTel || 'N/A'}
                                                </div>
                                            </div>
                                            <div className="customer-result-address">
                                                <FaMapMarkerAlt size={12} />
                                                {[
                                                    customer['Address 1'],
                                                    customer['Address 2'],
                                                    customer['Address 3'],
                                                    customer.City,
                                                    customer.County,
                                                ].filter(Boolean).join(', ')}
                                                {customer.PostCode && (
                                                    <span className={`customer-postcode ${getPostcodeMatchType(customer.PostCode, deliveryPostcode)}`}>
                                                        {customer.PostCode && `${customer.PostCode}`}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="search-result-action">
                                            <button
                                                className="select-customer-button"
                                                onClick={() => onSelectCustomer(customer)}
                                            >
                                                <FaCheckCircle size={14} style={{ marginRight: '6px' }} /> Select
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    ) : customers && customers.length === 0 ? (
                        <div className="no-results">
                            <FaExclamationTriangle className="warning-icon" />
                            <p>No customers found matching your search criteria.</p>
                            <p className="error-hint">Try a different phone number or email address.</p>
                        </div>
                    ) : null}
                </div>
            </div>
        </div>
    );
};

export default CustomerLookupModal;
