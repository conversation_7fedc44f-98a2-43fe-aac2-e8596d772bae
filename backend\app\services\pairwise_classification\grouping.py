# backend/app/services/pairwise_classification/grouping.py

import networkx as nx


def build_graph(predictions):
    """
    Build a graph from the predictions where nodes are result IDs and
    edges are added between nodes if the prediction is True.
    """
    G = nx.Graph()

    # Add edges for each "True" prediction
    for result_id1, result_id2, predicted_same_group in predictions:
        if predicted_same_group:  # Only add an edge if predicted as the same group
            G.add_edge(result_id1, result_id2)

    return G


def find_clusters_with_index(G):
    """
    Find connected components in the graph and return a dictionary where
    each result_id is mapped to its cluster index.
    """
    clusters = list(
        nx.connected_components(G)
    )  # Each component is a group of linked IDs
    cluster_dict = {}

    # Assign each result_id a cluster index
    for idx, cluster in enumerate(clusters, start=1):
        for result_id in cluster:
            cluster_dict[result_id] = idx

    return cluster_dict
