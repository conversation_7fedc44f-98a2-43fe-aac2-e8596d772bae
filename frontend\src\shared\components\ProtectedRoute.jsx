// frontend/src/shared/components/ProtectedRoute.jsx

import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ requiredRoles }) => {
    // Get user, userProfile and the combined loading state from context
    const { user, userProfile, loading } = useAuth();

    // Check if authentication or profile data is still loading
    if (loading) {
        return <div className="loading">Loading authentication...</div>;
    }

    // Check if the user is logged in and has a user profile
    if (!user || !userProfile) {
        return <Navigate to="/login" replace />;
    }

    // Check if the user profile has one of the required roles
    if (!requiredRoles.includes(userProfile.role)) {
        return <Navigate to="/unauthorized" replace />;
    }

    return <Outlet />;
};

export default ProtectedRoute;
