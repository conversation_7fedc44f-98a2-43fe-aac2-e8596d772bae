networks:
  shared_network:
    name: internal_app_network
    external: true # Indicates the netwrok is created outside this compose file

services:
  # Flask backend application
  orderprocessing:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    volumes:
      - output:/app/output
      - uploads:/app/uploads
      - ./backend/app:/app/app # This mounts the entire backend/app directory
      # Uncomment the line below for production and comment out the line above
      # - ./backend/app/ml_models:/app/app/ml_models:ro # Read only
      # - ./backend/app/paddle_models:/app/app/paddle_models:ro # Read only
    ports:
      - "5001:5001"
    env_file:
      - .env
    environment:
      # Comment out the line below for production
      - FLASK_ENV=development
      - SECRET_KEY=${SECRET_KEY}
      - LOG_LEVEL=${LOG_LEVEL}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SQLALCHEMY_DATABASE_URI=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - OUTLOOK_SMTP_SERVER=${OUTLOOK_SMTP_SERVER}
      - SMTP_PORT=${SMTP_PORT}
      - SENDER_EMAIL=${SENDER_EMAIL}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - UPLOAD_FOLDER=/app/uploads
      - OUTPUT_FOLDER=/app/output
      - MODEL_BASE_PATH=${MODEL_BASE_PATH}
      - PRODUCT_CODES_TXT=${PRODUCT_CODES_TXT}
      - NN_MODEL_PATH=${NN_MODEL_PATH}
      - BERT_MODEL_PATH=${BERT_MODEL_PATH}
      - PADDLEOCR_HOME=${PADDLEOCR_HOME}

    depends_on:
      - db
    networks:
      - shared_network

  db:
    build:
      context: ./database
      dockerfile: Dockerfile
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - shared_network

volumes:
  output:
  uploads:
  db_data:
