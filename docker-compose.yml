networks:
  shared_network:
    name: internal_app_network
    external: true # Indicates this network is created outside this compose file

services:
  # React Frontend served by Nginx
  frontend:
    build:
      context: ./frontend # Tells <PERSON><PERSON> to look for <PERSON><PERSON><PERSON>le in ./frontend
      args:
        # Pass build-time args for React env vars
        REACT_APP_SUPABASE_URL: ${REACT_APP_SUPABASE_URL}
        REACT_APP_SUPABASE_ANON_KEY: ${REACT_APP_SUPABASE_ANON_KEY}
    restart: always
    ports:
      - "9000:9000"
    networks:
      - shared_network # Connect this service to the shared network
