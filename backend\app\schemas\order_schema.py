# app/schemas/order_schema.py

from marshmallow import Schema, fields, pre_load


def empty_str_to_none(data):
    """Convert empty strings to None."""
    for key, value in data.items():
        if isinstance(value, str) and value.strip() == "":
            data[key] = None
    return data


class AddressSchema(Schema):
    # All fields allow None
    delivery_organisation_name = fields.String(allow_none=False)
    delivery_house = fields.String(allow_none=True)
    delivery_street = fields.String(allow_none=False)
    delivery_locality = fields.String(allow_none=True)
    delivery_town = fields.String(allow_none=True)
    delivery_county = fields.String(allow_none=False)
    delivery_postcode = fields.String(allow_none=False)
    delivery_country = fields.String(allow_none=False)

    # invoice_organisation_name = fields.String(allow_none=False)
    # invoice_house = fields.String(allow_none=True)
    # invoice_street = fields.String(allow_none=False)
    # invoice_locality = fields.String(allow_none=True)
    # invoice_town = fields.String(allow_none=True)
    # invoice_county = fields.String(allow_none=False)
    # invoice_postcode = fields.String(allow_none=False)
    # invoice_country = fields.String(allow_none=False)

    @pre_load
    def convert_empty_to_none(self, data, **kwargs):
        return empty_str_to_none(data)


class ProductSchema(Schema):
    # Changed required=False to allow_none=True for consistency
    Product_Code = fields.String(allow_none=False)
    Product_Name = fields.String(allow_none=True)
    Product_Qty = fields.Integer(allow_none=False)
    Unit_Cost = fields.Float(allow_none=False)

    @pre_load
    def convert_empty_to_none(self, data, **kwargs):
        return empty_str_to_none(data)


class OrderSchema(Schema):
    address_code = fields.String(allow_none=False)
    po_number = fields.String(allow_none=False)
    part_shipment = fields.String(allow_none=True)
    channel = fields.String(allow_none=True)
    lea = fields.String(allow_none=True)
    delivery_instructions = fields.String(allow_none=True)
    # contact_name = fields.String(allow_none=True)
    contact_phone = fields.String(allow_none=True)
    contact_email = fields.String(allow_none=False)

    # invoice_email = fields.String(allow_none=True)
    # invoice_phone = fields.String(allow_none=True)
    # total_value = fields.Float(allow_none=True)

    # Made required=False and allow_none=True
    addresses = fields.Dict(required=False, allow_none=True)
    products = fields.List(fields.Nested(ProductSchema), allow_none=True)

    @pre_load
    def convert_empty_to_none(self, data, **kwargs):
        # Convert top-level empty strings to None
        return empty_str_to_none(data)
