/* frontend/src/sphere/styles/ProductVerificationModal.css */

.product-verification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
  transition: all 0.2s ease-in-out;
}

.product-verification-modal {
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
  border: 1px solid var(--border-color-light);
}

.search-results-modal {
  max-width: 850px; /* Increased from 700px to provide more space for product names */
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
    scale: 0.98;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    scale: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px;
  border-bottom: 1px solid var(--border-color-light);
  background-color: var(--card-background);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-color);
  font-weight: 600;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--button-secondary-bg);
  border: none;
  width: 28px;
  height: 28px;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: var(--text-color);
  background-color: var(--button-secondary-hover-bg);
}

.modal-body {
  padding: 24px;
  color: var(--text-color);
}

.modal-body p {
  margin-top: 0;
  margin-bottom: 16px;
  line-height: 1.5;
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.spinner {
  animation: spin 1s linear infinite;
  font-size: 2rem;
  color: var(--button-primary-bg);
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error state */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
}

.error-icon {
  font-size: 2.5rem;
  color: var(--message-error-text);
  margin-bottom: 16px;
}

.error-hint {
  margin-top: 8px;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-style: italic;
  max-width: 90%;
}

.error-actions {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  border-top: 1px solid var(--border-color-light);
  padding-top: 16px;
}

.search-suggestion {
  margin-bottom: 12px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.search-by-name-button {
  padding: 10px 18px;
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.search-by-name-button:hover {
  background-color: var(--button-secondary-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* Verification results */
.verification-summary {
  margin-bottom: 24px;
  text-align: center;
}

.verification-success, .verification-warning {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  font-size: 2.5rem;
  color: var(--message-success-text);
  margin-bottom: 8px;
}

.warning-icon {
  font-size: 2.5rem;
  color: var(--button-tertiary-warning-text);
  margin-bottom: 8px;
}

.verification-details {
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.verification-row {
  display: grid;
  grid-template-columns: 80px 1fr 1fr 40px;
  padding: 14px 18px;
  align-items: center;
}

.verification-row:not(:last-child) {
  border-bottom: 1px solid var(--border-color-light);
}

.verification-row:nth-child(odd) {
  background-color: var(--table-row-hover);
}

.field-label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.field-value {
  padding: 0 12px;
  word-break: break-word;
}

.field-value.original {
  color: var(--text-secondary);
}

.field-value.fetched {
  font-weight: 500;
}

.field-status {
  display: flex;
  justify-content: center;
  align-items: center;
}

.match-icon {
  color: var(--message-success-text);
  font-size: 1.2rem;
}

.mismatch-icon {
  color: var(--message-error-text);
  font-size: 1.2rem;
}

.verification-actions {
  display: flex;
  justify-content: center;
}

.select-product-button {
  padding: 10px 20px;
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-product-button:hover {
  background-color: var(--button-primary-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Original product details */
.original-product-summary {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color-light);
}

.original-product-summary h3 {
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--text-color);
  font-weight: 600;
}

.original-product-details {
  background-color: var(--table-row-hover);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 16px;
  display: grid;
  gap: 12px;
}

.original-detail {
  display: flex;
  align-items: center;
}

.detail-label {
  font-weight: 600;
  color: var(--text-secondary);
  width: 80px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
}

.detail-value {
  font-weight: 500;
  color: var(--text-color);
}

/* Search results */
.search-results-container {
  max-height: 400px;
  overflow-y: auto;
  margin-top: 20px;
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: var(--border-color) transparent; /* For Firefox */
}

/* Custom scrollbar for WebKit browsers */
.search-results-container::-webkit-scrollbar {
  width: 8px;
}

.search-results-container::-webkit-scrollbar-track {
  background: transparent;
}

.search-results-container::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
  border: 2px solid transparent;
}

.search-result-item {
  display: grid;
  grid-template-columns: minmax(0, 1fr) auto; /* Added minmax to ensure the first column can shrink */
  align-items: center;
  padding: 14px 18px;
  border-bottom: 1px solid var(--border-color-light);
  transition: background-color 0.15s ease;
  gap: 16px;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: var(--table-row-hover);
}

.search-result-details {
  display: grid;
  grid-template-columns: 110px minmax(0, 2fr) 100px; /* Changed from 1fr to 2fr to allocate more space for product names */
  gap: 16px;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.search-result-code {
  font-weight: 600;
  color: var(--text-color);
  background-color: var(--button-secondary-bg);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  display: inline-block;
}

/* For standard screens, keep single line with ellipsis */
.search-result-name {
  color: var(--text-color);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%; /* Ensure the text stays within its container */
  padding-right: 8px; /* Add some padding to prevent text from touching the edge */
}

/* For very long product names, show tooltip on hover */
.search-result-name:hover {
  cursor: help;
}

/* For smaller screens, allow wrapping to two lines */
@media (max-width: 768px) {
  .search-result-name {
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* Show max 2 lines */
    -webkit-box-orient: vertical;
    line-height: 1.3;
    height: 2.6em; /* Approximately 2 lines */
  }
}

.search-result-price {
  text-align: right;
  color: var(--text-color);
  font-weight: 600;
  font-size: 1.05rem;
}

.search-result-action {
  min-width: 100px;
  display: flex;
  justify-content: flex-end;
}
