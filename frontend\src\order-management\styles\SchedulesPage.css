/* frontend/src/order-management/styles/SchedulesPage.css */

/* --- Base Layout --- */
.schedules-container {
  padding: 20px 0;
}

.schedules-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;  /* ensure header items sit apart */
}

.schedules-header h1 {
  color: var(--text-color);
  font-size: 1.8rem;
}

.refresh-button {
  margin-left: 12px;  /* space between title and icon */
}

/* --- Card Styles --- */
.filters-card,
.schedules-card {
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
  margin-bottom: 20px;
  transition: background-color 0.3s ease;
}

.card-header {
  background-color: var(--table-header-bg); /* Uses theme variable */
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-color); /* Uses theme variable */
}

.card-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-color); /* Uses theme variable */
  font-weight: 600;
}

/* --- Filters Form --- */
.filters-form {
  display: flex;
  gap: 20px;
  padding: 20px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.filters-form .form-group {
  margin-bottom: 0;
  min-width: 180px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filters-form .form-group label {
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: var(--text-secondary); /* Uses theme variable */
  font-weight: 500;
}

.filters-form select {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color); /* Uses theme variable */
  border-radius: 4px;
  background-color: var(--input-background); /* Uses theme variable */
  color: var(--text-color); /* Uses theme variable */
  font-size: 1rem;
  height: 42px; /* Match button height */
}

.filters-form button {
  padding: 10px 16px;
  margin-bottom: 0;
  height: 42px;
  white-space: nowrap;
}

/* --- Loading & Empty States --- */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-secondary); /* Uses theme variable */
}

.spinner {
  animation: spin 1s linear infinite;
  font-size: 2rem;
  margin-bottom: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.empty-container {
  padding: 40px;
  text-align: center;
  color: var(--text-secondary); /* Uses theme variable */
}

/* --- Table Styles --- */
.table-container {
  overflow-x: auto;
}

.schedules-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
  min-width: 900px; /* Adjust as needed based on content */
}

.schedules-table th,
.schedules-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  white-space: nowrap;
  vertical-align: middle;
}

.schedules-table th {
  background-color: var(--table-header-bg);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

.schedules-table tr:hover {
  background-color: var(--table-row-hover);
}

/* --- Status Badges (Now only for Eligibility) --- */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  min-width: 65px;
  line-height: 1.4;
}

.status-badge.active {
  background-color: var(--message-success-bg);
  color: var(--message-success-text);
}

.status-badge.inactive {
  background-color: var(--message-error-bg);
  color: var(--message-error-text);
}

/* --- Styles for the Combined "Last Run" Cell when Failed --- */
.last-run-failed-cell {
  display: flex; /* Align "Failed" text and icon */
  align-items: center;
  gap: 6px; /* Space between text and icon */
}

.failed-text {
  color: var(--message-error-text); /* Use error text color */
  font-weight: 600; /* Make it stand out slightly */
}

/* --- Action Buttons & Icons --- */
.actions {
  display: flex;
  gap: 8px;
}

.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 4px;
  color: var(--text-color);
  transition:
    background-color 0.2s,
    color 0.2s;
}

.icon-button:hover {
  background-color: var(--button-secondary-bg);
}

.edit-button {
  color: var(--text-secondary);
}

.edit-button:hover {
  color: var(--text-color);
}

/* Info icon button (used in last-run-failed-cell) */
.info-icon {
  color: var(--text-secondary);
  font-size: 1rem;
  padding: 2px;
  /* margin-left removed as gap is used in flex container */
  line-height: 1;
}

.info-icon:hover {
  color: var(--text-color);
  background-color: transparent;
}

/* --- Pagination --- */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 20px;
  border-top: 1px solid var(--border-color);
}

.pagination-button {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--button-secondary-hover-bg);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* --- Basic Button Styles (Example) --- */
.button {
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition:
    background-color 0.2s,
    opacity 0.2s;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.secondary-button {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--border-color);
}

.secondary-button:hover:not(:disabled) {
  background-color: var(--button-secondary-hover-bg);
}

/* --- Modal Styles --- */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  opacity: 0;
  animation: fadeIn 0.2s forwards;
}

.modal-content {
  background-color: var(--card-background);
  padding: 25px 30px;
  border-radius: 8px;
  box-shadow: 0 5px 20px var(--shadow-color);
  z-index: 1001;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  color: var(--text-color);
  position: relative;
  transform: scale(0.95);
  opacity: 0;
  animation: scaleIn 0.2s 0.1s forwards;
}

.modal-content > * {
  pointer-events: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.4rem;
  color: var(--text-color);
  font-weight: 600;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 2rem;
  font-weight: bold;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0 5px;
  line-height: 1;
  transition: color 0.2s ease;
}

.modal-close-button:hover {
  color: var(--text-color);
}

.modal-body {
  /* No specific styles needed if using <pre> */
}

/* Style the <pre> tag used for modal content */
.modal-error-message {
  margin: 0;
  line-height: 1.6;
  font-size: 0.95rem; /* Slightly smaller for potentially long text */
  font-family: inherit; /* Use the standard page font */
  white-space: pre-wrap; /* Crucial for line breaks */
  word-wrap: break-word; /* Crucial for long lines */
  color: var(--text-color); /* Ensure text color matches theme */
}

/* --- Animation Keyframes --- */
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}