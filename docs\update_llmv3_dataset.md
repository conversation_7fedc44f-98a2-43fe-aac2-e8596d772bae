# Update LLMv3 Dataset Script

This script updates JSON files with additional annotations for key phrases, phone numbers, email addresses, product codes, and quantities. It reads the JSON files, processes the annotations, and saves the updated JSON files.

## Dependencies

- `os`
- `json`
- `logging`
- `re`
- `uuid`

## Class: JSONUpdater

### __init__(self, product_codes_file)
Initialises the `JSONUpdater` class, loading product codes and defining key phrases.

- **Parameters**:
  - `product_codes_file` (str): Path to the file containing product codes.

### load_product_codes(self, product_codes_file)
Loads product codes from a specified file.

- **Parameters**:
  - `product_codes_file` (str): Path to the file containing product codes.

- **Returns**:
  - `product_codes` (set): Set of product codes.

### load_json(self, json_file)
Loads a JSON file.

- **Parameters**:
  - `json_file` (str): Path to the JSON file.

- **Returns**:
  - `data` (dict): JSON data.

### save_json(self, data, json_file)
Saves data to a JSON file.

- **Parameters**:
  - `data` (dict): JSON data to be saved.
  - `json_file` (str): Path to the JSON file.

### create_bbox(self, part, width_ratio, x_start, bbox, image_width, image_height)
Creates a bounding box for a text part.

- **Parameters**:
  - `part` (str): Text part.
  - `width_ratio` (float): Width ratio of the text part.
  - `x_start` (float): Starting x-coordinate.
  - `bbox` (dict): Original bounding box.
  - `image_width` (int): Width of the image.
  - `image_height` (int): Height of the image.

- **Returns**:
  - `part` (str): Text part.
  - `new_bbox` (dict): New bounding box.
  - `x_start` (float): Updated starting x-coordinate.

### split_text_with_pattern(self, pattern, text)
Splits text using a specified pattern.

- **Parameters**:
  - `pattern` (str): Regular expression pattern.
  - `text` (str): Text to be split.

- **Returns**:
  - `pre_text` (str): Text before the pattern.
  - `matched_text` (str): Matched pattern.
  - `post_text` (str): Text after the pattern.

### process_text_annotation(self, bbox_annotation, text_annotation, image_width, image_height)
Processes text and bounding box annotations.

- **Parameters**:
  - `bbox_annotation` (dict): Bounding box annotation.
  - `text_annotation` (dict): Text annotation.
  - `image_width` (int): Width of the image.
  - `image_height` (int): Height of the image.

- **Returns**:
  - `text` (str): Text from the annotation.
  - `bbox` (dict): Bounding box from the annotation.

### split_text_and_boxes(self, text, keyword, bbox, image_width, image_height)
Splits text and bounding boxes based on a keyword.

- **Parameters**:
  - `text` (str): Text to be split.
  - `keyword` (str): Keyword to split the text.
  - `bbox` (dict): Original bounding box.
  - `image_width` (int): Width of the image.
  - `image_height` (int): Height of the image.

- **Returns**:
  - `new_boxes` (list): List of tuples containing text parts and new bounding boxes.

### update_json_for_emails(self, data)
Updates JSON data with annotations for email addresses.

- **Parameters**:
  - `data` (dict): JSON data.

- **Returns**:
  - `data` (dict): Updated JSON data.

### update_json_for_phones(self, data)
Updates JSON data with annotations for phone numbers.

- **Parameters**:
  - `data` (dict): JSON data.

- **Returns**:
  - `data` (dict): Updated JSON data.

### check_for_product_code(self, text)
Checks if a text contains a product code.

- **Parameters**:
  - `text` (str): Text to be checked.

- **Returns**:
  - `product_code` (str): Product code if found, otherwise `None`.

### update_json_for_product_codes(self, data)
Updates JSON data with annotations for product codes.

- **Parameters**:
  - `data` (dict): JSON data.

- **Returns**:
  - `data` (dict): Updated JSON data.

### update_json_for_key_phrases(self, data)
Updates JSON data with annotations for key phrases.

- **Parameters**:
  - `data` (dict): JSON data.

- **Returns**:
  - `data` (dict): Updated JSON data.

### update_json_for_quantities_ea(self, data)
Updates JSON data with annotations for quantities in EA (each).

- **Parameters**:
  - `data` (dict): JSON data.

- **Returns**:
  - `data` (dict): Updated JSON data.

### update_json_for_quantities_x(self, data)
Updates JSON data with annotations for quantities indicated by 'x'.

- **Parameters**:
  - `data` (dict): JSON data.

- **Returns**:
  - `data` (dict): Updated JSON data.

### update_json(self, input_json_file_name=None, input_json=None, output_json_file_name=None, save=True)
Updates a JSON file with additional annotations.

- **Parameters**:
  - `input_json_file_name` (str): Path to the input JSON file.
  - `input_json` (dict): Input JSON data.
  - `output_json_file_name` (str): Path to the output JSON file.
  - `save` (bool): Whether to save the updated JSON data. Default is `True`.

### process_all_json_files(self, input_folder_path, output_folder_path)
Processes all JSON files in a specified folder.

- **Parameters**:
  - `input_folder_path` (str): Path to the folder containing input JSON files.
  - `output_folder_path` (str): Path to the folder where updated JSON files will be saved.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the paths for the product codes file, input folder, and output folder.
2. Initialises the `JSONUpdater` class with the product codes file.
3. Processes all JSON files in the specified input folder, updating them with additional annotations, and saves the updated JSON files in the specified output folder.

## Usage

Run the script using Python:

```bash
python update_llmv3_dataset.py
```

## Logging

The script uses the `logging` module to log information about the JSON update process, including progress and any errors encountered.

## Error Handling

Errors encountered during the JSON update process are logged using the `logging` module, allowing for easier debugging and tracking of issues.
