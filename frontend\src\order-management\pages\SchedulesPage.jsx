// frontend/src/order-management/pages/SchedulesPage.jsx

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import { Fa<PERSON><PERSON>ner, FaEdit, FaInfoCircle, FaSync } from 'react-icons/fa';
import { getSchedules, syncSchedules } from '../services/scheduleService';
import '../styles/SchedulesPage.css';

const SchedulesPage = () => {
    const [schedules, setSchedules] = useState([]);
    const [loading, setLoading] = useState(true);
    const [syncing, setSyncing] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalContent, setModalContent] = useState('');
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 20,
        totalItems: 0,
        totalPages: 0
    });
    const [filters, setFilters] = useState({
        is_active: null,
        frequency: ''
    });

    const fetchSchedules = async () => {
        setLoading(true);
        try {
            const response = await getSchedules(
                pagination.page,
                pagination.pageSize,
                filters
            );
            setSchedules(response.data.schedules);
            setPagination(response.data.pagination);
        } catch (err) {
            const errorMsg = err.message || 'Failed to load schedules';
            toast.error(errorMsg);
            console.error('Error fetching schedules:', err);
            // Clear schedules and pagination on error
            setSchedules([]);
            setPagination({ page: 1, pageSize: 10, totalItems: 0, totalPages: 0 });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchSchedules();
    }, [pagination.page, pagination.pageSize, filters]);

    const handlePageChange = (newPage) => {
        setPagination({
            ...pagination,
            page: newPage
        });
    };

    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters({
            ...filters,
            [name]: value === '' ? null : value
        });
        // Reset to page 1 when filters change
        setPagination({
            ...pagination,
            page: 1
        });
    };

    const formatFrequency = (frequency) => {
        return frequency.charAt(0).toUpperCase() + frequency.slice(1);
    };

    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return '-';
        const date = new Date(dateTimeString);
        // Format as dd/mm/yyyy hh:mm
        return date.toLocaleString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    };

    const openErrorModal = (runTime, errorMessage) => {
        // Ensure we have something to show
        if (!runTime && !errorMessage) return;

        const formattedTime = runTime ? formatDateTime(runTime) : 'N/A';
        const errorText = errorMessage || 'No error details provided.';
        // Truncate error if needed
        const truncatedError =
            errorText.length > 500 ? errorText.substring(0, 500) + '...' : errorText;

        // Combine into a single string with clear labels and line breaks
        const combinedMessage = `Last Attempted Run: ${formattedTime}\n\nError:\n${truncatedError}`;

        setModalContent(combinedMessage); // Set the combined message
        setIsModalOpen(true);
    };

    const closeErrorModal = () => {
        setIsModalOpen(false);
        setModalContent(''); // Clear message on close
    };

    const handleRefresh = async () => {
        setSyncing(true);
        try {
            await syncSchedules();
            toast.success('Sync started. Refresh page to see updated status. This may take a moment.');
        } catch (err) {
            console.error('Error starting sync:', err);
            toast.error(`Failed to start sync: ${err.message}`);
        } finally {
            setSyncing(false);
        }
    };

    return (
        <div className="schedules-container">
            <div className="schedules-header">
                <h1>Report Schedules</h1>
                <button
                    className="icon-button refresh-button"
                    onClick={handleRefresh}
                    title="Start sync"
                    disabled={syncing}
                >
                    {syncing ? <FaSpinner className="spinner" /> : <FaSync />}
                </button>
            </div>

            <div className="filters-card">
                <div className="card-header">
                    <h2>Filters</h2>
                </div>
                <div className="filters-form">
                    <div className="form-group">
                        <label htmlFor="is_active">Status</label>
                        <select
                            id="is_active"
                            name="is_active"
                            value={filters.is_active === null ? '' : filters.is_active}
                            onChange={handleFilterChange}
                        >
                            <option value="">All</option>
                            <option value="true">Active</option>
                            <option value="false">Inactive</option>
                        </select>
                    </div>

                    <div className="form-group">
                        <label htmlFor="frequency">Frequency</label>
                        <select
                            id="frequency"
                            name="frequency"
                            value={filters.frequency || ''}
                            onChange={handleFilterChange}
                        >
                            <option value="">All</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="biweekly">Biweekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>

                    <button
                        className="button secondary-button"
                        onClick={() => {
                            setFilters({
                                is_active: null,
                                frequency: ''
                            });
                        }}
                    >
                        Clear Filters
                    </button>
                </div>
            </div>

            <div className="schedules-card">
                <div className="card-header">
                    <h2>Schedules List</h2>
                </div>
                {loading ? (
                    <div className="loading-container">
                        <FaSpinner className="spinner" />
                        <p>Loading schedules...</p>
                    </div>
                ) : schedules.length === 0 ? (
                    <div className="empty-container">
                        <p>No schedules found.</p>
                    </div>
                ) : (
                    <>
                        <div className="table-container">
                            <table className="schedules-table">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Report Name</th>
                                        <th>Frequency</th>
                                        <th>Next Run</th>
                                        <th>Last Run</th>
                                        <th>Eligibility</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {schedules.map((schedule) => {
                                        const {
                                            id,
                                            customers,
                                            report_name,
                                            frequency,
                                            next_run_at,
                                            last_run_at,
                                            last_run_status,
                                            last_run_error,
                                        } = schedule;
                                        const customerName = customers?.name || 'Unknown';
                                        const isEligible = customers?.is_report_eligible ?? false;

                                        // Determine if the last run failed or was skipped
                                        const lastRunFailed =
                                            last_run_status?.toLowerCase() === 'failed' ||
                                            last_run_status?.toLowerCase() === 'skipped';

                                        // Determine if the info icon should be shown (only if failed/skipped)
                                        const showInfoIcon = lastRunFailed;

                                        return (
                                            <tr key={id}>
                                                {/* --- Data Cells - Ensure Order Matches Headers --- */}
                                                <td>{customerName}</td>
                                                <td>{report_name}</td>
                                                <td>{formatFrequency(frequency)}</td>
                                                <td>{formatDateTime(next_run_at)}</td>

                                                <td>
                                                    {lastRunFailed ? (
                                                        // Display "Failed" text and icon
                                                        <span className="last-run-failed-cell">
                                                            <span className="failed-text">Failed</span>
                                                            <button
                                                                className="icon-button info-icon"
                                                                onClick={() =>
                                                                    openErrorModal(last_run_at, last_run_error)
                                                                } // Pass time and error
                                                                title="Show last run details"
                                                            >
                                                                <FaInfoCircle />
                                                            </button>
                                                        </span>
                                                    ) : (
                                                        formatDateTime(last_run_at)
                                                    )}
                                                </td>

                                                <td>
                                                    <span
                                                        className={`status-badge ${isEligible ? 'active' : 'inactive'
                                                            }`}
                                                    >
                                                        {isEligible ? 'Active' : 'Inactive'}
                                                    </span>
                                                </td>

                                                <td className="actions">
                                                    <Link
                                                        to={`/schedules/${id}`}
                                                        className="icon-button edit-button"
                                                        title="Edit Schedule"
                                                    >
                                                        <FaEdit />
                                                    </Link>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>

                        <div className="pagination">
                            <button
                                className="pagination-button"
                                disabled={pagination.page <= 1}
                                onClick={() => handlePageChange(pagination.page - 1)}
                            >
                                Previous
                            </button>
                            <span className="pagination-info">
                                Page {pagination.page} of {pagination.totalPages}
                            </span>
                            <button
                                className="pagination-button"
                                disabled={pagination.page >= pagination.totalPages}
                                onClick={() => handlePageChange(pagination.page + 1)}
                            >
                                Next
                            </button>
                        </div>
                    </>
                )}
            </div>

            {/* --- Modal Structure --- */}
            {isModalOpen && (
                <div className="modal-overlay" onClick={closeErrorModal}>
                    <div
                        className="modal-content"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="modal-header">
                            {/* Updated Modal Title */}
                            <h2>Last Run Details</h2>
                            <button className="modal-close-button" onClick={closeErrorModal}>
                                &times;
                            </button>
                        </div>
                        <div className="modal-body">
                            {/* Use <pre> to respect line breaks from modalContent */}
                            <pre className="modal-error-message">{modalContent}</pre>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SchedulesPage;
