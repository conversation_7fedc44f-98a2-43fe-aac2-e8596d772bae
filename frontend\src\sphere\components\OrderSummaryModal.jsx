// frontend/src/sphere/components/OrderSummaryModal.jsx
import { FaTimes, FaCheckCircle } from 'react-icons/fa';
import '../styles/OrderSummaryModal.css';

const OrderSummaryModal = ({
    isOpen,
    onClose,
    formData,
    selectedCustomer
}) => {
    if (!isOpen || !formData) return null;

    // Extract delivery address fields
    const deliveryAddressFields = Object.entries(formData)
        .filter(([key]) => key.startsWith('delivery_'))
        .reduce((acc, [key, value]) => {
            // Extract the field name after 'delivery_'
            const fieldName = key.replace('delivery_', '');
            acc[fieldName] = value;
            return acc;
        }, {});

    // Create an array of address lines for display
    const addressLines = [];
    if (deliveryAddressFields.name) addressLines.push(deliveryAddressFields.name);
    if (deliveryAddressFields.addressLine1) addressLines.push(deliveryAddressFields.addressLine1);
    if (deliveryAddressFields.addressLine2) addressLines.push(deliveryAddressFields.addressLine2);
    if (deliveryAddressFields.addressLine3) addressLines.push(deliveryAddressFields.addressLine3);
    if (deliveryAddressFields.addressLine4) addressLines.push(deliveryAddressFields.addressLine4);
    if (deliveryAddressFields.addressLine5) addressLines.push(deliveryAddressFields.addressLine5);
    if (deliveryAddressFields.postcode) addressLines.push(deliveryAddressFields.postcode);
    if (deliveryAddressFields.country) addressLines.push(deliveryAddressFields.country);

    // Get customer address code if available
    const customerAddressCode = selectedCustomer ? selectedCustomer.AddressCode : 'N/A';

    return (
        <div className="order-summary-modal-overlay" onClick={onClose}>
            <div className="order-summary-modal" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>
                        <FaCheckCircle className="success-icon" />
                        Order Summary
                    </h2>
                    <button className="close-button" onClick={onClose}>
                        <FaTimes />
                    </button>
                </div>
                <div className="modal-body">
                    <div className="summary-section">
                        <h3>Customer Information</h3>
                        <div className="summary-field">
                            <span className="field-label">Address Code:</span>
                            <span className="field-value">{customerAddressCode}</span>
                        </div>
                        {formData.po_number && (
                            <div className="summary-field">
                                <span className="field-label">PO Number:</span>
                                <span className="field-value">{formData.po_number}</span>
                            </div>
                        )}
                        {formData.contact_phone && (
                            <div className="summary-field">
                                <span className="field-label">Telephone:</span>
                                <span className="field-value">{formData.contact_phone}</span>
                            </div>
                        )}
                        {formData.contact_email && (
                            <div className="summary-field">
                                <span className="field-label">Email:</span>
                                <span className="field-value">{formData.contact_email}</span>
                            </div>
                        )}
                    </div>

                    <div className="summary-section">
                        <h3>Delivery Address</h3>
                        <div className="address-lines">
                            {addressLines.map((line, index) => (
                                <div key={index} className="address-line">{line}</div>
                            ))}
                        </div>
                    </div>

                    <div className="summary-section">
                        <h3>Products</h3>
                        <div className="products-table-container">
                            <table className="products-summary-table">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Name</th>
                                        <th>Quantity</th>
                                        <th>Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {formData.products.map((product, index) => (
                                        <tr key={index}>
                                            <td>{product.Product_Code}</td>
                                            <td>{product.Product_Name}</td>
                                            <td>{product.Product_Qty}</td>
                                            <td>{product.Unit_Cost}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div className="modal-footer">
                    <button
                        className="confirm-button"
                        onClick={() => {
                            // Placeholder for actual submission
                            onClose();
                        }}
                    >
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    );
};

export default OrderSummaryModal;
