# backend/app/utils/logger.py

import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler
import os

# Determine the base directory (backend folder)
BASE_DIR = Path(__file__).resolve().parent.parent.parent


def setup_logging(
    log_level="INFO",
    log_dir="logs",
    log_file_name="app.log",
    max_bytes=5 * 1024 * 1024,
    backup_count=3,
    logger_name=None,  # If None, configures the root logger
):
    """
    Configures logging with console and rotating file handlers.

    Args:
        log_level (str): The logging level (e.g., 'DEBUG', 'INFO').
        log_dir (str): The directory to store log files, relative to BASE_DIR.
        log_file_name (str): The name of the log file.
        max_bytes (int): Maximum size of the log file before rotation.
        backup_count (int): Number of backup log files to keep.
        logger_name (str, optional): The specific logger to configure.
                                     If None, configures the root logger.
    """
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level.upper())

    # Clear existing handlers for root or celery loggers to avoid conflicts
    # and ensure our configuration takes precedence.
    if logger.name is None or logger.name.startswith("celery"):
        if logger.hasHandlers():  # Check before clearing to avoid unnecessary debug log
            logger.handlers.clear()
            # Optional: Keep for debugging if needed later
            # logger.debug(f"Cleared existing handlers for logger '{logger.name or 'root'}'.")

    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Add Console Handler (if not already present)
    if not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # Create logs directory if it doesn't exist
    logs_path = BASE_DIR / log_dir
    try:
        logs_path.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logger.error(f"Failed to create log directory {logs_path}: {e}", exc_info=True)
        # Optionally return here if file logging is critical
        # return logger

    # Add Rotating File Handler (if directory creation succeeded and handler not present)
    if logs_path.exists():  # Proceed only if directory exists or was created
        log_file_path = logs_path / log_file_name
        try:
            # Prevent adding duplicate file handlers for the same file
            if not any(
                isinstance(h, RotatingFileHandler)
                and h.baseFilename == str(log_file_path)
                for h in logger.handlers
            ):
                file_handler = RotatingFileHandler(
                    log_file_path,
                    maxBytes=max_bytes,
                    backupCount=backup_count,
                    encoding="utf-8",
                )
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
                # Log confirmation that the file handler was added
                logger.info(f"File logging configured for {log_file_path}")

        except PermissionError:
            logger.error(
                f"Permission denied writing to log file {log_file_path}. File logging disabled.",
                exc_info=True,
            )
        except Exception as e:
            logger.error(
                f"Failed to set up file handler for {log_file_path}: {e}. File logging disabled.",
                exc_info=True,
            )
    else:
        logger.warning(
            f"Log directory {logs_path} does not exist. Skipping file handler setup."
        )

    # Optional: Log that setup was attempted (might be verbose)
    # logger.info(f"Logging setup processed for '{logger.name or 'root'}'.")

    return logger
