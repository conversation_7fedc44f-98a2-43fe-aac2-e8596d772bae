# backend/app/services/doc_processing/visualise.py

import logging
import json
from PIL import ImageDraw, ImageFont

logger = logging.getLogger(__name__)


class AnnotationVisualizer:
    label_to_color = {
        "PO_Number": "blue",
        "Contact_Name": "green",
        "Contact_Phone": "orange",
        "Contact_Email": "purple",
        "Delivery_Address": "yellow",
        "Invoice_Address": "pink",
        "Product_Code": "cyan",
        "Product_Qty": "magenta",
        "Invoice_Email": "lime",
        "Product_Name": "brown",
        "Unit_Cost": "red",
        "Line_Value": "navy",
        "Total_Value": "gold",
        "Ignore_Text": "grey",
        "Order_Date": "olive",
        "Invoice_Phone": "teal",
    }

    def __init__(self, json_data, image):
        try:
            self.data = (
                json.loads(json_data) if isinstance(json_data, str) else json_data
            )
            self.image_width = self.data["width"]
            self.image_height = self.data["height"]
            self.annotations = self.data["annotations"]
            self.image = image
            logger.info("AnnotationVisualizer initialized successfully.")
        except Exception as e:
            logger.error(f"Error initializing AnnotationVisualizer: {e}")
            raise

    def draw_annotations(self, output_path):
        try:
            with self.image as img:
                draw = ImageDraw.Draw(img)
                font = ImageFont.load_default()  # Use default font
                for annotation in self.annotations:
                    box = annotation["box"]
                    label = annotation["label"]
                    text = annotation.get("text", "")

                    # Convert the coordinates from normalized values to pixel values
                    x1 = (box[0] / 1000) * self.image_width
                    y1 = (box[1] / 1000) * self.image_height
                    x2 = (box[2] / 1000) * self.image_width
                    y2 = (box[3] / 1000) * self.image_height

                    color = self.label_to_color.get(
                        label, "red"
                    )  # Default color is red if label not found

                    # Swap if needed
                    if y1 > y2:
                        y1, y2 = y2, y1

                    draw.rectangle(((x1, y1), (x2, y2)), outline=color, width=2)
                    draw.text((x1, y1 - 20), f"{label}: {text}", fill=color, font=font)

                img.save(output_path)
                logger.info(
                    f"Annotations drawn and image saved to {output_path} successfully."
                )
        except Exception as e:
            logger.error(f"Error drawing annotations: {e}")
            raise

    def visualize(self):
        try:
            self.draw_annotations()
            logger.info("Visualization completed successfully.")
        except Exception as e:
            logger.error(f"Error in visualization: {e}")
            raise
