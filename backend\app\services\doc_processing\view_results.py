# backend/app/services/doc_processing/view_results.py

import os
import logging
import json
import uuid
from flask import current_app

from .pdf_to_imgs import PDFConverter
from .process_image import ImageProcessor
from .inference import ModelInference
from .visualise import AnnotationVisualizer
from ..pairwise_classification.inference import get_groups
from ...models_db.image import Image
from ...models_db.annotation import Annotation

logger = logging.getLogger(__name__)


def process_pdf(pdf_path, pdf_id, session):
    try:
        logger.info(f"Starting processing for {pdf_path}")

        # Get file and folder paths
        MODEL_PATH = current_app.config["MODEL_BASE_PATH"]
        PRODUCT_CODES_PATH = current_app.config["PRODUCT_CODES_TXT"]

        # Initialize converters and processors
        pdf_converter = PDFConverter()
        image_processor = ImageProcessor(PRODUCT_CODES_PATH)
        model_inference = ModelInference(
            model_path=MODEL_PATH, processor_path=MODEL_PATH
        )

        # Convert the PDF to images
        images = pdf_converter.convert_pdf_to_images(pdf_path)
        logger.info(f"PDF converted to images: {len(images)} images")

        output_files = []
        all_annotations = []  # To collect all annotations for the PDF

        for idx, image in enumerate(images):
            logger.info(f"Processing image {idx + 1}...")

            # Process the image and get predictions
            data = image_processor.process_image(image=image)

            if not data:
                logger.warning(f"Skipping image {idx + 1} due to no valid annotations.")
                continue

            predictions, sample, words, boxes, img = model_inference.predict(
                data, image
            )
            final_results = model_inference.decode_predictions(
                predictions, sample, words, boxes
            )

            # Create JSON output for the annotations
            output_json = {
                "file_name": f"image_{idx + 1}",
                "height": data[0]["height"],
                "width": data[0]["width"],
                "annotations": [
                    {
                        "id": uuid.uuid4().hex,
                        "page_num": idx + 1,
                        "box": item["box"],
                        "text": item["word"],
                        "label": item["label"],
                    }
                    for item in final_results
                ],
            }
            temp_list = []
            temp_list.append(output_json)
            groups = get_groups(temp_list)

            # Add group information to the annotations based on matching 'id'
            for annotation in output_json["annotations"]:
                annotation_id = annotation["id"]
                if annotation_id in groups:
                    annotation["group"] = groups[annotation_id]
                else:
                    annotation["group"] = (
                        None  # Optional: Handle case where no group is assigned
                    )

            all_annotations.append(output_json)

            # Visualize the annotations on the image
            visualizer = AnnotationVisualizer(output_json, image)
            base_filename = os.path.splitext(os.path.basename(pdf_path))[0]
            output_file = os.path.join(
                "/app/output", f"{base_filename}_page_{idx + 1}.jpg"
            )
            visualizer.draw_annotations(output_path=output_file)
            output_files.append(output_file)

            # Save image metadata in the database
            new_image = Image(
                pdf_id=pdf_id,
                filename=os.path.basename(output_file),
                storage_path=output_file,
            )
            session.add(new_image)
            session.commit()

        # After processing all images, save the combined annotations
        combined_annotations_filename = f"{base_filename}_annotations.json"
        combined_annotations_path = os.path.join(
            "/app/output", combined_annotations_filename
        )
        with open(combined_annotations_path, "w") as f:
            json.dump(all_annotations, f)

        # Save the combined annotations metadata in the database
        new_annotation = Annotation(
            pdf_id=pdf_id,
            filename=combined_annotations_filename,
            storage_path=combined_annotations_path,
        )
        session.add(new_annotation)
        session.commit()

        logger.info(f"Process completed successfully for PDF: {pdf_path}")
        return output_files

    except Exception as e:
        session.rollback()
        logger.error(f"Error in main process: {e}")
        raise e
