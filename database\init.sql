-- Create pdfs table
CREATE TABLE pdfs
(
    id SERIAL PRIMARY KEY,
    uploaded_by UUID NOT NULL,
    -- Who uploaded the PDF
    origin_name VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    storage_path VARCHAR(255) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'unprocessed',
    -- New status field with default value
    completed_by UUID DEFAULT NULL,
    -- Who approved the PDF
    completed_at TIMESTAMP,
    -- When it was processed
    comments TEXT,
    -- Comments during review
    skip_reason TEXT,
    skip_comments TEXT
);

-- Create images table
CREATE TABLE images
(
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES pdfs(id),
    filename VARCHAR(255) NOT NULL,
    storage_path VARCHAR(255) NOT NULL,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create the new annotations table with a reference to the pdfs table
CREATE TABLE annotations
(
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES pdfs(id),
    -- Reference to the PDF instead of the image
    filename VARCHAR(255) NOT NULL,
    storage_path VARCHAR(255) NOT NULL,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Orders table: holds overall order information
CREATE TABLE orders
(
    id SERIAL PRIMARY KEY,
    pdf_id INTEGER REFERENCES pdfs(id),
    -- Order details
    address_code VARCHAR(255),
    po_number VARCHAR(255),
    part_shipment VARCHAR(50),
    channel VARCHAR(50),
    lea VARCHAR(100),
    delivery_instructions VARCHAR(255),
    -- order_date DATE,
    -- contact_name VARCHAR(255),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(255),
    -- Delivery address fields
    delivery_organisation_name VARCHAR(255),
    delivery_house VARCHAR(255),
    delivery_street VARCHAR(255),
    delivery_locality VARCHAR(255),
    delivery_town VARCHAR(255),
    delivery_county VARCHAR(255),
    delivery_postcode VARCHAR(50),
    delivery_country VARCHAR(255),
    -- Invoice address fields
    -- invoice_organisation_name VARCHAR(255),
    -- invoice_house VARCHAR(255),
    -- invoice_street VARCHAR(255),
    -- invoice_locality VARCHAR(255),
    -- invoice_town VARCHAR(255),
    -- invoice_county VARCHAR(255),
    -- invoice_postcode VARCHAR(50),
    -- invoice_country VARCHAR(255),
    -- invoice_email VARCHAR(255),
    -- invoice_phone VARCHAR(50),
    -- total_value NUMERIC(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Order lines table: holds details of each product on the order
CREATE TABLE order_lines
(
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_code VARCHAR(50),
    product_name VARCHAR(255),
    product_qty INTEGER,
    unit_cost NUMERIC(10,2),
);