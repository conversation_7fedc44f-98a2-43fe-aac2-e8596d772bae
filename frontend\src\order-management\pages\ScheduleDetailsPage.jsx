// frontend/src/order-management/pages/ScheduleDetailsPage.jsx

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { FaSpinner, FaSave, FaArrowLeft, FaUpload } from 'react-icons/fa';
import { getScheduleDetails, updateSchedule } from '../services/scheduleService';
import Papa from "papaparse";
import '../styles/ScheduleDetailsPage.css';

const ScheduleDetailsPage = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);

    // Schedule data state
    const [schedule, setSchedule] = useState(null);
    const [eligibleContacts, setEligibleContacts] = useState([]);

    // Form state
    const [formData, setFormData] = useState({
        report_name: '',
        frequency: '',
        time_of_day: '',
        day_of_week: '',
        day_of_month: '',
        timezone: '',
    });

    // State for UI toggle controls (boolean values)
    const [parameterToggles, setParameterToggles] = useState({
        receive_if_no_open_orders: false,
        receive_historic_orders: false,
        receive_availability: false,
    });

    // States for availability options
    const [availabilityMode, setAvailabilityMode] = useState("category"); // "category" or "custom_list"
    const [customProductCodesString, setCustomProductCodesString] = useState(""); // For textarea input
    const [customProductCodes, setCustomProductCodes] = useState([]); // Array of processed IDs

    // State to store the full parameter objects from API (including IDs)
    const [scheduleParameters, setScheduleParameters] = useState([]);

    const findOrCreateParameter = (paramsArray, name, defaultValue) => {
        let param = paramsArray.find((p) => p.param_name === name);
        if (!param) {
            param = { param_name: name, param_value: defaultValue };
        }
        return param;
    };

    const updateScheduleParameterEntry = (name, value) => {
        setScheduleParameters((prevParams) => {
            const existingParamIndex = prevParams.findIndex(
                (p) => p.param_name === name,
            );
            if (existingParamIndex > -1) {
                const updatedParams = [...prevParams];
                updatedParams[existingParamIndex] = {
                    ...updatedParams[existingParamIndex],
                    param_value: value,
                };
                return updatedParams;
            } else {
                // If the parameter doesn't exist, add it.
                // This assumes your backend can handle creating new parameters if an 'id' is missing.
                // Or, these parameters should ideally be initialized by the backend.
                return [...prevParams, { param_name: name, param_value: value }];
            }
        });
    };

    // Fetch schedule details
    const fetchScheduleDetails = useCallback(async () => {
        setLoading(true);
        try {
            const response = await getScheduleDetails(id);
            const { schedule, eligible_contacts } = response.data;

            if (!schedule) { // Handle case where schedule might be null/undefined even if API call succeeds
                throw new Error("Schedule data not found.");
            }

            setSchedule(schedule);
            setEligibleContacts(eligible_contacts || []);

            // Initialize form data from schedule
            setFormData({
                report_name: schedule.report_name || '',
                frequency: schedule.frequency || 'monthly',
                time_of_day: schedule.time_of_day || '09:00:00',
                day_of_week: schedule.day_of_week !== null ? parseInt(schedule.day_of_week, 10) : 1,
                day_of_month: schedule.day_of_month !== null ? parseInt(schedule.day_of_month, 10) : 1,
                timezone: schedule.timezone
            });

            // Store the original parameters fetched from the API
            const fetchedParams = schedule.parameters || [];
            setScheduleParameters(fetchedParams);

            // Initialize the UI toggle state based on fetched parameters
            const initialToggles = {};
            let initialAvailabilityMode = "category";
            let initialCustomProductCodes = [];
            let initialCustomProductCodesJsonString = "[]";

            fetchedParams.forEach((param) => {
                initialToggles[param.param_name] = param.param_value === "TRUE";
                if (param.param_name === "availability_mode") {
                    initialAvailabilityMode = param.param_value || "category";
                }
                if (param.param_name === "custom_product_codes_json") {
                    initialCustomProductCodesJsonString = param.param_value || "[]";
                    try {
                        const parsedCodes = JSON.parse(initialCustomProductCodesJsonString);
                        if (Array.isArray(parsedCodes)) {
                            initialCustomProductCodes = parsedCodes.map(String); // Ensure strings
                        }
                    } catch (e) {
                        console.error("Error parsing custom_product_codes_json:", e);
                        initialCustomProductCodes = [];
                    }
                }
            });
            setParameterToggles({
                receive_if_no_open_orders:
                    initialToggles.receive_if_no_open_orders || false,
                receive_historic_orders:
                    initialToggles.receive_historic_orders || false,
                receive_availability:
                    initialToggles.receive_availability || false,
            });

            setAvailabilityMode(initialAvailabilityMode);
            setCustomProductCodes(initialCustomProductCodes);
            setCustomProductCodesString(initialCustomProductCodes.join("\n")); // For textarea display
        } catch (err) {
            const errorMsg = err.message || 'Failed to load schedule details';
            toast.error(errorMsg);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchScheduleDetails();
    }, [fetchScheduleDetails]);

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        // Ensure numeric values for day_of_week/month are stored as numbers
        let processedValue = type === "checkbox" ? checked : value;
        if (name === "day_of_week" || name === "day_of_month") {
            processedValue = value ? parseInt(value, 10) : null;
        }

        setFormData({
            ...formData,
            [name]: processedValue,
        });
    };

    // Handle parameter toggle changes
    const handleParameterToggleChange = (paramName, isChecked) => {
        setParameterToggles((prevToggles) => ({
            ...prevToggles,
            [paramName]: isChecked,
        }));
        updateScheduleParameterEntry(paramName, isChecked ? "TRUE" : "FALSE");

        // If toggling receive_availability on, ensure mode parameter exists
        if (paramName === "receive_availability" && isChecked) {
            // Ensure availability_mode parameter exists, default to "category"
            const modeParam = scheduleParameters.find(p => p.param_name === "availability_mode");
            if (!modeParam) {
                updateScheduleParameterEntry("availability_mode", "category");
                setAvailabilityMode("category"); // Update local state too
            } else {
                setAvailabilityMode(modeParam.param_value || "category");
            }
            // Ensure custom_product_codes_json parameter exists, default to "[]"
            if (!scheduleParameters.find(p => p.param_name === "custom_product_codes_json")) {
                updateScheduleParameterEntry("custom_product_codes_json", "[]");
                setCustomProductCodes([]);
                setCustomProductCodesString("");
            }
        }
    };

    const handleAvailabilityModeChange = (e) => {
        const newMode = e.target.value;
        setAvailabilityMode(newMode);
        updateScheduleParameterEntry("availability_mode", newMode);
    };

    const handleCustomProductCodesChange = (e) => {
        setCustomProductCodesString(e.target.value);
        // Process and update the actual ID list (e.g., on blur or with a button)
        // For simplicity, let's process on change for now, can be optimized
        const codes = e.target.value
            .split(/[\n,]+/) // Split by newline or comma
            .map((code) => code.trim())
            .filter(Boolean); // Remove empty strings
        setCustomProductCodes(codes);
        updateScheduleParameterEntry("custom_product_codes_json", JSON.stringify(codes));
    };

    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            if (file.type === "text/csv" || file.name.endsWith(".csv")) {
                Papa.parse(file, {
                    complete: (results) => {
                        // Assuming CSV is a single column of product IDs, or first column
                        const codes = results.data
                            .flat() // If multiple columns, take all; or specify results.data.map(row => row[0])
                            .map((code) => String(code).trim())
                            .filter(Boolean);
                        setCustomProductCodes(codes);
                        setCustomProductCodesString(codes.join("\n"));
                        updateScheduleParameterEntry(
                            "custom_product_codes_json",
                            JSON.stringify(codes),
                        );
                        toast.success(`${codes.length} product codes loaded from CSV.`);
                    },
                    error: (error) => {
                        toast.error("Failed to parse CSV: " + error.message);
                    },
                });
            } else {
                toast.error("Please upload a valid CSV file.");
            }
            // Reset file input to allow uploading the same file again
            event.target.value = null;
        }
    };

    // Submit form
    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);

        let finalParameters = [...scheduleParameters];

        // Ensure core parameters are present
        const coreParamNames = [
            "receive_if_no_open_orders",
            "receive_historic_orders",
            "receive_availability",
        ];
        coreParamNames.forEach(name => {
            if (!finalParameters.find(p => p.param_name === name)) {
                finalParameters.push({ param_name: name, param_value: parameterToggles[name] ? "TRUE" : "FALSE" });
            }
        });


        if (parameterToggles.receive_availability) {
            // Ensure availability_mode is in parameters
            let modeParam = finalParameters.find(p => p.param_name === "availability_mode");
            if (modeParam) {
                modeParam.param_value = availabilityMode;
            } else {
                finalParameters.push({ param_name: "availability_mode", param_value: availabilityMode });
            }

            // Ensure custom_product_codes_json is in parameters
            let customCodesParam = finalParameters.find(p => p.param_name === "custom_product_codes_json");
            const processedCustomCodes = customProductCodesString
                .split(/[\n,]+/)
                .map(code => code.trim())
                .filter(Boolean);
            const customCodesJsonValue = JSON.stringify(processedCustomCodes);

            if (customCodesParam) {
                customCodesParam.param_value = customCodesJsonValue;
            } else {
                finalParameters.push({ param_name: "custom_product_codes_json", param_value: customCodesJsonValue });
            }
        } else {
            // If receive_availability is false, ensure mode and custom IDs are set to defaults or cleared
            // This helps clean up parameters if the feature is disabled.
            let modeParam = finalParameters.find(p => p.param_name === "availability_mode");
            if (modeParam) modeParam.param_value = "category";
            else finalParameters.push({ param_name: "availability_mode", param_value: "category" });

            let customCodesParam = finalParameters.find(p => p.param_name === "custom_product_codes_json");
            if (customCodesParam) customCodesParam.param_value = "[]";
            else finalParameters.push({ param_name: "custom_product_codes_json", param_value: "[]" });
        }

        let updateData;
        try {
            // Validate form
            if (!formData.report_name) {
                throw new Error('Report name is required');
            }
            if (formData.frequency === 'weekly' || formData.frequency === 'biweekly') {
                if (formData.day_of_week === null) {
                    throw new Error('Day of week is required for weekly/biweekly schedules');
                }
            } else if (formData.frequency === 'monthly') {
                if (formData.day_of_month === null) {
                    throw new Error('Day of month is required for monthly schedules');
                }
                if (formData.day_of_month < 1 || formData.day_of_month > 31) {
                    throw new Error('Day of month must be between 1 and 31');
                }
            }

            updateData = {
                ...formData,
                day_of_month:
                    formData.day_of_month !== null
                        ? parseInt(formData.day_of_month, 10)
                        : null,
                day_of_week:
                    formData.day_of_week !== null
                        ? parseInt(formData.day_of_week, 10)
                        : null,
                parameters: finalParameters, // Send the managed parameters array
            };

            const updatePromise = updateSchedule(id, updateData);
            await toast.promise(updatePromise, {
                loading: "Saving schedule...",
                success: "Schedule updated successfully!",
                error: (err) =>
                    `Failed to update schedule: ${err.message || "Unknown error"}`,
            });
            fetchScheduleDetails(); // Refresh
        } catch (err) {
            if (!saving) {
                toast.error(err.message || "Failed to update schedule");
            }
            console.error("Error preparing or updating schedule:", err);
        } finally {
            setSaving(false);
        }
    };

    // Render frequency-specific fields
    const renderFrequencyFields = () => {
        switch (formData.frequency) {
            case 'daily':
                return null;
            case 'weekly':
            case 'biweekly':
                return (
                    <div className="form-group">
                        <label htmlFor="day_of_week">Day of Week</label>
                        <select
                            id="day_of_week"
                            name="day_of_week"
                            value={formData.day_of_week || 0}
                            onChange={handleInputChange}
                        >
                            <option value={0}>Sunday</option>
                            <option value={1}>Monday</option>
                            <option value={2}>Tuesday</option>
                            <option value={3}>Wednesday</option>
                            <option value={4}>Thursday</option>
                            <option value={5}>Friday</option>
                            <option value={6}>Saturday</option>
                        </select>
                    </div>
                );
            case 'monthly':
                return (
                    <div className="form-group">
                        <label htmlFor="day_of_month">Day of Month</label>
                        <input
                            type="number"
                            id="day_of_month"
                            name="day_of_month"
                            min="1"
                            max="31"
                            value={formData.day_of_month || 1}
                            onChange={handleInputChange}
                        />
                    </div>
                );
            default:
                return null;
        }
    };

    if (loading) {
        return (
            <div className="loading-container">
                <FaSpinner className="spinner" />
                <p>Loading schedule details...</p>
            </div>
        );
    }

    if (!schedule) {
        return (
            <div className="schedule-details-container error-state"> {/* Added class */}
                <h2>Error Loading Schedule</h2>
                <p>Could not load the schedule details. Please try again later.</p>
                <button
                    className="button primary-button"
                    onClick={() => navigate('/schedules')}
                >
                    <FaArrowLeft /> Back to Schedules
                </button>
            </div>
        );
    }

    return (
        <div className="schedule-details-container">
            <div className="schedule-details-header">
                <h1>
                    <button
                        className="back-button"
                        onClick={() => navigate('/schedules')}
                        aria-label="Back to Schedules"
                    >
                        <FaArrowLeft />
                    </button>
                    Edit Schedule
                </h1>
            </div>

            <form onSubmit={handleSubmit} className="schedule-form">
                <div className="form-card">
                    <div className="card-header">
                        <h2>Schedule Information</h2>
                    </div>
                    <div className="card-content">
                        <div className="form-group customer-info">
                            <label>Customer</label>
                            <div className="customer-name">
                                {schedule?.customers?.name || 'Unknown Customer'}
                            </div>
                        </div>

                        <div className="form-group">
                            <label htmlFor="report_name">Report Name</label>
                            <input
                                type="text"
                                id="report_name"
                                name="report_name"
                                value={formData.report_name}
                                onChange={handleInputChange}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="frequency">Frequency</label>
                            <select
                                id="frequency"
                                name="frequency"
                                value={formData.frequency}
                                onChange={handleInputChange}
                            >
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="biweekly">Biweekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>

                        {renderFrequencyFields()}

                        <div className="form-group">
                            <label htmlFor="time_of_day">Time of Day</label>
                            <input
                                type="time"
                                id="time_of_day"
                                name="time_of_day"
                                value={formData.time_of_day ? formData.time_of_day.substring(0, 5) : '09:00'}
                                onChange={(e) => handleInputChange({
                                    target: {
                                        name: 'time_of_day',
                                        value: e.target.value + ':00'
                                    }
                                })}
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="timezone">Timezone</label>
                            <div className="timezone-value">
                                {formData.timezone}
                            </div>
                        </div>
                    </div>
                </div>

                <div className="form-card">
                    <div className="card-header">
                        <h2>Report Options</h2>
                    </div>
                    <div className="card-content">
                        <div className="parameter-toggle-group">
                            <div className="parameter-label">
                                Should the customer receive a report if they have no open orders?
                            </div>
                            <div className="toggle-switch">
                                <label className="switch">
                                    <input
                                        type="checkbox"
                                        checked={parameterToggles.receive_if_no_open_orders}
                                        onChange={(e) =>
                                            handleParameterToggleChange(
                                                "receive_if_no_open_orders",
                                                e.target.checked,
                                            )
                                        } />
                                    <span className="slider round"></span>
                                </label>
                                <span className="toggle-value">
                                    {parameterToggles.receive_if_no_open_orders ? 'Yes' : 'No'}
                                </span>
                            </div>
                        </div>

                        <div className="parameter-toggle-group">
                            <div className="parameter-label">
                                Should the customer receive historic orders in their report?
                            </div>
                            <div className="toggle-switch">
                                <label className="switch">
                                    <input
                                        type="checkbox"
                                        checked={parameterToggles.receive_historic_orders}
                                        onChange={(e) =>
                                            handleParameterToggleChange(
                                                "receive_historic_orders",
                                                e.target.checked,
                                            )
                                        } />
                                    <span className="slider round"></span>
                                </label>
                                <span className="toggle-value">
                                    {parameterToggles.receive_historic_orders ? 'Yes' : 'No'}
                                </span>
                            </div>
                        </div>

                        <div className="parameter-toggle-group">
                            <div className="parameter-label">
                                Should the customer receive product availability in their report?
                            </div>
                            <div className="toggle-switch">
                                <label className="switch">
                                    <input
                                        type="checkbox"
                                        checked={parameterToggles.receive_availability}
                                        onChange={(e) =>
                                            handleParameterToggleChange(
                                                "receive_availability",
                                                e.target.checked,
                                            )
                                        } />
                                    <span className="slider round"></span>
                                </label>
                                <span className="toggle-value">
                                    {parameterToggles.receive_availability ? 'Yes' : 'No'}
                                </span>
                            </div>
                        </div>

                        {/* --- Availability Mode Section --- */}
                        {parameterToggles.receive_availability && (
                            <div className="availability-options-section">
                                <div className="form-group">
                                    <label>Availability Data Source:</label>
                                    <div className="radio-group">
                                        <label>
                                            <input
                                                type="radio"
                                                name="availabilityMode"
                                                value="category"
                                                checked={availabilityMode === "category"}
                                                onChange={handleAvailabilityModeChange}
                                            />
                                            Customer's Default Price Range
                                        </label>
                                        <label>
                                            <input
                                                type="radio"
                                                name="availabilityMode"
                                                value="custom_list"
                                                checked={availabilityMode === "custom_list"}
                                                onChange={handleAvailabilityModeChange}
                                            />
                                            Custom Product List
                                        </label>
                                    </div>
                                </div>

                                {availabilityMode === "custom_list" && (
                                    <div className="custom-list-controls">
                                        <div className="form-group">
                                            <label htmlFor="customProductCodes">
                                                Custom Product Codes (one per line or comma-separated):
                                            </label>
                                            <textarea
                                                id="customProductCodes"
                                                name="customProductCodes"
                                                rows="5"
                                                value={customProductCodesString}
                                                onChange={handleCustomProductCodesChange}
                                                placeholder="Enter one product code per line or separate with commas"
                                            ></textarea>
                                        </div>
                                        <div className="form-group file-upload-group">
                                            <label htmlFor="csvUpload" className="button secondary-button small-button">
                                                <FaUpload /> Upload CSV
                                            </label>
                                            <input
                                                type="file"
                                                id="csvUpload"
                                                accept=".csv"
                                                onChange={handleFileUpload}
                                                style={{ display: 'none' }} // Hide default input
                                            />
                                            {customProductCodes.length > 0 && (
                                                <span className="file-info">
                                                    {customProductCodes.length} product code{customProductCodes.length === 1 ? '' : 's'} loaded
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div >

                <div className="form-card">
                    <div className="card-header">
                        <h2>Recipients</h2>
                    </div>
                    <div className="card-content">
                        {eligibleContacts.length === 0 ? (
                            <div className="empty-state">
                                <p>No eligible contacts found for this customer.</p>
                            </div>
                        ) : (
                            <div className="contact-list">
                                {eligibleContacts.map(contact => (
                                    <div key={contact.id} className="contact-item">
                                        <div className="contact-info">
                                            <span className="contact-name">{contact.name}</span>
                                            <span className="contact-email">{contact.email}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                <div className="form-actions">
                    <button
                        type="submit"
                        className="button primary-button"
                        disabled={saving}
                    >
                        {saving ? <FaSpinner className="spinner" /> : <FaSave />}
                        {saving ? 'Saving...' : 'Save Changes'}
                    </button>
                    <button
                        type="button"
                        className="button secondary-button"
                        onClick={() => navigate('/schedules')}
                        disabled={saving}
                    >
                        Cancel
                    </button>
                </div>
            </form >
        </div >
    );
};

export default ScheduleDetailsPage;
