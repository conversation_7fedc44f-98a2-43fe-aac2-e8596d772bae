// frontend/src/sphere/pages/PendingJobsPage.jsx

import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { fetchPendingJobs } from '../services/pdfService';
import '../styles/PendingJobsPage.css';
import {
    FaFilePdf,
    FaExclamationTriangle,
    FaCheckCircle,
    FaHourglassHalf,
    FaCog,
    FaEye,
    FaInbox,
    FaUpload
} from 'react-icons/fa';

// Helper function to format date
const formatDate = (dateString) => {
    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
};

// Helper function to get status badge
const StatusBadge = ({ status }) => {
    let statusClass = '';
    let icon = null;
    let label = status;

    switch (status.toLowerCase()) {
        case 'pending':
            statusClass = 'pending';
            icon = <FaHourglassHalf className="status-icon" />;
            break;
        case 'processing':
            statusClass = 'processing';
            icon = <FaCog className="status-icon" />;
            break;
        case 'completed':
            statusClass = 'completed';
            icon = <FaCheckCircle className="status-icon" />;
            break;
        case 'failed':
            statusClass = 'failed';
            icon = <FaExclamationTriangle className="status-icon" />;
            break;
        default:
            statusClass = 'pending';
            icon = <FaHourglassHalf className="status-icon" />;
    }

    return (
        <span className={`status-badge ${statusClass}`}>
            {icon}
            {label}
        </span>
    );
};

const PendingJobsPage = () => {
    const [jobs, setJobs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        const getJobs = async () => {
            try {
                const data = await fetchPendingJobs();
                setJobs(data);
            } catch (err) {
                setError('Failed to load pending jobs');
            } finally {
                setLoading(false);
            }
        };

        getJobs();
    }, []);

    return (
        <div className="pending-jobs-page">
            <div className="page-header">
                <h1>Pending Jobs</h1>
                <p className="page-subtitle">
                    View and manage all PDF processing jobs in the system
                </p>
            </div>

            {loading ? (
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p className="loading-text">Loading pending jobs...</p>
                </div>
            ) : error ? (
                <div className="error-container">
                    <FaExclamationTriangle className="error-icon" />
                    <h2 className="error-title">Error Loading Jobs</h2>
                    <p className="error-message">{error}</p>
                    <button
                        className="action-button primary"
                        onClick={() => window.location.reload()}
                    >
                        Try Again
                    </button>
                </div>
            ) : jobs.length > 0 ? (
                <div className="table-container">
                    <table className="jobs-table">
                        <thead>
                            <tr>
                                <th>PDF Name</th>
                                <th>Uploaded At</th>
                                <th>Status</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            {jobs.map((job) => (
                                <tr key={job.id}>
                                    <td data-label="PDF Name">
                                        <div className="pdf-name-cell">
                                            <FaFilePdf className="pdf-icon" />
                                            <span className="pdf-name">{job.origin_name}</span>
                                        </div>
                                    </td>
                                    <td data-label="Uploaded At">{formatDate(job.uploaded_at)}</td>
                                    <td data-label="Status">
                                        <StatusBadge status={job.status} />
                                    </td>
                                    <td data-label="Actions">
                                        <Link
                                            to={`/pdfs/review/${job.id}`}
                                            className="action-button primary"
                                        >
                                            <FaEye />
                                            <span>Review</span>
                                        </Link>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            ) : (
                <div className="empty-state">
                    <FaInbox className="empty-icon" />
                    <h2 className="empty-title">No Pending Jobs</h2>
                    <p className="empty-description">
                        There are currently no PDF jobs in the system. Upload a new PDF to get started.
                    </p>
                    <Link to="/upload-pdf" className="action-button primary">
                        <FaUpload />
                        <span>Upload New PDF</span>
                    </Link>
                </div>
            )}
        </div>
    );
};

export default PendingJobsPage;
