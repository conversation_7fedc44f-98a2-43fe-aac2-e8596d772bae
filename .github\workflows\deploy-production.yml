# Cosy-Internal-Frontend/.github/workflows/deploy-production.yml
name: Deploy Frontend Production

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy:
    name: Build and Deploy Frontend
    runs-on: self-hosted # Target your Windows server runner

    # Define environment variables for the job using GitHub Secrets
    # These will be available to docker-compose via ${VAR_NAME}
    env:
      # --- Map ALL secrets needed by docker-compose.yml here ---
      REACT_APP_SUPABASE_URL: ${{ secrets.REACT_APP_SUPABASE_URL }}
      REACT_APP_SUPABASE_ANON_KEY: ${{ secrets.REACT_APP_SUPABASE_ANON_KEY }}

      # Set COMPOSE_PROJECT_NAME to avoid potential conflicts
      COMPOSE_PROJECT_NAME: internal_frontend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Ensure shared Docker network exists
        run: |
          if (-not (docker network ls --filter name=internal_app_network -q)) {
            Write-Host "Network 'internal_app_network' not found. Creating..."
            docker network create internal_app_network
          } else {
            Write-Host "Network 'internal_app_network' already exists."
          }
        shell: powershell -ExecutionPolicy Bypass -File {0}

      - name: Build Docker images for frontend
        # Docker Compose automatically uses variables from the 'env:' block
        run: docker-compose -f docker-compose.yml build --pull
        shell: powershell -ExecutionPolicy Bypass -File {0}

      - name: Deploy Frontend containers
        run: docker-compose -f docker-compose.yml up -d --remove-orphans
        shell: powershell -ExecutionPolicy Bypass -File {0}

      - name: Prune old Docker images
        if: always()
        run: docker image prune -a -f --filter "label!=maintainer"
        shell: powershell -ExecutionPolicy Bypass -File {0}
