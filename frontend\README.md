# Order Insights and Management Frontend

This directory contains the frontend code for the Order Insights and Management application. The frontend is built with React and communicates with the backend API and Supabase for authentication and data storage.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Project Structure](#project-structure)
4. [Configuration](#configuration)
5. [Dependencies](#dependencies)
6. [Setup and Installation](#setup-and-installation)
7. [Development](#development)
8. [Building for Production](#building-for-production)
9. [Deployment](#deployment)
10. [Docker Deployment](#docker-deployment)
11. [Troubleshooting](#troubleshooting)

## Overview

The frontend provides a user interface for managing order insights, schedules, and reports. It uses React for the UI, React Router for navigation, and Supabase for authentication and data storage.

## Features

- **Authentication**: Microsoft OAuth authentication through Supabase
- **Dashboard**: Overview of key metrics and recent activities
- **Schedules**: View and manage report schedules
- **Dark/Light Mode**: Theme toggle for user preference
- **Responsive Design**: Works on desktop and mobile devices

## Project Structure

```
frontend/
├── public/              # Static assets
├── src/
│   ├── components/      # Reusable UI components
│   ├── contexts/        # React context providers
│   ├── pages/           # Page components
│   ├── services/        # API and service integrations
│   ├── styles/          # CSS and styling files
│   ├── utils/           # Utility functions
│   ├── App.jsx          # Main application component
│   └── index.js         # Application entry point
├── docs/                # Documentation files
├── .env.example         # Example environment variables
├── package.json         # Dependencies and scripts
├── Dockerfile           # Container configuration
└── nginx.conf           # Nginx configuration for container
```

## Configuration

### Environment Variables

Create a `.env` file in the frontend directory with the following variables:

```
# Supabase Configuration
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key

# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
```

For production, create a `.env.production` file with appropriate values.

## Dependencies

Major dependencies include:

- React 19.1.0
- React Router 7.5.0
- Supabase JS Client 2.49.4
- React Icons 5.5.0

See `package.json` for the complete list of dependencies.

## Setup and Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/your-repo.git
   cd your-repo/frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment variables:**
   - Copy `.env.example` to `.env`
   - Fill in your Supabase URL and anonymous key
   - Set the API URL to your backend server

## Development

To start the development server:

```bash
npm start
```

This will run the app in development mode at [http://localhost:3000](http://localhost:3000).

## Building for Production

To create a production build:

```bash
npm run build
```

This creates optimized files in the `build` directory.

## Deployment

### Standard Deployment

1. Build the application as described above
2. Deploy the contents of the `build` directory to your web server

### Local Network Deployment

For deploying on a local network with Nginx, see the detailed instructions in `docs/local_network_deployment.md`.

Quick steps:

1. Create `.env.production` with your network IP address
2. Build the application
3. Use the deployment script:
   ```bash
   # Run as administrator
   deploy-to-nginx.bat
   ```

## Docker Deployment

The frontend is containerized using Docker with Nginx as the web server.

### Container Configuration

The frontend container is built using a multi-stage Dockerfile:
1. Stage 1: Builds the React application using Node.js
2. Stage 2: Serves the built application using Nginx

### Environment Variables for Docker

When using Docker, environment variables are passed at build time:

```bash
# These are set in the docker-compose.yml file
REACT_APP_API_URL=/api  # Uses relative path for API proxy
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Building and Running with Docker

#### Using Docker Directly

```bash
# Build the image
docker build -t frontend .

# Run the container
docker run -p 9000:9000 frontend
```

#### Using Docker Compose (Recommended)

See the main project README for instructions on using Docker Compose to run the entire application stack.

## Troubleshooting

If you encounter issues:

1. Check browser console for errors
2. Verify environment variables are set correctly
3. Ensure backend API is running and accessible
4. For authentication issues, verify Supabase configuration

### Docker-specific Troubleshooting

1. **Container not starting:**
   ```bash
   # Check container logs
   docker logs <container_id>
   ```

2. **API connection issues:**
   - Ensure the backend container is running
   - Check that the Nginx configuration is correctly proxying API requests
   - Verify network settings in docker-compose.yml

3. **Environment variable problems:**
   - Remember that environment variables for the React app must be set at build time
   - Check that all required variables are passed in docker-compose.yml
   - Rebuild the container after changing environment variables
