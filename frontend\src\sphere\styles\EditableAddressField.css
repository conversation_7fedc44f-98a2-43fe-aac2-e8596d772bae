/* frontend/src/sphere/styles/EditableAddressField.css */

.editable-address-field.is-empty-optional .form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.address-input-interactive-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.address-input-interactive-wrapper .form-control {
  flex-grow: 1;
  width: 100%;
  padding-right: 38px; /* Ensure space for the button */
  box-sizing: border-box;
  cursor: text;
}

/* Drag and drop styles */
.address-input-interactive-wrapper .form-control[draggable="true"] {
  cursor: grab;
}

.address-input-interactive-wrapper .form-control.dragging {
  opacity: 0.6;
  cursor: grabbing;
  background-color: rgba(var(--button-primary-bg-rgb), 0.1);
}

.editable-address-field.drag-over .form-control {
  border-color: var(--button-primary-bg);
  background-color: rgba(var(--button-primary-bg-rgb), 0.1);
  box-shadow: 0 0 0 2px rgba(var(--button-primary-bg-rgb), 0.2);
}

.address-field-clear-btn-inner {
  /* Positioning */
  position: absolute;
  right: 4px;
  top: 50%;
  z-index: 2;
  opacity: 0;
  transform: translateY(-50%) scale(0.8);
}

.address-input-interactive-wrapper:hover
  .address-field-clear-btn-inner:not(:disabled) {
  opacity: 0.7;
  transform: translateY(-50%) scale(1);
}

.address-field-clear-btn-inner:hover:not(:disabled),
.address-field-clear-btn-inner:focus:not(:disabled),
.address-field-clear-btn-inner:focus-visible:not(:disabled) {
  opacity: 1;
}

.address-field-clear-btn-inner:disabled {
  transform: translateY(-50%) scale(1);
}

/* Validation error styles */
.editable-address-field.has-error .form-control {
  border-color: var(--message-error-text);
  background-color: rgba(var(--message-error-bg-rgb), 0.1);
}

/* Drag preview styles */
.address-drag-preview {
  padding: 8px 12px;
  background: var(--card-background);
  border: 1px solid var(--button-primary-bg);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: absolute;
  top: -1000px;
  font-size: 0.9rem;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

