/* frontend/src/order-management/styles/ScheduleDetailsPage.css */
.schedule-details-container {
  padding: 20px 0;
}

.schedule-details-header {
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}

.schedule-details-header h1 {
  color: var(--text-color);
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  letter-spacing: -0.2px;
}

.back-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  color: var(--text-color);
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: var(--button-secondary-bg);
  transform: translateX(-2px);
}

.schedule-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-card {
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 18px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px;
  border-bottom: 1px solid var(--border-color-lighter);
}

.card-header h2 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: -0.2px;
}

.card-content {
  padding: 24px;
}

.form-group {
  margin-bottom: 12px;
  transition: opacity 0.3s ease;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  color: var(--text-color);
  font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="time"],
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  background-color: var(--input-background);
  color: var(--text-color);
  font-size: 1rem;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.form-group input:focus,
.form-group select:focus {
  border-color: var(--button-primary-bg);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.15);
  outline: none;
}

/* Improved select dropdowns */
.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20fill%3D%22%23666%22%20d%3D%22M8%2011L3%206h10z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 40px;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
}

.customer-info {
  background-color: var(--background-color);
  padding: 18px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.customer-name {
  font-weight: 600;
  color: var(--text-color);
}

.timezone-value {
  background-color: var(--input-background);
  padding: 12px 16px;
  border-radius: 8px;
  color: var(--text-color);
  font-weight: 500;
  border: 1px solid var(--border-color-light);
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
}

/* Parameter Toggle Styles */
.parameter-toggle-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 22px;
  padding: 16px;
  border-radius: 10px;
  background-color: var(--background-color);
  transition: all 0.2s ease;
  border: none;
}

.parameter-toggle-group:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.parameter-toggle-group:last-child {
  margin-bottom: 0;
  padding-bottom: 16px;
  border-bottom: none;
}

.parameter-label {
  font-weight: 500;
  color: var(--text-color);
  flex: 1;
  padding-right: 16px;
}

.toggle-switch {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-value {
  min-width: 30px;
  color: var(--text-color);
  font-weight: 500;
}

/* The switch - the box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: .3s cubic-bezier(0.4, 0.0, 0.2, 1);
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .3s cubic-bezier(0.4, 0.0, 0.2, 1);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input:checked + .slider {
  background-color: var(--button-primary-bg);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--button-primary-bg);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.empty-state {
  padding: 24px;
  text-align: center;
  color: var(--text-secondary);
  background-color: var(--background-color);
  border-radius: 8px;
  margin: 10px 0;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.contact-item {
  padding: 14px;
  border-radius: 8px;
  background-color: var(--background-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.contact-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.contact-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.contact-email {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 0px;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 46px;
}

.primary-button {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  box-shadow: 0 2px 6px rgba(0, 120, 212, 0.3);
}

.primary-button:hover {
  background-color: var(--button-primary-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 120, 212, 0.4);
}

.secondary-button {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
}

.secondary-button:hover {
  background-color: var(--button-secondary-hover-bg);
  transform: translateY(-1px);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.availability-options-section {
  margin-top: 25px;
  padding: 24px;
  background-color: var(--background-color);
  border-radius: 10px;
  border: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.availability-options-section .form-group {
  margin-bottom: 20px;
}

.availability-options-section .form-group:last-child {
  margin-bottom: 0;
}

.availability-options-section > .form-group > label {
  font-size: 1.15rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 15px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color-lighter);
  display: block;
}

.radio-group {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  width: 100%;
}

.radio-group label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  border: 1px solid var(--border-color-light);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--card-background);
  color: var(--text-secondary);
  flex-grow: 1;
  font-weight: 500;
  text-align: center;
  height: 48px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.radio-group input[type="radio"] {
  display: none;
}

.radio-group label:has(input[type="radio"]:checked) {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border-color: var(--button-primary-bg);
  box-shadow: 0 4px 10px rgba(0, 120, 212, 0.2);
}

.radio-group label:hover:not(:has(input[type="radio"]:checked)) {
  background-color: var(--background-color);
  border-color: var(--border-color);
  transform: translateY(-1px);
}

.custom-list-controls {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--background-color);
  border-radius: 10px;
  border: none;
  transition: all 0.2s ease;
}

.custom-list-controls .form-group label {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8px;
}

.custom-list-controls textarea {
  width: 100%;
  padding: 14px;
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 0.95rem;
  min-height: 130px;
  resize: vertical;
  transition: all 0.2s ease;
  font-family: "SF Mono", "Roboto Mono", monospace;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.custom-list-controls textarea:focus {
  border-color: var(--button-primary-bg);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.15);
  outline: none;
}

.custom-list-controls textarea::placeholder {
  color: var(--text-secondary);
  opacity: 0.8;
}

.file-upload-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 15px;
}

.file-upload-group > label.button.secondary-button.small-button {
  padding: 10px 16px;
  font-size: 0.9rem;
  background-color: var(--button-secondary-bg);
  color: var(--text-color);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  height: 38px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.file-upload-group > label.button.secondary-button.small-button:hover {
  background-color: var(--button-secondary-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.file-upload-group > label.button.secondary-button.small-button:active {
  transform: translateY(1px);
}

.file-upload-group .file-info {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Loading animation */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  min-height: 300px;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.spinner {
  animation: spin 1s linear infinite, pulse 1.5s ease-in-out infinite;
  font-size: 2rem;
  color: var(--button-primary-bg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error state */
.error-state {
  text-align: center;
  padding: 40px;
}
