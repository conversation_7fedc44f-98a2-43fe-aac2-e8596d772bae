/* frontend/src/shared/styles/CommonActionButtons.css */

.icon-action-button {
  background-color: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  width: 30px;
  height: 30px;
  padding: 0;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out,
    transform 0.15s ease-in-out, opacity 0.15s ease-in-out;
}

.icon-action-button:hover {
  color: var(--message-error-text);
  background-color: rgba(var(--message-error-text-rgb), 0.1);
}

.icon-action-button:focus-visible,
.icon-action-button:focus {
  outline: 2px solid var(--button-primary-bg);
  outline-offset: 1px;
  color: var(--message-error-text);
  background-color: rgba(
    var(--message-error-text-rgb),
    0.1
  );
}

.icon-action-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background-color: transparent;
  color: var(--text-disabled, #999);
  transform: scale(1); /* Reset any transform on disabled */
}
