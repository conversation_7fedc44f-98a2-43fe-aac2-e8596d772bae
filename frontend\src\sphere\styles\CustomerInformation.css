/* frontend/src/sphere/styles/CustomerInformation.css */

.customer-information-section {
    background-color: var(--card-background);
    border: 1px solid var(--border-color-light);
    border-radius: 8px;
    box-shadow: 0 1px 4px var(--shadow-color);
    margin-bottom: 20px;
    padding: 16px;
}

.customer-information-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.customer-information-header h2 {
    margin: 0;
    padding: 0;
    padding-bottom: 10px;
    border-bottom: none;
    font-size: 1.25rem;
    color: var(--text-color);
    width: auto;
    flex: 1; /* Take up available space */
    position: relative; /* For the pseudo-element positioning */
}

/* Add a pseudo-element for the border that stretches to the end of h2 */
.customer-information-header h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 10px;
    height: 1px;
    background-color: var(--border-color);
    z-index: 1; /* Ensure it's above other elements */
}

/* When customer is selected, we have buttons, so adjust the border */
.customer-information-section:has(.customer-actions) .customer-information-header h2::after {
    right: 10px;
}

.customer-actions {
    display: flex;
    gap: 10px;
}

.unselect-customer-button {
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-text);
}

.unselect-customer-button:hover {
    background-color: var(--button-secondary-hover-bg);
}

.no-customer-selected {
    padding: 16px 0;
    color: var(--text-secondary);
    font-style: italic;
}

.customer-required-text {
    color: var(--message-error-text);
    font-weight: 500;
}

.required-asterisk {
    color: var(--message-error-text);
    margin-left: 4px;
    font-weight: bold;
}

.customer-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    background-color: var(--background-color);
    border-radius: 6px;
    border: 1px solid var(--border-color-light);
}

.customer-primary-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.customer-name-company {
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
    gap: 16px;
    align-items: center;
}

.customer-name, .customer-company {
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-name {
    font-weight: 600;
    font-size: 1.05rem;
    color: var(--text-color);
}

.customer-company {
    font-weight: 500;
    color: var(--text-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.customer-contact {
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
    gap: 16px;
    margin-top: 4px;
}

.customer-email, .customer-phone {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.customer-address {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    padding-top: 8px;
    border-top: 1px solid var(--border-color-lighter);
}

.address-code {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .customer-name-company,
    .customer-contact {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .customer-actions {
        flex-direction: column;
        gap: 8px;
    }
}
