# backend/app/services/doc_processing/split_dataset.py

import json
import logging
import re
from uuid import uuid4

logger = logging.getLogger(__name__)


class JSONUpdater:
    def __init__(self, product_codes_file):
        try:
            self.product_codes = self.load_product_codes(product_codes_file)
            self.key_phrases = [
                "https://www.cosydirect.com/",
                "www.cosydirect.com/",
                "PO:",
                "PO Number:",
                "PO No:",
                "Order Number:",
                "Order Number",
                "Order No:",
                "FAO:",
                "Telephone:",
                "Telephone Number:",
                "Telephone No:",
                "Tel.:",
                "Tel. Number:",
                "Tel. No:",
                "Tel:",
                "Tel Number:",
                "Tel No:",
                "Tel Details:",
                "Tel. Details",
                "Phone:",
                "Phone Number:",
                "Phone No:",
                "Phone:",
                "Email:",
                "Email to:",
                "Fax:",
                "Fax Number:",
                "Fax No:",
                "Account Number:",
                "Account No:",
                "Account:",
                "Invoice Number:",
                "Invoice No:",
                "Invoice:",
                "Customer Number:",
                "Customer No:",
                "Customer:",
                "Reference Number:",
                "Reference No:",
                "Ref No:",
                "Ref:",
                "Shipment Number:",
                "Shipment No:",
                "Shipment:",
                "Tracking Number:",
                "Tracking No:",
                "Tracking:",
                "Vendor:",
                "Vendor Number:",
                "Vendor No:",
                "Supplier:",
                "Supplier Number:",
                "Supplier No:",
                "Product Code:",
                "Product ID:",
                "Item Number:",
                "Item No:",
                "SKU:",
                "Product:",
                "Item:",
                "Description:",
                "Qty:",
                "Quantity:",
                "Unit Price:",
                "Price:",
                "Subtotal:",
                "Total Amount:",
                "Amount:",
                "Date:",
                "Order Date:",
                "Delivery Date:",
                "Due Date:",
                "Payment Terms:",
                "Terms:",
                "Address:",
                "Billing Address:",
                "Shipping Address:",
                "Company Name:",
                "Company:",
                "Contact:",
                "Contact Name:",
                "Attention:",
                "Attn:",
                "Ship To:",
                "Bill To:",
                "Bill To Name:",
                "Delivery To:",
                "Ship Date:",
                "Delivery:",
                "Delivery Note:",
                "Salesperson:",
                "Sales Rep:",
                "Rep:",
                "Prepared By:",
                "Authorized By:",
                "Signature:",
                "Sign:",
                "Approved By:",
                "Approved:",
                "Customer PO:",
                "Customer Order:",
                "Purchase Order:",
                "Payment Due:",
                "Balance Due:",
                "Order Confirmation:",
                "Confirmation:",
                "Payment Method:",
                "Method of Payment:",
                "Payment:",
                "Credit Card:",
                "Card Number:",
                "Exp Date:",
                "CVV:",
                "Bank Transfer:",
                "Bank Details:",
                "Sort Code:",
                "Account Name:",
                "Swift Code:",
                "IBAN:",
                "Wire Transfer:",
                "EIN:",
                "Tax ID:",
                "VAT Number:",
                "VAT No:",
                "TIN:",
                "Company ID:",
                "DUNS Number:",
                "DUNS:",
                "Business ID:",
                "Registration Number:",
                "Reg No:",
                "License Number:",
                "License No:",
                "Issued By:",
                "Valid Until:",
                "Expiry Date:",
                "Document Number:",
                "Doc No:",
                "Document ID:",
                "Reference:",
                "Requisitioning Officer:",
                "Requisitioned By: ",
                "Requisitioned By :",
                "T:",
                "F:",
                "SKU",
                "PO Number/Rhif",
                "Entered by:",
                "Tel No. :",
                "Tel No.:",
                "Originator:",
                "Originator :",
                "Approved By:",
                "Approved By :",
                "Purchase order",
                "Raised By:",
                "Order No.",
                "Deliver Address:",
                "Deliver Address :",
                "Delivery address:",
                "Delivery address :",
                "Name:",
                "Name :",
                "2011STANDARD",
                "To:" "Ship to",
                "Supplier item",
                "Telephone/Ffon",
                "Date/Dyddiad",
                "Telephone/Ffon",
                "Contact no.",
                "Date of order",
            ]
            logger.info("JSONUpdater initialized successfully.")
        except Exception as e:
            logger.error(f"Error initializing JSONUpdater: {e}")
            raise

    def load_product_codes(self, product_codes_file):
        try:
            with open(product_codes_file, "r") as f:
                product_codes = f.read().splitlines()
            logger.info("Product codes loaded successfully: %s", product_codes_file)
            return set(product_codes)
        except FileNotFoundError as e:
            logger.error("Product codes file not found: %s", e)
            return set()
        except Exception as e:
            logger.error("Error loading product codes file: %s", e)
            return set()

    def load_json(self, json_file):
        try:
            with open(json_file, "r") as f:
                data = json.load(f)
            logger.info("JSON file loaded successfully: %s", json_file)
            return data
        except FileNotFoundError as e:
            logger.error("File not found: %s, %s", json_file, e)
            return None
        except Exception as e:
            logger.error("Error loading JSON file: %s, %s", json_file, e)
            return None

    def save_json(self, data, json_file):
        try:
            with open(json_file, "w") as f:
                json.dump(data, f, indent=4)
            logger.info("JSON file saved successfully: %s", json_file)
        except Exception as e:
            logger.error("Error saving JSON file: %s, %s", json_file, e)

    def create_bbox(self, part, width_ratio, x_start, bbox, image_width, image_height):
        try:
            width = width_ratio * bbox["width"] * image_width / 100
            new_bbox = {
                "x": 100 * x_start / image_width,
                "y": bbox["y"],
                "width": 100 * width / image_width,
                "height": bbox["height"],
                "rotation": bbox["rotation"],
            }
            x_start += width
            return part, new_bbox, x_start
        except Exception as e:
            logger.error("Error creating bounding box: %s", e)
            raise

    def split_text_with_pattern(self, pattern, text):
        try:
            match = re.search(pattern, text)
            if match:
                matched_text = match.group()
                pre_text, post_text = text.split(matched_text, 1)
                pre_text = pre_text.strip()
                post_text = post_text.strip()
                return pre_text, matched_text, post_text
            return None, None, None
        except Exception as e:
            logger.error(
                "Error splitting text with pattern: %s, pattern: %s, text: %s",
                e,
                pattern,
                text,
            )
            raise

    def process_text_annotation(
        self, bbox_annotation, text_annotation, image_width, image_height
    ):
        try:
            text = text_annotation["value"]["text"][0]
            bbox = bbox_annotation["value"]
            return text, bbox
        except Exception as e:
            logger.error("Error processing text annotation: %s", e)
            raise

    def split_text_and_boxes(self, text, keyword, bbox, image_width, image_height):
        try:
            lower_text = text.lower()
            lower_keyword = keyword.lower()

            keyword_index = lower_text.find(lower_keyword)
            if keyword_index == -1:
                return []

            before_keyword = text[: keyword_index + len(keyword)]
            after_keyword = text[keyword_index + len(keyword) :]

            total_chars = len(before_keyword) + len(after_keyword)
            before_width_ratio = len(before_keyword) / total_chars
            after_width_ratio = len(after_keyword) / total_chars

            x_start = bbox["x"] * image_width / 100

            def create_bbox(part, width_ratio):
                nonlocal x_start
                width = width_ratio * bbox["width"] * image_width / 100
                new_bbox = {
                    "x": 100 * x_start / image_width,
                    "y": bbox["y"],
                    "width": 100 * width / image_width,
                    "height": bbox["height"],
                    "rotation": bbox["rotation"],
                }
                x_start += width
                return part, new_bbox

            new_boxes = []
            if before_keyword:
                before_keyword = before_keyword.replace("-", " ")
                new_boxes.append(create_bbox(before_keyword, before_width_ratio))
            if after_keyword:
                after_keyword = after_keyword.replace("-", " ")
                new_boxes.append(create_bbox(after_keyword, after_width_ratio))

            return new_boxes
        except Exception as e:
            logger.error(
                "Error splitting text and boxes: %s, text: %s, keyword: %s",
                e,
                text,
                keyword,
            )
            raise

    def update_json_for_emails(self, data):
        try:
            email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
            for task in data:
                updated_annotations = []
                image_width, image_height = 100, 100

                bbox_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "rectangle"
                ]
                text_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "textarea"
                ]

                for bbox_annotation, text_annotation in zip(
                    bbox_annotations, text_annotations
                ):
                    text, bbox = self.process_text_annotation(
                        bbox_annotation, text_annotation, image_width, image_height
                    )
                    pre_email, email, post_email = self.split_text_with_pattern(
                        email_pattern, text
                    )
                    if email:
                        total_chars = len(pre_email) + len(email) + len(post_email)
                        pre_email_width_ratio = len(pre_email) / total_chars
                        email_width_ratio = len(email) / total_chars
                        post_email_width_ratio = len(post_email) / total_chars

                        x_start = bbox["x"] * image_width / 100
                        new_boxes = []

                        if pre_email:
                            part, new_bbox, x_start = self.create_bbox(
                                pre_email,
                                pre_email_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))
                        part, new_bbox, x_start = self.create_bbox(
                            email,
                            email_width_ratio,
                            x_start,
                            bbox,
                            image_width,
                            image_height,
                        )
                        new_boxes.append((part, new_bbox))
                        if post_email:
                            part, new_bbox, x_start = self.create_bbox(
                                post_email,
                                post_email_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))

                        for word, new_bbox in new_boxes:
                            region_id = str(uuid4())[:10]
                            bbox_result = {
                                "id": region_id,
                                "from_name": "bbox",
                                "to_name": "image",
                                "type": "rectangle",
                                "value": new_bbox,
                            }
                            transcription_result = {
                                "id": region_id,
                                "from_name": "transcription",
                                "to_name": "image",
                                "type": "textarea",
                                "value": {
                                    "text": [word],
                                    "x": new_bbox["x"],
                                    "y": new_bbox["y"],
                                    "width": new_bbox["width"],
                                    "height": new_bbox["height"],
                                    "rotation": new_bbox["rotation"],
                                },
                                "score": text_annotation["score"],
                            }
                            updated_annotations.extend(
                                [bbox_result, transcription_result]
                            )
                    else:
                        updated_annotations.extend([bbox_annotation, text_annotation])

                task["predictions"][0]["result"] = updated_annotations
            logger.info("JSON updated for emails successfully.")
            return data
        except Exception as e:
            logger.error("Error updating JSON for emails: %s", e)
            raise

    def update_json_for_phones(self, data):
        try:
            phone_pattern = r"\b(\+44\s?\d{4}|\+44\s?7\d{3}|\d{4}|\d{3}|\d{2}\s?\d{4}|\d{2}\s?7\d{3})\s?\d{3}\s?\d{3,4}\b"

            def process_phone_with_parentheses(phone):
                return phone.replace("(", "").replace(")", "").replace(" ", "")

            for task in data:
                updated_annotations = []
                image_width, image_height = 100, 100

                bbox_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "rectangle"
                ]
                text_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "textarea"
                ]

                for bbox_annotation, text_annotation in zip(
                    bbox_annotations, text_annotations
                ):
                    text, bbox = self.process_text_annotation(
                        bbox_annotation, text_annotation, image_width, image_height
                    )
                    pre_phone, phone, post_phone = self.split_text_with_pattern(
                        phone_pattern, text
                    )
                    new_boxes = []

                    if phone:
                        phone_updated = process_phone_with_parentheses(phone)
                    else:
                        phone_updated = None
                    if phone_updated and not len(phone_updated) < 10:
                        phone = phone_updated
                        total_chars = len(pre_phone) + len(phone) + len(post_phone)
                        pre_phone_width_ratio = len(pre_phone) / total_chars
                        phone_width_ratio = len(phone) / total_chars
                        post_phone_width_ratio = len(post_phone) / total_chars

                        x_start = bbox["x"] * image_width / 100

                        if pre_phone:
                            part, new_bbox, x_start = self.create_bbox(
                                pre_phone,
                                pre_phone_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))
                        part, new_bbox, x_start = self.create_bbox(
                            phone,
                            phone_width_ratio,
                            x_start,
                            bbox,
                            image_width,
                            image_height,
                        )
                        new_boxes.append((part, new_bbox))
                        if post_phone:
                            part, new_bbox, x_start = self.create_bbox(
                                post_phone,
                                post_phone_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))
                    else:
                        updated_annotations.extend([bbox_annotation, text_annotation])

                    for word, new_bbox in new_boxes:
                        region_id = str(uuid4())[:10]
                        bbox_result = {
                            "id": region_id,
                            "from_name": "bbox",
                            "to_name": "image",
                            "type": "rectangle",
                            "value": new_bbox,
                        }
                        transcription_result = {
                            "id": region_id,
                            "from_name": "transcription",
                            "to_name": "image",
                            "type": "textarea",
                            "value": {
                                "text": [word],
                                "x": new_bbox["x"],
                                "y": new_bbox["y"],
                                "width": new_bbox["width"],
                                "height": new_bbox["height"],
                                "rotation": new_bbox["rotation"],
                            },
                            "score": text_annotation["score"],
                        }
                        updated_annotations.extend([bbox_result, transcription_result])
                task["predictions"][0]["result"] = updated_annotations
            logger.info("JSON updated for phones successfully.")
            return data
        except Exception as e:
            logger.error("Error updating JSON for phones: %s", e)
            raise

    def check_for_product_code(self, text):
        try:
            words = re.split(r"(\W+)", text)
            for word in words:
                stripped_word = word.strip()
                if stripped_word in self.product_codes:
                    index = text.find(word)
                    pre_char = text[index - 1] if index > 0 else " "
                    post_char = (
                        text[index + len(word)]
                        if index + len(word) < len(text)
                        else " "
                    )
                    if (
                        (
                            re.match(r"[\s'-]", pre_char)
                            or re.match(r"[SKU]", pre_char)
                            or re.match(r"[SKU:]", pre_char)
                            or re.match(r"[(]", pre_char)
                        )
                        and not pre_char.endswith(".")
                        and (
                            re.match(r"[\s'-:]|$", post_char)
                            or re.match(r"\.\s*$", post_char)
                            or post_char.startswith("-")
                            or post_char.startswith(")")
                            or post_char.startswith(",")
                        )
                        and not post_char.startswith(".")
                    ):
                        # Help avoid splitting prices
                        if not pre_char.endswith(".") and not (
                            post_char.startswith(".") and len(post_char) > 1
                        ):
                            return stripped_word
            return None
        except Exception as e:
            logger.error("Error checking for product code: %s, text: %s", e, text)
            raise

    def update_json_for_product_codes(self, data):
        try:
            for task in data:
                updated_annotations = []
                image_width, image_height = 100, 100

                bbox_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "rectangle"
                ]
                text_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "textarea"
                ]

                for bbox_annotation, text_annotation in zip(
                    bbox_annotations, text_annotations
                ):
                    text, bbox = self.process_text_annotation(
                        bbox_annotation, text_annotation, image_width, image_height
                    )

                    x_start = bbox["x"] * image_width / 100
                    total_chars = len(text)
                    new_boxes = []

                    while True:
                        product_code = self.check_for_product_code(text)
                        if not product_code:
                            break

                        pre_code, post_code = text.split(product_code, 1)
                        pre_code = pre_code.strip()
                        post_code = post_code.strip()

                        pre_code_width_ratio = (
                            len(pre_code) / total_chars if pre_code else 0
                        )
                        product_code_width_ratio = len(product_code) / total_chars
                        post_code_width_ratio = (
                            len(post_code) / total_chars if post_code else 0
                        )

                        if pre_code:
                            pre_code_stripped = pre_code.replace("-", " ")
                            part, new_bbox, x_start = self.create_bbox(
                                pre_code_stripped,
                                pre_code_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))

                        part, new_bbox, x_start = self.create_bbox(
                            product_code,
                            product_code_width_ratio,
                            x_start,
                            bbox,
                            image_width,
                            image_height,
                        )
                        new_boxes.append((part, new_bbox))

                        text = post_code

                    if not new_boxes:
                        new_boxes.append((text, bbox))
                    else:
                        if text:
                            text_stripped = text.replace("-", " ")
                            part, new_bbox, x_start = self.create_bbox(
                                text_stripped,
                                len(text) / total_chars,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))

                    for word, new_bbox in new_boxes:
                        region_id = str(uuid4())[:10]
                        bbox_result = {
                            "id": region_id,
                            "from_name": "bbox",
                            "to_name": "image",
                            "type": "rectangle",
                            "value": new_bbox,
                        }
                        transcription_result = {
                            "id": region_id,
                            "from_name": "transcription",
                            "to_name": "image",
                            "type": "textarea",
                            "value": {
                                "text": [word],
                                "x": new_bbox["x"],
                                "y": new_bbox["y"],
                                "width": new_bbox["width"],
                                "height": new_bbox["height"],
                                "rotation": new_bbox["rotation"],
                            },
                            "score": text_annotation["score"],
                        }
                        updated_annotations.extend([bbox_result, transcription_result])

                task["predictions"][0]["result"] = updated_annotations
            logger.info("JSON updated for product codes successfully.")
            return data
        except Exception as e:
            logger.error("Error updating JSON for product codes: %s", e)
            raise

    def update_json_for_key_phrases(self, data):
        try:
            for task in data:
                updated_annotations = []
                image_width, image_height = 100, 100

                bbox_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "rectangle"
                ]
                text_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "textarea"
                ]

                for bbox_annotation, text_annotation in zip(
                    bbox_annotations, text_annotations
                ):
                    text, bbox = self.process_text_annotation(
                        bbox_annotation, text_annotation, image_width, image_height
                    )
                    for phrase in self.key_phrases:
                        if phrase.upper() in text.upper():
                            new_boxes = self.split_text_and_boxes(
                                text, phrase, bbox, image_width, image_height
                            )
                            for word, new_bbox in new_boxes:
                                region_id = str(uuid4())[:10]
                                bbox_result = {
                                    "id": region_id,
                                    "from_name": "bbox",
                                    "to_name": "image",
                                    "type": "rectangle",
                                    "value": new_bbox,
                                }
                                transcription_result = {
                                    "id": region_id,
                                    "from_name": "transcription",
                                    "to_name": "image",
                                    "type": "textarea",
                                    "value": {
                                        "text": [word],
                                        "x": new_bbox["x"],
                                        "y": new_bbox["y"],
                                        "width": new_bbox["width"],
                                        "height": new_bbox["height"],
                                        "rotation": new_bbox["rotation"],
                                    },
                                    "score": text_annotation["score"],
                                }
                                updated_annotations.extend(
                                    [bbox_result, transcription_result]
                                )
                            break
                    else:
                        updated_annotations.extend([bbox_annotation, text_annotation])

                task["predictions"][0]["result"] = updated_annotations
            logger.info("JSON updated for key phrases successfully.")
            return data
        except Exception as e:
            logger.error("Error updating JSON for key phrases: %s", e)
            raise

    def update_json_for_quantities_ea(self, data):
        try:
            quantity_pattern = (
                r"(\b\d+(\.\d+)?\b)\s*(EA|EACH|~EA|~EACH)(?=\s|$)|(\bi\s*'?\s*EACH\b)"
            )
            for task in data:
                updated_annotations = []
                image_width, image_height = 100, 100

                bbox_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "rectangle"
                ]
                text_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "textarea"
                ]

                for bbox_annotation, text_annotation in zip(
                    bbox_annotations, text_annotations
                ):
                    text, bbox = self.process_text_annotation(
                        bbox_annotation, text_annotation, image_width, image_height
                    )
                    match = re.search(quantity_pattern, text, re.IGNORECASE)
                    if match:
                        if match.group(1):
                            quantity = match.group(1)
                            unit = match.group(3).upper()
                            matched_text = match.group(0)
                        else:
                            quantity = "1"
                            unit = "EACH"
                            matched_text = match.group(4)

                        pre_quantity, post_quantity = text.split(matched_text, 1)
                        pre_quantity = pre_quantity.strip()
                        post_quantity = post_quantity.strip()

                        total_chars = (
                            len(pre_quantity)
                            + len(quantity)
                            + len(unit)
                            + len(post_quantity)
                        )
                        pre_quantity_width_ratio = len(pre_quantity) / total_chars
                        quantity_width_ratio = len(quantity) / total_chars
                        unit_width_ratio = len(unit) / total_chars
                        post_quantity_width_ratio = len(post_quantity) / total_chars

                        x_start = bbox["x"] * image_width / 100

                        new_boxes = []
                        if pre_quantity:
                            part, new_bbox, x_start = self.create_bbox(
                                pre_quantity,
                                pre_quantity_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))
                        part, new_bbox, x_start = self.create_bbox(
                            quantity,
                            quantity_width_ratio,
                            x_start,
                            bbox,
                            image_width,
                            image_height,
                        )
                        new_boxes.append((part, new_bbox))
                        part, new_bbox, x_start = self.create_bbox(
                            unit,
                            unit_width_ratio,
                            x_start,
                            bbox,
                            image_width,
                            image_height,
                        )
                        new_boxes.append((part, new_bbox))
                        if post_quantity:
                            part, new_bbox, x_start = self.create_bbox(
                                post_quantity,
                                post_quantity_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))

                        for word, new_bbox in new_boxes:
                            region_id = str(uuid4())[:10]
                            bbox_result = {
                                "id": region_id,
                                "from_name": "bbox",
                                "to_name": "image",
                                "type": "rectangle",
                                "value": new_bbox,
                            }
                            transcription_result = {
                                "id": region_id,
                                "from_name": "transcription",
                                "to_name": "image",
                                "type": "textarea",
                                "value": {
                                    "text": [word],
                                    "x": new_bbox["x"],
                                    "y": new_bbox["y"],
                                    "width": new_bbox["width"],
                                    "height": new_bbox["height"],
                                    "rotation": new_bbox["rotation"],
                                },
                                "score": text_annotation["score"],
                            }
                            updated_annotations.extend(
                                [bbox_result, transcription_result]
                            )
                    else:
                        updated_annotations.extend([bbox_annotation, text_annotation])

                task["predictions"][0]["result"] = updated_annotations
            logger.info("JSON updated for quantities (EA) successfully.")
            return data
        except Exception as e:
            logger.error("Error updating JSON for quantities (EA): %s", e)
            raise

    def update_json_for_quantities_x(self, data):
        try:
            quantity_pattern = r"\b(\s*[xX]\s*)(\d+)(\s|$)|\b(\s*[xX])(\d+)(\s|$)"
            for task in data:
                updated_annotations = []
                image_width, image_height = 100, 100

                bbox_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "rectangle"
                ]
                text_annotations = [
                    a
                    for a in task["predictions"][0]["result"]
                    if a["type"] == "textarea"
                ]

                for bbox_annotation, text_annotation in zip(
                    bbox_annotations, text_annotations
                ):
                    text, bbox = self.process_text_annotation(
                        bbox_annotation, text_annotation, image_width, image_height
                    )
                    match = re.search(quantity_pattern, text, re.IGNORECASE)
                    if match:
                        if match.group(1):
                            before_x = text[: match.start()].strip()
                            x_text = match.group(1).strip()
                            number_after_x = match.group(2).strip()
                            after_number = text[match.end() :].strip()
                        elif match.group(4):
                            before_x = text[: match.start()].strip()
                            x_text = match.group(4).strip()
                            number_after_x = match.group(5).strip()
                            after_number = text[match.end() :].strip()

                        total_chars = (
                            len(before_x)
                            + len(x_text)
                            + len(number_after_x)
                            + len(after_number)
                        )
                        before_x_width_ratio = len(before_x) / total_chars
                        x_text_width_ratio = len(x_text) / total_chars
                        number_after_x_width_ratio = len(number_after_x) / total_chars
                        after_number_width_ratio = len(after_number) / total_chars

                        x_start = bbox["x"] * image_width / 100

                        new_boxes = []
                        if before_x:
                            part, new_bbox, x_start = self.create_bbox(
                                before_x,
                                before_x_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))
                        part, new_bbox, x_start = self.create_bbox(
                            x_text,
                            x_text_width_ratio,
                            x_start,
                            bbox,
                            image_width,
                            image_height,
                        )
                        new_boxes.append((part, new_bbox))
                        part, new_bbox, x_start = self.create_bbox(
                            number_after_x,
                            number_after_x_width_ratio,
                            x_start,
                            bbox,
                            image_width,
                            image_height,
                        )
                        new_boxes.append((part, new_bbox))
                        if after_number:
                            part, new_bbox, x_start = self.create_bbox(
                                after_number,
                                after_number_width_ratio,
                                x_start,
                                bbox,
                                image_width,
                                image_height,
                            )
                            new_boxes.append((part, new_bbox))

                        for word, new_bbox in new_boxes:
                            region_id = str(uuid4())[:10]
                            bbox_result = {
                                "id": region_id,
                                "from_name": "bbox",
                                "to_name": "image",
                                "type": "rectangle",
                                "value": new_bbox,
                            }
                            transcription_result = {
                                "id": region_id,
                                "from_name": "transcription",
                                "to_name": "image",
                                "type": "textarea",
                                "value": {
                                    "text": [word],
                                    "x": new_bbox["x"],
                                    "y": new_bbox["y"],
                                    "width": new_bbox["width"],
                                    "height": new_bbox["height"],
                                    "rotation": new_bbox["rotation"],
                                },
                                "score": text_annotation["score"],
                            }
                            updated_annotations.extend(
                                [bbox_result, transcription_result]
                            )
                    else:
                        updated_annotations.extend([bbox_annotation, text_annotation])

                task["predictions"][0]["result"] = updated_annotations
            logger.info("JSON updated for quantities (X) successfully.")
            return data
        except Exception as e:
            logger.error("Error updating JSON for quantities (X): %s", e)
            raise

    def update_json(self, input_json=None):
        try:
            data = input_json
            if data is None:
                logger.warning("No input JSON data provided for update.")
                return

            data = self.update_json_for_key_phrases(data)
            data = self.update_json_for_phones(data)
            data = self.update_json_for_emails(data)
            data = self.update_json_for_quantities_ea(data)
            data = self.update_json_for_quantities_x(data)
            data = self.update_json_for_product_codes(data)

            logger.info("JSON update completed successfully.")
            return data
        except Exception as e:
            logger.error(f"Error updating JSON: {e}")
            raise
