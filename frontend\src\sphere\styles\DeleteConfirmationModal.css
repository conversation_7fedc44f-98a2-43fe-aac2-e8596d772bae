/* frontend/src/sphere/styles/DeleteConfirmationModal.css */

.delete-confirmation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Match ProductVerificationModal */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
  transition: all 0.2s ease-in-out;
  animation: fadeIn 0.2s forwards;
}

.delete-confirmation-modal {
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 8px 24px var(--shadow-color);
  width: 90%;
  max-width: 450px;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
  border: 1px solid var(--border-color-light);
  transform: scale(0.95);
  opacity: 0;
  animation: scaleIn 0.2s 0.1s forwards;
}

.delete-confirmation-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
}

.delete-confirmation-modal .modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.delete-confirmation-modal .warning-icon {
  color: var(--message-error-text);
}

.delete-confirmation-modal .close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--button-secondary-bg);
  border: none;
  width: 28px;
  height: 28px;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.delete-confirmation-modal .close-button:hover {
  color: var(--text-color);
  background-color: var(--button-secondary-hover-bg);
}

.delete-confirmation-modal .modal-body {
  padding: 20px;
}

.delete-confirmation-modal .delete-warning {
  font-size: 1rem;
  margin-bottom: 16px;
  color: var(--text-color);
}

.delete-confirmation-modal .product-to-delete {
  background-color: var(--border-color-lighter);
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-color);
}

.delete-confirmation-modal .delete-icon {
  color: var(--message-error-text);
  font-size: 1.1rem;
}

.delete-confirmation-modal .delete-note {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0;
}

.delete-confirmation-modal .modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
}

.delete-confirmation-modal .cancel-button {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.delete-confirmation-modal .cancel-button:hover {
  background-color: var(--button-secondary-hover-bg);
  transform: translateY(-1px);
}

.delete-confirmation-modal .confirm-button {
  background-color: var(--message-error-text);
  color: var(--button-primary-text);
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px var(--shadow-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-confirmation-modal .confirm-button:hover {
  background-color: var(--message-error-bg); /* Darker shade using error background */
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
