# PDF to Images Script

This script converts each page of a PDF file into individual images using PyMuPDF and PIL (Pillow). The converted images are returned as a list of PIL Image objects.

## Dependencies

- `os`
- `logging`
- `fitz` (PyMuPDF)
- `PIL` (Pillow)
- `numpy`
- `concurrent.futures` (ProcessPoolExecutor, as_completed)

## Configuration

The logging configuration is set to display information messages.

```python
logging.basicConfig(level=logging.INFO)
```

## Classes

### PDFConverter

This class is responsible for converting PDF files into images.

#### __init__(self)
Initialises the `PDFConverter` class.

#### convert_pdf_to_images(self, pdf_file_path)
Converts a single PDF file into individual images and returns them as a list of PIL Image objects.

- **Parameters**:
  - `pdf_file_path` (str): Path to the PDF file.

- **Returns**:
  - `images` (list): List of PIL Image objects representing each page of the PDF.

## Method Details

### convert_pdf_to_images(self, pdf_file_path)
This method performs the following steps:

1. Opens the PDF document using `fitz`.
2. Iterates through each page of the PDF.
3. Converts each page to an image using a specified zoom level for high-quality output.
4. Converts the pixmap to a PIL Image object and appends it to the list of images.
5. Logs the conversion of each page.
6. Returns the list of images.


## Important Notes

- Ensure that the PDF file path provided is correct and that the necessary dependencies are installed.
- The zoom level is set to 300 DPI for high-quality images, but this can be adjusted if needed.
- The script logs the conversion process for each page of the PDF.

## Error Handling

The script does not include explicit error handling. Ensure that the PDF file exists and is accessible, and that the necessary libraries are installed and imported correctly.
ining its purpose, functionality, and usage.
```