// frontend/src/sphere/services/pdfService.js

import { supabase } from '../../shared/services/supabase';

// Helper to get authentication token
const getToken = async () => {
    const { data: sessionData } = await supabase.auth.getSession();
    return sessionData?.session?.access_token;
};

// Helper for API requests with authentication
const authFetch = async (endpoint, options = {}) => {
    const token = await getToken();

    if (!token) {
        throw new Error('Authentication token not found');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
    };

    const response = await fetch(endpoint, {
        ...options,
        headers
    });

    const text = await response.text();

    let data;
    try {
        data = JSON.parse(text);
    } catch (parseError) {
        // Friendly fallback for any HTML or garbage response
        throw new Error('Server returned an unexpected response. Please try again later.');
    }

    if (!response.ok) {
        // Handle specific error messages from the API
        if (data.error) {
            throw new Error(data.error);
        }

        // Get context-aware error messages based on the endpoint
        const errorMessages = getContextAwareErrorMessages(endpoint);

        // Handle specific HTTP status codes with context-aware messages
        if (response.status === 404) {
            throw new Error(errorMessages.notFound);
        } else if (response.status === 400) {
            throw new Error(data.message || errorMessages.badRequest);
        } else if (response.status === 401 || response.status === 403) {
            throw new Error(errorMessages.unauthorized);
        } else if (response.status >= 500) {
            throw new Error(errorMessages.serverError);
        }

        // Fallback for other errors
        throw new Error(data.message || errorMessages.generic);
    }

    return data;
};

// Helper function to get context-aware error messages based on the endpoint
const getContextAwareErrorMessages = (endpoint) => {
    // Default generic messages
    const defaultMessages = {
        notFound: 'Resource not found. Please check your request and try again.',
        badRequest: 'Invalid input. Please check your data and try again.',
        unauthorized: 'Authentication error. Please log in again.',
        serverError: 'Server error. Please try again later or contact support.',
        generic: 'Something went wrong. Please try again.'
    };

    // Product lookup specific messages
    if (endpoint.includes('/api/lookup/product_lookup')) {
        return {
            ...defaultMessages,
            notFound: 'Product not found. Please check the product code and try again.',
            badRequest: 'Invalid product data. Please check the product code format and try again.'
        };
    }

    // Product lookup by name specific messages
    if (endpoint.includes('/api/lookup/product_lookup_by_name')) {
        return {
            ...defaultMessages,
            notFound: 'No matching products found. Please try different search terms.',
            badRequest: 'Invalid product search data. Please check your input and try again.'
        };
    }

    // PDF specific messages
    if (endpoint.includes('/api/pdfs')) {
        return {
            ...defaultMessages,
            notFound: 'PDF not found. Please check the PDF ID and try again.'
        };
    }

    // Customer lookup specific messages
    if (endpoint.includes('/api/lookup/customer_lookup')) {
        return {
            ...defaultMessages,
            notFound: 'Customer not found. Please check the customer information and try again.'
        };
    }

    // Return default messages for other endpoints
    return defaultMessages;
};

// Helper for API requests with auth (for Blob responses like images)
const authFetchBlob = async (endpoint, options = {}) => {
    const token = await getToken();

    if (!token) {
        throw new Error('Authentication token not found for blob fetch');
    }

    const headers = {
        'Authorization': `Bearer ${token}`,
        ...options.headers
    };

    const response = await fetch(endpoint, {
        ...options,
        headers
    });

    if (!response.ok) {
        // Get context-aware error messages
        const errorMessages = getContextAwareErrorMessages(endpoint);

        // Attempt to parse error as JSON, as API might still return JSON errors
        let errorPayload;
        try {
            errorPayload = await response.json();

            // Use API-provided error message if available, otherwise use context-aware message
            if (errorPayload.message || errorPayload.error) {
                throw new Error(errorPayload.message || errorPayload.error);
            }

            // Use context-aware error messages based on status code
            if (response.status === 404) {
                throw new Error(errorMessages.notFound);
            } else if (response.status === 400) {
                throw new Error(errorMessages.badRequest);
            } else if (response.status === 401 || response.status === 403) {
                throw new Error(errorMessages.unauthorized);
            } else if (response.status >= 500) {
                throw new Error(errorMessages.serverError);
            }

            // Fallback
            throw new Error(errorMessages.generic);

        } catch (e) {
            // If error payload isn't JSON, or json parsing failed
            // Check if e is our own error with a message
            if (e.message && e.message !== 'Unexpected token < in JSON at position 0') {
                throw e;
            }

            // Otherwise, use status text or generic message
            throw new Error(
                response.statusText ||
                `Request failed with status ${response.status}`
            );
        }
    }

    return response.blob(); // Returns a Promise that resolves with a Blob
};

// Get all pending jobs
export const fetchPendingJobs = async () => {
    return authFetch('/api/pdfs/pending_jobs');
};

// Fetch review data for a specific PDF
export const fetchPdfReviewData = async (pdfId) => {
    return authFetch(`/api/pdfs/pdf/${pdfId}/review-data`);
};

// View image
export const fetchImageAsBlob = async (imageUrl) => {
    if (!imageUrl) {
        throw new Error('Image URL is required to fetch blob.');
    }
    return authFetchBlob(imageUrl);
};

// Lookup product by code
export const lookupProductByCode = async (productCode, addressId) => {
    if (!productCode) {
        throw new Error('Product code is required for lookup.');
    }
    if (!addressId) {
        throw new Error('Customer must be selected for lookup.')
    }

    // Validate product code format (alphanumeric and hyphens only)
    const validProductCodePattern = /^[a-zA-Z0-9-]{1,20}$/;
    if (!validProductCodePattern.test(productCode)) {
        throw new Error('Invalid product code format. Only letters, numbers, and hyphens are allowed (max 20 characters).');
    }

    try {
        return await authFetch('/api/lookup/product_lookup', {
            method: 'POST',
            body: JSON.stringify({
                product_code: productCode,
                address_id: addressId
            })
        });
    } catch (error) {
        // Re-throw the error with the original message
        // The authFetch function already handles specific error messages from the API
        throw error;
    }
};

// Lookup product by name
export const lookupProductByName = async (productName, productCost, addressId) => {
    if (!productName) {
        throw new Error('Product name is required for lookup.');
    }

    // Validate product name (at least 5 characters)
    if (productName.trim().length < 5) {
        throw new Error('Product name must be at least 5 characters long.');
    }

    // Validate product cost if provided
    if (productCost && isNaN(parseFloat(productCost))) {
        throw new Error('Invalid product cost. Please enter a valid number.');
    }

    return authFetch('/api/lookup/product_lookup_by_name', {
        method: 'POST',
        body: JSON.stringify({
            product_name: productName,
            product_cost: productCost,
            address_id: addressId
        })
    });
};

/**
 * Look up customers by phone numbers, email addresses, and/or postcodes
 * @param {Array} phones - Array of phone numbers to search
 * @param {Array} emails - Array of email addresses to search
 * @param {Array} postcodes - Array of postcodes to search (optional)
 * @returns {Promise<Array>} - Array of customer objects
 */
export const lookupCustomer = async (phones = [], emails = [], postcodes = []) => {
    // Validate that at least one search parameter is provided
    if (phones.length === 0 && emails.length === 0 && postcodes.length === 0) {
        throw new Error('At least one phone number, email address, or postcode is required for customer lookup.');
    }

    try {
        // Make the API call
        const data = await authFetch(`/api/lookup/customer_lookup`, {
            method: 'POST',
            body: JSON.stringify({
                phone_numbers: phones,
                emails: emails,
                postcodes: postcodes
            })
        });

        // The API returns the array directly, not wrapped in an object
        // Check if data is an array (direct response) or if it has a customers property
        return Array.isArray(data) ? data : data.customers || [];
    } catch (error) {
        // Re-throw the error with the original message
        // The authFetch function already handles specific error messages from the API
        throw error;
    }
};

/**
 * Skip PDF review
 * @param {number} pdfId - The PDF ID to skip
 * @param {string} reason - Reason for skipping (optional)
 * @param {string} comments - Additional comments (optional)
 * @returns {Promise<Object>} - Response data from the server
 */
export const skipPdfReview = async (pdfId, reason = '', comments = '') => {
    if (!pdfId) {
        throw new Error('PDF ID is required to skip review.');
    }

    try {
        return await authFetch(`/api/pdfs/skip/${pdfId}`, {
            method: 'POST',
            body: JSON.stringify({
                reason: reason,
                comments: comments
            })
        });
    } catch (error) {
        // Re-throw the error with the original message
        // The authFetch function already handles specific error messages from the API
        throw error;
    }
};

/**
 * Upload PDF file to the server
 * @param {File} file - The PDF file to upload
 * @param {Function} onProgress - Optional callback for upload progress (0-100)
 * @returns {Promise<Object>} - Response data from the server
 */
export const uploadPdf = async (file, onProgress) => {
    // Validate file exists
    if (!file) {
        throw new Error('PDF file is required for upload.');
    }

    // Validate file type
    if (file.type !== 'application/pdf') {
        throw new Error('Only PDF files are allowed.');
    }

    // Validate file size (limit to 10MB)
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > MAX_FILE_SIZE) {
        throw new Error('File size exceeds the limit of 10MB.');
    }

    // Get authentication token
    const token = await getToken();
    if (!token) {
        throw new Error('Authentication token not found');
    }

    // Create FormData object
    const formData = new FormData();
    formData.append('file', file);

    // Create XMLHttpRequest to track upload progress
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // Setup progress tracking
        if (typeof onProgress === 'function') {
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const percentComplete = Math.round((event.loaded / event.total) * 100);
                    onProgress(percentComplete);
                }
            });
        }

        // Setup completion handler
        xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (error) {
                    reject(new Error('Server returned an unexpected response. Please try again later.'));
                }
            } else {
                // Handle error responses
                try {
                    const errorData = JSON.parse(xhr.responseText);
                    reject(new Error(errorData.error || 'Upload failed. Please try again.'));
                } catch (parseError) {
                    // If response isn't valid JSON
                    reject(new Error('Upload failed. Please try again.'));
                }
            }
        };

        // Setup error handler
        xhr.onerror = () => {
            reject(new Error('Network error occurred. Please check your connection and try again.'));
        };

        // Setup timeout handler
        xhr.ontimeout = () => {
            reject(new Error('Request timed out. Please try again.'));
        };

        // Open and send the request
        xhr.open('POST', '/api/pdfs/upload', true);
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        xhr.timeout = 60000; // 60 seconds timeout
        xhr.send(formData);
    });
};