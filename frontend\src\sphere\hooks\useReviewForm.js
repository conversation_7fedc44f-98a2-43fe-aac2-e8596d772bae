// frontend/src/sphere/hooks/useReviewForm.js
import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { fetchPdfReviewData } from "../services/pdfService";
import { initializeFormValues } from "../utils/formDataUtils";

export const useReviewForm = (pdfId) => {
    const [reviewData, setReviewData] = useState(null);
    const [formData, setFormData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const loadReviewData = async () => {
            setLoading(true);
            setReviewData(null);
            setFormData(null);
            setError(null);
            try {
                const data = await fetchPdfReviewData(pdfId);
                setReviewData(data);
                const initialFormValues = initializeFormValues(data);
                setFormData(initialFormValues);
            } catch (err) {
                console.error("Error fetching PDF review data:", err);
                const errorMessage = err.message || "Failed to load PDF review data";
                toast.error(errorMessage);
                setError(errorMessage);
            } finally {
                setLoading(false);
            }
        };

        if (pdfId) {
            loadReviewData();
        } else {
            setLoading(false);
            setError("No PDF ID was provided.");
        }
    }, [pdfId]);

    const updateFormData = (fieldKey, value) => {
        setFormData((prevData) => ({
            ...prevData,
            [fieldKey]: value,
        }));
    };

    const updateProductField = (rowIndex, columnKey, value) => {
        setFormData((prevData) => {
            const updatedProducts = prevData.products.map((product, index) => {
                if (index === rowIndex) {
                    return {
                        ...product,
                        [columnKey]: value,
                    };
                }
                return product;
            });
            return {
                ...prevData,
                products: updatedProducts,
            };
        });
    };

    const deleteProductRow = (rowIndex) => {
        setFormData((prevData) => {
            const updatedProducts = prevData.products.filter(
                (_, index) => index !== rowIndex,
            );
            return {
                ...prevData,
                products: updatedProducts,
            };
        });
        toast.success("Product row removed");
    };

    const addNewProductRow = () => {
        setFormData((prevData) => {
            // Create a new empty product object
            const newProduct = {
                Product_Code: "",
                Product_Name: "",
                Product_Qty: "",
                Unit_Cost: "",
                Line_Value: ""
            };

            // Add the new product to the products array
            return {
                ...prevData,
                products: [...prevData.products, newProduct]
            };
        });
        toast.success("New product row added");
    };

    return {
        reviewData,
        formData,
        loading,
        error,
        updateFormData,
        setFormData,
        updateProductField,
        deleteProductRow,
        addNewProductRow,
    };
};
