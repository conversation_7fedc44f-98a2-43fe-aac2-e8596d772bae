// frontend/src/shared/pages/LoginPage.jsx

import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const LoginPage = () => {
    const { signInWithAzure, user } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        // If user is already authenticated, redirect to dashboard
        if (user) {
            navigate('/dashboard');
        }
    }, [user, navigate]);

    const handleSignIn = async () => {
        await signInWithAzure();
    };

    return (
        <div className="login-container">
            <div className="login-card">
                <h1>Welcome</h1>
                <p>Sign in to access your dashboard</p>

                <button
                    className="login-button azure"
                    onClick={handleSignIn}
                >
                    Sign in with Microsoft
                </button>
            </div>
        </div>
    );
};

export default LoginPage;
