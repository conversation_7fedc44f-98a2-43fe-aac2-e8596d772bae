# Public Assets for Order Insights and Management

This directory contains the public assets for the Order Insights and Management application.

## Files

- `index.html`: The main HTML template for the application
- `logo.png`: The organization logo used as both favicon and app icon
- `manifest.json`: Web app manifest for PWA functionality
- `robots.txt`: Instructions for search engine crawlers

## Notes

- The `%PUBLIC_URL%` placeholders in index.html will be replaced with the actual URL path during the build process
- Only files in this directory can be referenced from the HTML
- If you need to add more static assets, place them in this directory

## Docker Deployment

When using Docker, these public assets are:

1. Included in the build process during the first stage of the multi-stage Dockerfile
2. Copied to the Nginx container in the `/usr/share/nginx/html` directory
3. Served by <PERSON><PERSON><PERSON> on port 9000
