// frontend/copy-version.js
const fs = require('fs');
const path = require('path');

// Read the package.json file
const packageJson = require('./package.json');

// Create a simplified version with only the version information
const versionInfo = {
  version: packageJson.version,
  name: packageJson.name
};

// Write the simplified version to the public folder
fs.writeFileSync(
  path.join(__dirname, 'public', 'package.json'),
  JSON.stringify(versionInfo, null, 2)
);
