// frontend/src/sphere/components/EditableProductCell.jsx
import React from 'react';

const EditableProductCell = ({
    value,
    onChange,
    rowIndex,
    columnKey,
    inputType = 'text',
    readOnly = false
}) => {
    const handleChange = (e) => {
        if (!readOnly) {
            onChange(rowIndex, columnKey, e.target.value);
        }
    };

    // Handle null or undefined values gracefully for the input
    const displayValue = value === null || value === undefined ? '' : value;

    return (
        <input
            type={inputType}
            value={displayValue}
            onChange={handleChange}
            className={`form-control product-cell-input ${readOnly ? 'read-only' : ''}`}
            aria-label={`Product ${rowIndex + 1} ${columnKey.replace("_", " ")}`}
            // Use a random name to prevent Chrome from recognizing the field pattern
            name={`product_${columnKey}_${Math.random().toString(36).substring(2, 10)}`}
            readOnly={readOnly}
            disabled={readOnly}
            autoComplete="new-password" // This tricks Chrome better than "off"
            autoCorrect="off"
            autoCapitalize="off"
            spellCheck="false"
            data-form-type="other"
            data-lpignore="true" // Ignores LastPass autofill
        />
    );
};

export default EditableProductCell;
