# backend/app/services/doc_processing/pdf_to_imgs.py

import logging
import fitz  # PyMuPDF for PDF manipulation
from PIL import Image

logger = logging.getLogger(__name__)


class PDFConverter:
    def __init__(self):
        pass

    def convert_pdf_to_images(self, pdf_file_path):
        try:
            # Convert a single PDF file into individual images and return them as a list of variables
            doc = fitz.open(pdf_file_path)
            images = []

            # Convert each page of the PDF to an image
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                zoom = 300 / 72  # Set zoom level for high-quality images
                magnify = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=magnify)
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                images.append(img)

                logger.info(
                    f"Converted page {page_num + 1} of PDF '{pdf_file_path}' to image"
                )
            return images
        except Exception as e:
            logger.error(f"Error converting PDF to images: {e}")
            raise e
