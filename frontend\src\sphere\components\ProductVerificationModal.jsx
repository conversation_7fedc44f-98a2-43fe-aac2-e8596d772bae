// frontend/src/sphere/components/ProductVerificationModal.jsx
import {
    FaCheckCircle,
    FaTimesCircle,
    FaExclamationTriangle,
    FaSpinner,
    FaSearch,
    FaTag,
    FaPoundSign
} from 'react-icons/fa';
import '../styles/ProductVerificationModal.css';

const ProductVerificationModal = ({
    isOpen,
    onClose,
    loading,
    product,
    searchResults,
    originalProduct,
    onSelectProduct,
    onSearchByName,
    error
}) => {
    if (!isOpen) return null;

    // Helper function to determine if values match
    const doValuesMatch = (val1, val2, isPrice = false) => {
        if (!val1 && !val2) return true;
        if (!val1 || !val2) return false;

        if (isPrice) {
            // For prices, extract numeric values and compare as numbers
            const numericVal1 = parseFloat(String(val1).replace(/[^0-9.,]/g, '').replace(',', '.'));
            const numericVal2 = parseFloat(String(val2).replace(/[^0-9.,]/g, '').replace(',', '.'));

            // Check if both values are valid numbers
            if (isNaN(numericVal1) || isNaN(numericVal2)) {
                return false;
            }

            // Compare with a small tolerance for floating point precision
            const tolerance = 0.01; // 1 penny/cent tolerance
            return Math.abs(numericVal1 - numericVal2) < tolerance;
        } else {
            // For non-price values, compare as trimmed strings
            const str1 = String(val1).trim().toLocaleLowerCase();
            const str2 = String(val2).trim().toLocaleLowerCase();
            return str1 === str2;
        }
    };

    // Determine status for each field if we have a product result
    const getFieldStatus = (field) => {
        if (!product || !originalProduct) return null;

        let originalValue, fetchedValue, isPrice = false;

        switch (field) {
            case 'code':
                originalValue = originalProduct.Product_Code;
                fetchedValue = product.ProductCode;
                break;
            case 'name':
                originalValue = originalProduct.Product_Name;
                fetchedValue = product.ProductName;
                break;
            case 'price':
                originalValue = originalProduct.Unit_Cost;
                fetchedValue = product.Price;
                isPrice = true;
                break;
            default:
                return null;
        }

        return doValuesMatch(originalValue, fetchedValue, isPrice);
    };

    // Render loading state
    if (loading) {
        return (
            <div className="product-verification-modal-overlay">
                <div className="product-verification-modal">
                    <div className="modal-header">
                        <h2>Verifying Product</h2>
                        <button className="close-button" onClick={onClose}>&times;</button>
                    </div>
                    <div className="modal-body loading-state">
                        <FaSpinner className="spinner" />
                        <p>Looking up product information...</p>
                    </div>
                </div>
            </div>
        );
    }

    // Render error state
    if (error) {
        // Determine if we should show the search by name button
        // Show the button for "not found", "Invalid product code", and "Product code is required" errors
        // We don't need to show the button if we're already showing search results (which happens when code is empty but name exists)
        const showSearchByName = originalProduct && originalProduct.Product_Name &&
            (error.includes("not found") || error.includes("Invalid product code") || error.includes("Product code is required")) &&
            !searchResults;

        // Determine error type for better visual feedback
        let errorIcon = <FaTimesCircle className="error-icon" />;
        let errorTitle = "Product Verification Failed";
        let errorMessage = error;
        let errorHint = null;

        if (error.includes("not found")) {
            errorTitle = "Product Code Not Found";
            errorHint = "The product code could not be found in our database.";
        } else if (error.includes("Invalid product code format")) {
            errorTitle = "Invalid Product Code Format";
            errorHint = "Product codes must contain only letters, numbers, and hyphens.";
        } else if (error.includes("Product code is required")) {
            errorTitle = "Missing Product Code";
            errorHint = "You can search by product name instead.";
        } else if (error.includes("server error")) {
            errorTitle = "Server Error";
            errorHint = "There was a problem with the server. Please try again later or contact support.";
        }

        return (
            <div className="product-verification-modal-overlay">
                <div className="product-verification-modal">
                    <div className="modal-header">
                        <h2>{errorTitle}</h2>
                        <button className="close-button" onClick={onClose}>&times;</button>
                    </div>
                    <div className="modal-body error-state">
                        {errorIcon}
                        <p>{errorMessage}</p>
                        {errorHint && <p className="error-hint">{errorHint}</p>}
                        {showSearchByName && (
                            <div className="error-actions">
                                <p className="search-suggestion">
                                    {error.includes("Product code is required")
                                        ? "You can search directly by product name:"
                                        : "Try searching by product name instead:"}
                                </p>
                                <button
                                    className="search-by-name-button"
                                    onClick={onSearchByName}
                                >
                                    <FaSearch /> Search by Name
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    // Render search results
    if (searchResults && searchResults.length > 0) {
        return (
            <div className="product-verification-modal-overlay">
                <div className="product-verification-modal search-results-modal">
                    <div className="modal-header">
                        <h2><FaSearch /> Product Search Results</h2>
                        <button className="close-button" onClick={onClose}>&times;</button>
                    </div>
                    <div className="modal-body">
                        {originalProduct && (
                            <div className="original-product-summary">
                                <h3>Original Product Details</h3>
                                <div className="original-product-details">
                                    <div className="original-detail">
                                        <span className="detail-label"><FaTag size={12} /> Code:</span>
                                        <span className="detail-value">{originalProduct.Product_Code || 'N/A'}</span>
                                    </div>
                                    <div className="original-detail">
                                        <span className="detail-label"><FaSearch size={12} /> Name:</span>
                                        <span className="detail-value">{originalProduct.Product_Name || 'N/A'}</span>
                                    </div>
                                    <div className="original-detail">
                                        <span className="detail-label"><FaPoundSign size={12} /> Price:</span>
                                        <span className="detail-value">
                                            {originalProduct.Unit_Cost
                                                ? `£${parseFloat(originalProduct.Unit_Cost.replace(/[^0-9.]/g, '')).toFixed(2)}`
                                                : 'N/A'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        )}
                        <p>Found <strong>{searchResults.length}</strong> products matching your search:</p>
                        <div className="search-results-container">
                            {searchResults.map((result, index) => (
                                <div key={index} className="search-result-item">
                                    <div className="search-result-details">
                                        <div className="search-result-code"><FaTag size={12} /> {result.ProductCode}</div>
                                        <div className="search-result-name" title={result.ProductName}>{result.ProductName}</div>
                                        <div className="search-result-price"><FaPoundSign size={12} />{parseFloat(result.Price).toFixed(2)}</div>
                                    </div>
                                    <div className="search-result-action">
                                        <button
                                            className="select-product-button"
                                            onClick={() => onSelectProduct(result)}
                                        >
                                            <FaCheckCircle size={14} style={{ marginRight: '6px' }} /> Select
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Render single product verification result
    if (product) {
        const codeStatus = getFieldStatus('code');
        const nameStatus = getFieldStatus('name');
        const priceStatus = getFieldStatus('price');

        const allMatch = codeStatus && nameStatus && priceStatus;

        return (
            <div className="product-verification-modal-overlay">
                <div className="product-verification-modal">
                    <div className="modal-header">
                        <h2><FaCheckCircle style={{ marginRight: '8px' }} /> Product Verification</h2>
                        <button className="close-button" onClick={onClose}>&times;</button>
                    </div>
                    <div className="modal-body">
                        <div className="verification-summary">
                            {allMatch ? (
                                <div className="verification-success">
                                    <FaCheckCircle className="success-icon" />
                                    <p>All product details match!</p>
                                </div>
                            ) : (
                                <div className="verification-warning">
                                    <FaExclamationTriangle className="warning-icon" />
                                    <p>Some product details don't match.</p>
                                </div>
                            )}
                        </div>

                        <div className="verification-details">
                            <div className="verification-row">
                                <div className="field-label"><FaTag size={12} /> Code:</div>
                                <div className="field-value original">{originalProduct?.Product_Code || 'N/A'}</div>
                                <div className="field-value fetched">{product.ProductCode}</div>
                                <div className="field-status">
                                    {codeStatus === true ? (
                                        <FaCheckCircle className="match-icon" />
                                    ) : codeStatus === false ? (
                                        <FaTimesCircle className="mismatch-icon" />
                                    ) : null}
                                </div>
                            </div>

                            <div className="verification-row">
                                <div className="field-label"><FaSearch size={12} /> Name:</div>
                                <div className="field-value original">{originalProduct?.Product_Name || 'N/A'}</div>
                                <div className="field-value fetched">{product.ProductName}</div>
                                <div className="field-status">
                                    {nameStatus === true ? (
                                        <FaCheckCircle className="match-icon" />
                                    ) : nameStatus === false ? (
                                        <FaTimesCircle className="mismatch-icon" />
                                    ) : null}
                                </div>
                            </div>

                            <div className="verification-row">
                                <div className="field-label"><FaPoundSign size={12} /> Price:</div>
                                <div className="field-value original">
                                    {originalProduct?.Unit_Cost
                                        ? `£${parseFloat(originalProduct.Unit_Cost.replace(/[^0-9.]/g, '')).toFixed(2)}`
                                        : 'N/A'}
                                </div>
                                <div className="field-value fetched">£{parseFloat(product.Price).toFixed(2)}</div>
                                <div className="field-status">
                                    {priceStatus === true ? (
                                        <FaCheckCircle className="match-icon" />
                                    ) : priceStatus === false ? (
                                        <FaTimesCircle className="mismatch-icon" />
                                    ) : null}
                                </div>
                            </div>
                        </div>

                        <div className="verification-actions">
                            <button
                                className="select-product-button"
                                onClick={() => onSelectProduct(product)}
                            >
                                <FaCheckCircle size={14} style={{ marginRight: '6px' }} /> Use This Product
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Fallback empty state
    return (
        <div className="product-verification-modal-overlay">
            <div className="product-verification-modal">
                <div className="modal-header">
                    <h2>Product Verification</h2>
                    <button className="close-button" onClick={onClose}>&times;</button>
                </div>
                <div className="modal-body">
                    <p>No product information available.</p>
                </div>
            </div>
        </div>
    );
};

export default ProductVerificationModal;
