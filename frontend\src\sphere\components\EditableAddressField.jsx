// frontend/src/sphere/components/EditableAddressField.jsx
import { useState } from 'react';
import { FaTrashAlt } from 'react-icons/fa';
import '../../shared/styles/CommonActionButtons.css';
import '../styles/EditableAddressField.css';

const EditableAddressField = ({
    fieldKey,
    label,
    value,
    onChange,
    onClear,
    inputType = 'text',
    placeholderText = '',
    showLabel = true,
    required = false,
    error = null,
    showValidationErrors = false,
}) => {
    const [isDragging, setIsDragging] = useState(false);
    const [isDragOver, setIsDragOver] = useState(false);

    const hasError = error && showValidationErrors;
    const rootClassName = `editable-address-field ${!value && placeholderText ? "is-empty-optional" : ""} ${!showLabel ? "no-internal-label" : ""
        } ${required ? "required-field" : ""} ${hasError ? "has-error" : ""} ${isDragOver ? "drag-over" : ""}`;

    const handleDragStart = (e) => {
        // Set the data being dragged - the current field value
        if (value) {
            // Store the field key as source identifier
            e.dataTransfer.setData("text/plain", value);
            e.dataTransfer.setData("sourceFieldKey", fieldKey);
            e.dataTransfer.effectAllowed = "move"; // Changed to "move" since we're moving content

            // Create a custom drag image
            const dragPreview = document.createElement('div');
            dragPreview.className = 'address-drag-preview';
            dragPreview.textContent = value;

            // Append to body, set as drag image, then remove
            document.body.appendChild(dragPreview);
            e.dataTransfer.setDragImage(dragPreview, 10, 10);

            // Remove after a short delay (needs to exist during drag start)
            setTimeout(() => {
                document.body.removeChild(dragPreview);
            }, 100);

            setIsDragging(true);
        } else {
            // Prevent dragging if there's no content
            e.preventDefault();
        }
    };

    const handleDragEnd = () => {
        setIsDragging(false);
    };

    const handleDragOver = (e) => {
        // Prevent default to allow drop
        e.preventDefault();

        // Get the source field key
        const sourceFieldKey = e.dataTransfer.getData("sourceFieldKey");

        // If dragging over the same field, show "no-drop" cursor
        if (sourceFieldKey === fieldKey) {
            e.dataTransfer.dropEffect = "none";
        } else {
            e.dataTransfer.dropEffect = "move";
        }
    };

    const handleDragEnter = (e) => {
        e.preventDefault();

        // Get the source field key
        const sourceFieldKey = e.dataTransfer.getData("sourceFieldKey");

        // Only show drag-over effect if not the same field
        if (sourceFieldKey !== fieldKey) {
            setIsDragOver(true);
        }
    };

    const handleDragLeave = () => {
        setIsDragOver(false);
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragOver(false);

        const droppedText = e.dataTransfer.getData("text/plain");
        const sourceFieldKey = e.dataTransfer.getData("sourceFieldKey");

        if (!droppedText) return;

        // If dropped on the same field, do nothing
        if (sourceFieldKey === fieldKey) {
            return;
        }

        // If the target field already has content, append with comma separator
        if (value) {
            onChange(fieldKey, `${value}, ${droppedText}`);
        } else {
            // Otherwise just set the value
            onChange(fieldKey, droppedText);
        }

        // Clear the source field
        if (sourceFieldKey) {
            onChange(sourceFieldKey, "");
        }
    };

    return (
        <div className={`${rootClassName} ${showLabel ? "form-group" : ""}`}>
            {showLabel && <label htmlFor={fieldKey}>{label}</label>}
            <div className="address-input-interactive-wrapper">
                <input
                    type={inputType}
                    id={fieldKey} // ID for the external label to point to
                    name={`address_${Math.random().toString(36).substring(2, 10)}`} // Random name to confuse Chrome
                    className={`form-control ${required ? 'required-input' : ''} ${hasError ? 'invalid' : ''} ${isDragging ? 'dragging' : ''}`}
                    value={value || ''}
                    onChange={(e) => onChange(fieldKey, e.target.value)}
                    placeholder={placeholderText}
                    autoComplete="new-password" // This tricks Chrome better than "off"
                    autoCorrect="off"
                    autoCapitalize="off"
                    spellCheck="false"
                    data-form-type="other"
                    data-lpignore="true" // Ignores LastPass autofill
                    data-required={required}
                    aria-required={required}
                    aria-invalid={hasError}
                    draggable="true"
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                    onDragOver={handleDragOver}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                />
                {value && onClear && (
                    <button
                        type="button"
                        onClick={(e) => {
                            e.stopPropagation();
                            onClear(fieldKey);
                        }}
                        className="address-field-clear-btn-inner icon-action-button"
                        title={`Clear ${label}`} // Use label prop for title
                    >
                        <FaTrashAlt />
                    </button>
                )}
            </div>
        </div>
    );
};

export default EditableAddressField;