// frontend/src/sphere/components/CustomerInformation.jsx
import { FaUserPlus, FaUser, FaEnvelope, FaPhone, FaBuilding, FaMapMarkerAlt, FaTimes } from 'react-icons/fa';
import '../styles/CustomerInformation.css';

const CustomerInformation = ({
    customer,
    onFindCustomer,
    onUnselectCustomer,
    isRequired = true,
    validationFailed = false
}) => {
    // If no customer is selected, show only the Find Customer button
    if (!customer) {
        return (
            <section className="section-card customer-information-section">
                <div className="customer-information-header">
                    <h2>
                        Customer Information
                        {isRequired && <span className="required-asterisk">*</span>}
                    </h2>
                    <button
                        type="button"
                        onClick={onFindCustomer}
                        className="action-button find-customer-button"
                        title="Find existing customer"
                        aria-label="Find existing customer"
                    >
                        <FaUserPlus /> Find Customer
                    </button>
                </div>
                <div className="no-customer-selected">
                    <p className={validationFailed ? 'customer-required-text' : ''}>
                        No customer selected. Click "Find Customer" to search for an existing customer.
                    </p>
                </div>
            </section>
        );
    }

    // Format the customer address
    const formattedAddress = [
        customer['Address 1'],
        customer['Address 2'],
        customer['Address 3'],
        customer.City,
        customer.County,
        customer.PostCode,
        customer.Country
    ].filter(Boolean).join(', ');

    // Get the primary phone number
    const primaryPhone = customer.MobileTel || customer.HomeTel || customer.WorkTel || 'N/A';

    return (
        <section className="section-card customer-information-section">
            <div className="customer-information-header">
                <h2>Customer Information</h2>
                <div className="customer-actions">
                    <button
                        type="button"
                        onClick={onUnselectCustomer}
                        className="action-button unselect-customer-button"
                        title="Remove customer"
                        aria-label="Remove customer"
                    >
                        <FaTimes /> Remove
                    </button>
                    <button
                        type="button"
                        onClick={onFindCustomer}
                        className="action-button find-customer-button"
                        title="Find different customer"
                        aria-label="Find different customer"
                    >
                        <FaUserPlus /> Change Customer
                    </button>
                </div>
            </div>
            <div className="customer-details">
                <div className="customer-primary-info">
                    <div className="customer-name-company">
                        <div className="customer-name">
                            <FaUser size={16} />
                            <span>{customer.Contact || 'N/A'}</span>
                        </div>
                        <div className="customer-company">
                            <FaBuilding size={16} />
                            <span>{customer.Company || 'N/A'}</span>
                        </div>
                    </div>
                    <div className="customer-contact">
                        <div className="customer-email">
                            <FaEnvelope size={14} />
                            <span>{customer.Email || 'N/A'}</span>
                        </div>
                        <div className="customer-phone">
                            <FaPhone size={14} />
                            <span>{primaryPhone}</span>
                        </div>
                    </div>
                </div>
                <div className="customer-address">
                    <FaMapMarkerAlt size={14} />
                    <span>{formattedAddress}</span>
                </div>
                <div className="address-code">
                    <strong>Address code:</strong> {customer.AddressCode}
                </div>
            </div>
        </section>
    );
};

export default CustomerInformation;
