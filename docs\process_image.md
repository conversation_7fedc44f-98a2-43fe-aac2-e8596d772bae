# Process Image Script

This script processes an image file to extract OCR (Optical Character Recognition) annotations and converts them into a format suitable for training machine learning models. The extracted annotations include bounding boxes, text, and labels.

## Dependencies

- `json`
- `PIL` (Pillow)
- `src.model_dev.utils.create_llmv3_dataset` (OCRProcessor)
- `src.model_dev.utils.update_llmv3_dataset` (JSONUpdater)

## Label Mapping

The script uses a predefined label mapping to convert textual labels into numerical values. The mapping is defined as follows:

```python
label_mapping = {
    "PO_Number": 1,
    "Contact_Name": 2,
    "Contact_Phone": 3,
    "Contact_Email": 4,
    "Delivery_Address": 5,
    "Invoice_Address": 6,
    "Product_Code": 7,
    "Product_Qty": 8,
    "Invoice_Email": 9,
    "Product_Name": 0,
    "Unit_Cost": 10,
    "Line_Value": 11,
    "Total_Value": 12,
    "Ignore_Text": 13,
    "Order_Date": 14,
    "Invoice_Phone": 15,
}
```

## Class: ImageProcessor

### __init__(self, product_codes_file)
Initialises the `ImageProcessor` class with the OCR processor and JSON updater.

- **Parameters**:
  - `product_codes_file` (str): Path to the file containing product codes.

### convert_bounding_box(self, x, y, width, height, original_width, original_height, new_width, new_height)
Converts the given bounding box coordinates to normalised values.

- **Parameters**:
  - `x` (float): The x-coordinate of the top-left corner of the bounding box in percentages.
  - `y` (float): The y-coordinate of the top-left corner of the bounding box in percentages.
  - `width` (float): The width of the bounding box in percentages.
  - `height` (float): The height of the bounding box in percentages.
  - `original_width` (int): The original width of the image.
  - `original_height` (int): The original height of the image.
  - `new_width` (int): The new width of the image after resizing.
  - `new_height` (int): The new height of the image after resizing.

- **Returns**:
  - `bbox` (list): A list of four coordinates `[x1, y1, x2, y2]` normalised to [0, 1].

### process_image(self, file_path)
Processes a single image file to extract OCR annotations and converts them into the desired format.

- **Parameters**:
  - `file_path` (str): Path to the image file.

- **Returns**:
  - `output` (list): List of dictionaries containing the processed annotations.

## Main Script Execution

The main block of the script is responsible for:

1. Initialising the `ImageProcessor` class with the path to the product codes file.
2. Calling the `process_image` method with the path to the image file.

## Usage

Run the script using Python:

```bash
python process_image.py
```

## Important Notes

- The `OCRProcessor` and `JSONUpdater` classes are imported from external modules.
- The script processes the annotations extracted from the image, converts bounding box coordinates, and maps textual labels to numerical values.
- Any unknown labels encountered during the processing are collected and printed for further review.

## Error Handling

The script includes error handling for opening image files and ensures that the annotations are processed correctly even if some data is missing or malformed.
