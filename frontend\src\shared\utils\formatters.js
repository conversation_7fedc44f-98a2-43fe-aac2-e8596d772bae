// frontend/src/shared/utils/formatters.js
// Utility functions for formatting data in the application

/**
 * This file contains utility functions for formatting various types of data
 * consistently throughout the application. These functions handle edge cases
 * and provide a consistent interface for formatting.
 */

/**
 * Formats text in title case (first letter of each word capitalized)
 * Handles text with parentheses by formatting inside them as well
 *
 * @param {string} text - The text to format
 * @returns {string} - The formatted text in title case
 */
export const formatTitleCase = (text) => {
    if (!text) return '-';

    // Handle text with parentheses separately
    return text.replace(/\b\w+\b|\([^)]*\)/g, (match) => {
        // If it's a parenthetical expression
        if (match.startsWith('(') && match.endsWith(')')) {
            // Format inside parentheses
            const inside = match.slice(1, -1);
            const formattedInside = inside.replace(/\b\w+\b/g, word =>
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            );
            return `(${formattedInside})`;
        }

        // Regular word formatting
        return match.charAt(0).toUpperCase() + match.slice(1).toLowerCase();
    });
};

/**
 * Formats a currency value with the £ symbol, thousands separators, and 2 decimal places
 *
 * @param {number|string} value - The value to format
 * @param {string} [currencySymbol='£'] - The currency symbol to use
 * @param {number} [decimalPlaces=2] - Number of decimal places to show
 * @returns {string} - The formatted currency string
 */
export const formatCurrency = (value, currencySymbol = '£', decimalPlaces = 2) => {
    // Handle null, undefined, or empty string
    if (value === null || value === undefined || value === '') {
        return `${currencySymbol}0.${'0'.repeat(decimalPlaces)}`;
    }

    // Convert to number and handle NaN
    const numValue = Number(value);
    if (isNaN(numValue)) {
        return `${currencySymbol}0.${'0'.repeat(decimalPlaces)}`;
    }

    // Format with Intl.NumberFormat for proper locale handling
    // This automatically adds thousands separators and handles decimal places
    const formatter = new Intl.NumberFormat('en-GB', {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces
    });

    return `${currencySymbol}${formatter.format(numValue)}`;
};

/**
 * Formats a number with thousands separators
 *
 * @param {number|string} value - The number to format
 * @param {number} [decimalPlaces=0] - Number of decimal places
 * @returns {string} - The formatted number string
 */
export const formatNumber = (value, decimalPlaces = 0) => {
    if (value === null || value === undefined || value === '') return '-';

    const numValue = Number(value);
    if (isNaN(numValue)) return '-';

    return new Intl.NumberFormat('en-GB', {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces
    }).format(numValue);
};

/**
 * Truncates text to a specified length and adds ellipsis if needed
 *
 * @param {string} text - The text to truncate
 * @param {number} [maxLength=50] - Maximum length before truncation
 * @returns {string} - The truncated text
 */
export const truncateText = (text, maxLength = 50) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;

    return text.substring(0, maxLength) + '...';
};