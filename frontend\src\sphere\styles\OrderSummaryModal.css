/* frontend/src/sphere/styles/OrderSummaryModal.css */

.order-summary-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
  transition: all 0.2s ease-in-out;
  animation: fadeIn 0.2s forwards;
}

.order-summary-modal {
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 8px 24px var(--shadow-color);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
  border: 1px solid var(--border-color-light);
  transform: scale(0.95);
  opacity: 0;
  animation: scaleIn 0.2s 0.1s forwards;
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: var(--border-color) transparent; /* For Firefox */
}

/* Custom scrollbar for WebKit browsers */
.order-summary-modal::-webkit-scrollbar {
  width: 8px;
}

.order-summary-modal::-webkit-scrollbar-track {
  background: transparent;
}

.order-summary-modal::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
  border: 2px solid transparent;
}

.order-summary-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
}

.order-summary-modal .modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-summary-modal .success-icon {
  color: var(--message-success-text);
}

.order-summary-modal .close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--button-secondary-bg);
  border: none;
  width: 28px;
  height: 28px;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.order-summary-modal .close-button:hover {
  color: var(--text-color);
  background-color: var(--button-secondary-hover-bg);
}

.order-summary-modal .modal-body {
  padding: 20px;
}

.order-summary-modal .modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
}

.order-summary-modal .confirm-button {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px var(--shadow-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-summary-modal .confirm-button:hover {
  background-color: var(--button-primary-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.summary-section {
  margin-bottom: 24px;
}

.summary-section h3 {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-lighter);
}

.summary-field {
  display: flex;
  margin-bottom: 8px;
}

.field-label {
  font-weight: 600;
  width: 120px;
  color: var(--text-color);
}

.field-value {
  flex: 1;
  color: var(--text-color);
}

.address-lines {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.address-line {
  color: var(--text-color);
}

.products-table-container {
  margin-top: 12px;
  overflow-x: auto;
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: var(--border-color) transparent; /* For Firefox */
}

/* Custom scrollbar for WebKit browsers */
.products-table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.products-table-container::-webkit-scrollbar-track {
  background: transparent;
}

.products-table-container::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
  border: 2px solid transparent;
}

.products-summary-table {
  width: 100%;
  border-collapse: collapse;
}

.products-summary-table th {
  text-align: left;
  padding: 8px 12px;
  background-color: var(--border-color-lighter);
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.9rem;
}

.products-summary-table td {
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color-lighter);
  color: var(--text-color);
}

.products-summary-table tr:last-child td {
  border-bottom: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
