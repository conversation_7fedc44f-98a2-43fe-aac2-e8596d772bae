# app/models/pdf.py

import uuid
from sqlalchemy import (
    Column,
    Integer,
    String,
    Foreign<PERSON>ey,
    DateTime,
    Enum,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.extensions import db


class StatusEnum(enum.Enum):
    unprocessed = "unprocessed"
    under_review = "under_review"
    approved = "approved"
    skipped = "skipped"


class PDF(db.Model):
    __tablename__ = "pdfs"
    id = Column(Integer, primary_key=True)
    uploaded_by = Column(UUID(as_uuid=True), nullable=False)  # Who uploaded the PDF
    origin_name = Column(String(255), nullable=False)  # Original name
    filename = Column(String(255), nullable=False)  # Filename
    storage_path = Column(String(255), nullable=False)  # Where it is stored
    uploaded_at = Column(DateTime, default=datetime.utcnow)  # When it was uploaded
    status = Column(Enum(StatusEnum), default=StatusEnum.unprocessed)  # Status
    completed_by = Column(UUID(as_uuid=True), nullable=True)  # Completed
    completed_at = Column(DateTime, nullable=True)  # When it was processed
    comments = Column(Text, nullable=True)  # Comments during review
    skip_reason = Column(Text, nullable=True)  # Reason for skipping
    skip_comments = Column(Text, nullable=True)  # Comments during skipping

    # Relationships
    images = relationship("Image", order_by="Image.id", back_populates="pdf")
    annotations = relationship(
        "Annotation", order_by="Annotation.id", back_populates="pdf"
    )
    order = relationship("Order", back_populates="pdf", uselist=False)
