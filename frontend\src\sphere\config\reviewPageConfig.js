// frontend/src/sphere/config/reviewPageConfig.js

export const productTableColumns = [
    { key: "Product_Code", label: "Product Code", width: "15%" },
    { key: "Product_Name", label: "Product Name", width: "50%" },
    { key: "Product_Qty", label: "Product Qty", width: "15%" },
    { key: "Unit_Cost", label: "Unit Price", width: "15%" },
    { key: "actions", label: "", width: "5%" },
    // 'Line_Value' is intentionally omitted
];

export const addressFieldConfig = [
    { key: "name", label: "Organisation", type: "text", required: true },
    { key: "addressLine1", label: "House", type: "text", required: false },
    { key: "addressLine2", label: "Street", type: "text", required: true },
    { key: "addressLine3", label: "Locality", type: "text", required: false },
    { key: "addressLine4", label: "Town or City", type: "text", required: false },
    { key: "addressLine5", label: "County", type: "text", required: true },
    { key: "postcode", label: "Postcode", type: "text", required: true },
    { key: "country", label: "Country", type: "text", required: true },
];

export const addressTypesConfig = [
    { prefix: "delivery", title: "Delivery Address" },
    // Temporarily removed invoice address section - can be re-enabled in the future
    // { prefix: "invoice", title: "Invoice Address" },
];

export const UK_POSTCODE_REGEX =
    /^[A-Z]{1,2}[0-9R][0-9A-Z]?\s*[0-9][A-Z]{2}$/i;

export const editableFieldDefinitions = {
    // contact_name: {
    //     label: "Contact Name",
    //     type: "text",
    //     source: "candidates",
    //     optionsKey: "contact_names",
    // },
    contact_email: {
        label: "Contact Email",
        type: "email",
        source: "candidates",
        optionsKey: "contact_emails",
    },
    contact_phone: {
        label: "Contact Phone",
        type: "tel",
        source: "candidates",
        optionsKey: "contact_phones",
    },
    // invoice_email: {
    //     label: "Invoice Email",
    //     type: "email",
    //     source: "candidates",
    //     optionsKey: "invoice_emails",
    // },
    // invoice_phone: {
    //     label: "Invoice Phone",
    //     type: "tel",
    //     source: "candidates",
    //     optionsKey: "invoice_phones",
    // },
    po_number: {
        label: "PO Number",
        type: "text",
        source: "candidates",
        optionsKey: "po_numbers",
    },
    // total_value: {
    //     label: "Total Value",
    //     type: "number",
    //     step: "0.01",
    //     source: "candidates",
    //     optionsKey: "total_values",
    // },
};
