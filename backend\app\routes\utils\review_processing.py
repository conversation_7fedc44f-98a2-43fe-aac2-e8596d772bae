# backend/app/routes/utils/review_processing.py

"""
Business-logic helpers for the React review screen.
No flask imports here - pure Python
"""

from __future__ import annotations
import re
from collections import defaultdict
from typing import Any, Iterable, Dict, List, Set

__all__ = [
    "extract_candidates",
    "extract_addresses",
    "extract_products",
]


def _clean_numbers(txt: str) -> str:
    """
    Remove all non-numeric characters except for commas and periods.
    Correctly handles leading zeros for integers and decimals.
    e.g., "007" -> "7", "0.50" -> "0.50", "0" -> "0"
    """
    # Remove all characters that are not digits, period, or comma
    cleaned_txt = re.sub(r"[^0-9.,]", "", txt)

    if not cleaned_txt:
        return ""

    # Handle numbers with decimal points
    if "." in cleaned_txt:
        parts = cleaned_txt.split(".", 1)
        integer_part = parts[0].lstrip("0")
        if not integer_part:  # Handles cases like ".50" or "0.50"
            integer_part = "0"

        # Ensure we only add decimal part if it exists
        if len(parts) > 1:
            # Further clean decimal part if necessary (e.g. remove trailing non-numerics if any slipped)
            decimal_part = re.sub(r"[^0-9]", "", parts[1])
            return f"{integer_part}.{decimal_part}"
        else:  # Only integer part, possibly became "0" or was like "123."
            return integer_part
    else:  # No decimal point, treat as integer
        stripped = cleaned_txt.lstrip("0")
        return stripped if stripped else "0"  # If "000" became "", return "0"


def _clean_phone(txt: str) -> str:
    """
    Remove all non-numeric characters, remove all leading zeros,
    remove leading '44', and add a leading '0'.
    """
    phone = re.sub(r"[^\d]", "", txt)  # keep digits only
    phone = phone.lstrip("0")  # drop leading zeros
    if phone.startswith("44"):
        phone = phone[2:]
    return f"0{phone}"


def _dedup(seq: Iterable[str]) -> list[str]:
    """
    De-duplicate a list of strings, removing all empty strings in the process.
    """
    seen: Set[str] = set()
    out: list[str] = []
    for item in seq:
        if item and item not in seen:
            seen.add(item)
            out.append(item)
    return out


# 1. Field candidates (PO number, contact email...)
FIELD_LABELS = {
    "PO_Number": "po_numbers",
    "Order_Date": "order_dates",
    "Contact_Name": "contact_names",
    "Contact_Phone": "contact_phones",
    "Contact_Email": "contact_emails",
    "Invoice_Email": "invoice_emails",
    "Invoice_Phone": "invoice_phones",
    "Total_Value": "total_values",
}


def extract_candidates(
    images_annotations: list[list[dict]],
) -> dict[str, list[str]]:
    """
    Returns a dictionary of candidate values for each field.
    """
    buckets: Dict[str, list[str]] = {v: [] for v in FIELD_LABELS.values()}

    for inner_list in images_annotations:
        for page_content_dict in inner_list:
            actual_annotations = page_content_dict.get("annotations", [])
            for ann in actual_annotations:
                label = ann.get("label")
                text = (ann.get("text") or "").strip()
                if not text:
                    continue

                # Normalise certain fields
                if label in ("Contact_Name", "Invoice_Name"):
                    text = text.replace("FAO ", "").replace("FAO", "").strip()
                if label in ("Contact_Phone", "Invoice_Phone"):
                    text = _clean_phone(text)
                if label == "Total_Value":
                    text = _clean_numbers(text)
                if label in ("Contact_Email", "Invoice_Email"):
                    text = text.lower().replace(" ", "").strip()

                bucket = FIELD_LABELS.get(label)
                if bucket:
                    # Check if the text is not already in the bucket's list
                    if text not in buckets[bucket]:
                        buckets[bucket].append(text)

    # Deduplicate while preserving order
    return {k: _dedup(v) for k, v in buckets.items()}


# 2. Address pools
def _split_address_fragment(raw: str) -> list[str]:
    """
    Split an address fragment by commas, but only if there are multiple parts.
    Otherwise return a single-item list containing the original string.
    """
    parts = [p.strip() for p in raw.split(",") if p.strip()]
    return parts or (
        [raw] if raw.strip() else []
    )  # Ensure even single non-empty raw is in list


def extract_addresses(
    images_annotations: list[list[dict]],
) -> dict[str, list[str]]:
    """
    Returns a dictionary of address pools for delivery and invoice addresses,
    with deduplication performed separately for each type.
    """
    delivery_lines: list[str] = []
    invoice_lines: list[str] = []
    delivery_seen: set[str] = set()  # Separate set for delivery addresses
    invoice_seen: set[str] = set()  # Separate set for invoice addresses

    for inner_list in images_annotations:
        for page_content_dict in inner_list:
            actual_annotations = page_content_dict.get("annotations", [])
            for ann in actual_annotations:
                label = ann.get("label")
                if label not in ("Delivery_Address", "Invoice_Address"):
                    continue

                raw_text = (ann.get("text") or "").strip()
                if not raw_text:
                    continue

                fragments = _split_address_fragment(raw_text)

                if label == "Delivery_Address":
                    target_list = delivery_lines
                    target_seen_set = delivery_seen
                else:  # Invoice_Address
                    target_list = invoice_lines
                    target_seen_set = invoice_seen

                for frag in fragments:
                    # frag is already stripped by _split_address_fragment
                    if frag and frag not in target_seen_set:
                        target_seen_set.add(frag)
                        target_list.append(frag)

    return {"delivery": delivery_lines, "invoice": invoice_lines}


#  3. Products
#     – groups annotations by 'group' id (with page-offset correction),
#     – joins multi-part names,
#     – calculates Unit_Cost if missing.

PRODUCT_NUM_LABELS = {"Product_Qty", "Unit_Cost", "Line_Value"}


def _remove_special(text: str) -> str:
    """
    Remove all special characters except for spaces, hyphens, commas, dots, and slashes.
    """
    return re.sub(r"[^A-Za-z0-9\s\-,./]", "", text)


def extract_products(
    images_annotations: list[list[dict]],
) -> list[dict[str, str]]:
    """
    Returns a list of product dictionaries, with joined product names and calculated unit costs.
    """
    products_map: dict[int, dict[str, list[str]]] = defaultdict(
        lambda: defaultdict(list)
    )
    global_group_id_offset = 0

    for inner_list_of_page_content_dicts in images_annotations:
        for page_content_dict in inner_list_of_page_content_dicts:
            current_page_annotations = page_content_dict.get("annotations", [])
            if not current_page_annotations:
                continue

            max_group_in_current_page = 0
            # Pass 1 – collect annotations for the current page_content_dict
            for ann in current_page_annotations:
                original_group_id = ann.get("group")
                if original_group_id is None:
                    continue

                # Track the maximum group ID encountered in this specific page's annotations
                if original_group_id > max_group_in_current_page:
                    max_group_in_current_page = original_group_id

                unique_group_id = original_group_id + global_group_id_offset
                label = ann.get("label")
                text = (ann.get("text") or "").strip()

                if (
                    not text and label not in PRODUCT_NUM_LABELS
                ):  # Allow empty strings for numeric fields if needed by _clean_numbers
                    continue

                if label == "Product_Name":
                    text = _remove_special(text)
                elif label in PRODUCT_NUM_LABELS:
                    text = _clean_numbers(text)

                if label:  # Ensure label exists
                    products_map[unique_group_id][label].append(text)

            # Update the global offset by the max group ID found in this page_content_dict
            # This ensures group IDs from the next page_content_dict (if any) are unique.
            global_group_id_offset += max_group_in_current_page

    # Pass 2 – normalise
    tidy: list[dict[str, str]] = []
    for (
        _gid,
        pdata,
    ) in (
        products_map.items()
    ):  # Use _gid to indicate group id is not directly used here
        prod: dict[str, str] = {}
        for lab, texts in pdata.items():
            uniq = _dedup(texts)
            prod[lab] = " ".join(uniq)

        # Fill Unit_Cost if missing
        # Ensure keys exist and values are not empty before attempting float conversion
        qty_str = prod.get("Product_Qty")
        line_str = prod.get("Line_Value")

        if "Unit_Cost" not in prod or not prod.get(
            "Unit_Cost"
        ):  # Also calculate if Unit_Cost is empty
            if qty_str and line_str:  # Check if strings are not None and not empty
                try:
                    qty = float(qty_str)
                    line = float(line_str)
                    if qty != 0:
                        prod["Unit_Cost"] = f"{line / qty:.2f}"
                    else:
                        prod["Unit_Cost"] = ""  # Or "0.00" or other appropriate value
                except ValueError:
                    # If conversion fails, Unit_Cost might remain empty or be set to ""
                    if "Unit_Cost" not in prod:
                        prod["Unit_Cost"] = ""
            elif "Unit_Cost" not in prod:  # If calculation wasn't possible
                prod["Unit_Cost"] = ""

        # If unit cost still empty and we have unit cost but not qty, just use line value
        if prod["Unit_Cost"] == "" and line_str:
            prod["Unit_Cost"] = line_str

        # Ensure all expected product fields are present, even if empty
        for field_key in [
            "Product_Code",
            "Product_Name",
            "Product_Qty",
            "Unit_Cost",
            "Line_Value",
        ]:
            if field_key not in prod:
                prod[field_key] = ""

        tidy.append(prod)
    return tidy
