# Cluster Management Script

This script manages and queries PDF clusters created by a clustering algorithm. It can load cluster labels, display samples of PDFs in each cluster, query specific images, and determine the largest cluster.

## Dependencies

- `os`
- `pickle`
- `logging`
- `random`
- `argparse`

## Functions

### load_cluster_labels(file_path)
Loads cluster labels and PDF paths from a pickle file.

- **Parameters**:
  - `file_path` (str): Path to the pickle file containing the cluster labels and PDF paths.

- **Returns**:
  - `cluster_labels` (list): List of cluster labels.
  - `pdf_paths` (list): List of PDF file paths.

### get_cluster_n_pdfs(cluster_labels, pdf_paths, cluster_n)
Retrieves PDF paths that belong to a specific cluster.

- **Parameters**:
  - `cluster_labels` (list): List of cluster labels.
  - `pdf_paths` (list): List of PDF file paths.
  - `cluster_n` (int): The cluster number to retrieve PDFs for.

- **Returns**:
  - `cluster_n_pdfs` (list): List of PDF paths belonging to the specified cluster.

### show_sample_pdfs(cluster_labels, pdf_paths, sample_size=5)
Displays a random sample of PDF names in each cluster.

- **Parameters**:
  - `cluster_labels` (list): List of cluster labels.
  - `pdf_paths` (list): List of PDF file paths.
  - `sample_size` (int): The number of sample PDFs to display for each cluster. Default is 5.

### query_image_cluster(image_path, cluster_labels, pdf_paths)
Queries the cluster of a specific image path.

- **Parameters**:
  - `image_path` (str): Path to the image file to query.
  - `cluster_labels` (list): List of cluster labels.
  - `pdf_paths` (list): List of PDF file paths.

### determine_largest_cluster(cluster_labels, pdf_paths)
Determines and prints the largest cluster by the number of PDFs.

- **Parameters**:
  - `cluster_labels` (list): List of cluster labels.
  - `pdf_paths` (list): List of PDF file paths.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the file path for the cluster file.
2. Loads cluster labels and PDF paths from the specified file.
3. Queries the cluster of a specific image path.
4. Determines and prints the largest cluster.
5. Displays a random sample of PDF names in each cluster.

## Usage

Run the script using Python:

```bash
python load_clusters.py
```

### Command-line Arguments

You can add command-line arguments using the `argparse` library to make the script more flexible, for example:

- `--cluster-file`: Path to the cluster file (default: "data/cluster_labels.pkl").
- `--image-path`: Path to the image file to query.

Example:

```bash
python load_clusters.py --cluster-file "data/cluster_labels.pkl" --image-path "data/images/raw/pdf_1/pdf_1_page_1.jpg"
```

## Logging

The script uses the `logging` module to log information about the clustering process, such as the number of PDFs in each cluster, the largest cluster, and the results of specific image queries.

## Error Handling

Errors encountered during the cluster management process should be handled appropriately within the context of your application. Currently, the script logs progress and results for each step.
