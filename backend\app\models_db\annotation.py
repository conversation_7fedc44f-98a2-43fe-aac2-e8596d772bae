# app/models/annotation.py

from sqlalchemy import (
    Column,
    Integer,
    String,
    ForeignKey,
    DateTime,
)
from sqlalchemy.orm import relationship
from datetime import datetime
from app.extensions import db
from .pdf import PDF


class Annotation(db.Model):
    __tablename__ = "annotations"
    id = Column(Integer, primary_key=True)
    pdf_id = Column(Integer, ForeignKey("pdfs.id"))  # Reference to the PDF
    filename = Column(String(255), nullable=False)
    storage_path = Column(String(255), nullable=False)
    processed_at = Column(DateTime, default=datetime.utcnow)

    pdf = relationship("PDF", back_populates="annotations")
