// frontend/src/sphere/components/EditableMainField.jsx
import { useState, useEffect } from 'react';
import { FaPencilAlt, FaListUl } from 'react-icons/fa';

const EditableField = ({
    fieldKey,
    label,
    currentValue,
    originalDataSource,
    onChange,
    inputType = 'text',
    inputStep,
    useGridLayout = false,
}) => {
    const [isCustomInputActive, setIsCustomInputActive] = useState(false);

    const hasMultipleOptions =
        Array.isArray(originalDataSource) && originalDataSource.length > 1;

    useEffect(() => {
        if (!hasMultipleOptions && isCustomInputActive) {
            setIsCustomInputActive(false);
        }
    }, [hasMultipleOptions, originalDataSource, isCustomInputActive]);

    const commonInputProps = {
        id: `field-${fieldKey}`,
        // Use a random name to prevent Chrome from recognizing the field
        name: `field_${Math.random().toString(36).substring(2, 10)}`,
        value:
            currentValue === null || currentValue === undefined ? '' : currentValue,
        onChange: (e) => onChange(fieldKey, e.target.value),
        className: 'form-control',
        autoComplete: 'new-password', // This tricks Chrome better than "off"
        autoCorrect: 'off',
        autoCapitalize: 'off',
        spellCheck: 'false',
        'data-form-type': 'other',
        'data-lpignore': 'true', // Ignores LastPass autofill
    };

    const handleToggleCustomInput = () => {
        setIsCustomInputActive(!isCustomInputActive);
    };

    const handleSelectChange = (e) => {
        onChange(fieldKey, e.target.value);
        if (isCustomInputActive) {
            setIsCustomInputActive(false);
        }
    };

    const inputElement = (
        <div className="editable-field-wrapper">
            {hasMultipleOptions ? (
                isCustomInputActive ? (
                    <input
                        type={inputType}
                        {...commonInputProps}
                        step={inputType === 'number' ? inputStep : undefined}
                        autoFocus
                    />
                ) : (
                    <select {...commonInputProps} onChange={handleSelectChange}>
                        {currentValue === '' && (
                            <option value="" disabled>
                                Select or enter custom...
                            </option>
                        )}
                        {!originalDataSource.includes(String(currentValue)) &&
                            currentValue !== '' && (
                                <option value={currentValue} disabled>
                                    {String(currentValue)} (Custom)
                                </option>
                            )}
                        {originalDataSource.map((option, index) => (
                            <option key={index} value={option}>
                                {String(option)}
                            </option>
                        ))}
                    </select>
                )
            ) : (
                <input
                    type={inputType}
                    {...commonInputProps}
                    step={inputType === 'number' ? inputStep : undefined}
                />
            )}
            {hasMultipleOptions && (
                <button
                    type="button"
                    onClick={handleToggleCustomInput}
                    className="editable-field-toggle-btn"
                    title={
                        isCustomInputActive
                            ? 'Use Suggestions'
                            : 'Enter Custom Value'
                    }
                >
                    {isCustomInputActive ? <FaListUl /> : <FaPencilAlt />}
                </button>
            )}
        </div>
    );

    // Return different layouts based on the useGridLayout prop
    if (useGridLayout) {
        return (
            <div className="candidate-field-item">
                <label htmlFor={`field-${fieldKey}`}>{label}</label>
                {inputElement}
            </div>
        );
    }

    // Default table row layout
    return (
        <tr className="editable-field-row">
            <th>
                <label htmlFor={`field-${fieldKey}`}>{label}</label>
            </th>
            <td>
                {inputElement}
            </td>
        </tr>
    );
};

export { EditableField };
