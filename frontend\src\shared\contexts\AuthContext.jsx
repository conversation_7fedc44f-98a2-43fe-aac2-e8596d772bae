// frontend/src/shared/contexts/AuthContext.jsx

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { supabase } from '../services/supabase';

const AuthContext = createContext();

export function useAuth() {
    return useContext(AuthContext);
}

export function AuthProvider({ children }) {
    const [user, setUser] = useState(null);
    const [session, setSession] = useState(null);
    const [userProfile, setUserProfile] = useState(null);
    const [authLoading, setAuthLoading] = useState(true);
    const [profileLoading, setProfileLoading] = useState(false);

    // Function to fetch user profile
    const fetchUserProfile = useCallback(async (userId) => {
        if (!userId) return; // Don't fetch if no user ID

        setProfileLoading(true);
        setUserProfile(null); // Clear previous profile before fetching
        try {
            // Select the profile of the user
            const { data, error, status } = await supabase
                .from("profiles")
                .select(`user_id, role`)
                .eq("user_id", userId)
                .single();

            if (error && status !== 406) {
                // 406 status means no rows found
                console.error("Error fetching user profile:", error);
                throw error;
            }

            if (data) {
                setUserProfile(data);
            } else {
                // Handle profile not existing - set a default or leave null
                setUserProfile({ user_id: userId, role: "unauthorised" }); // Example default
                console.log(
                    `No profile found for user ${userId}.`
                );
            }
        } catch (error) {
            setUserProfile(null); // Clear profile on error
        } finally {
            setProfileLoading(false);
        }
    }, []);

    useEffect(() => {
        setAuthLoading(true); // Start in loading state
        setUserProfile(null); // Ensure profile is null initially
        setProfileLoading(false); // Ensure profile loading is false initially

        // Rely solely on onAuthStateChange
        const {
            data: { subscription },
        } = supabase.auth.onAuthStateChange((event, session) => {

            setSession(session);
            const currentUser = session?.user || null;
            setUser(currentUser);

            // Logic to fetch profile based on user
            if (currentUser) {
                // User is not null (logged in)
                fetchUserProfile(currentUser.id);
            } else {
                // User is null (logged out)
                setUserProfile(null);
                setProfileLoading(false); // Ensure profile loading stops if user logs out
            }

            // We know the auth state now, stop the main auth loading indicator
            setAuthLoading(false);
        });

        // Cleanup subscription on component unmount
        return () => {
            subscription?.unsubscribe();
        };
    }, [fetchUserProfile]); // fetchUserProfile is stable due to useCallback([])

    // --- Rest of the component (signIn, signOut, value, return) remains the same ---

    const signInWithAzure = async () => {
        try {
            const currentUrl = window.location.protocol + '//' + window.location.host;

            const { error } = await supabase.auth.signInWithOAuth({
                provider: 'azure',
                options: {
                    redirectTo: currentUrl + '/dashboard',
                    // Include necessary scopes to get the user email
                    scopes: 'openid profile email'
                },
            });
            if (error) throw error;
        } catch (error) {
            console.error('Error signing in with Azure:', error);
            return { error };
        }
    };

    const signOut = async () => {
        try {
            const { error } = await supabase.auth.signOut();
            if (error) throw error;
        } catch (error) {
            console.error('Error signing out:', error);
            return { error };
        }
    };

    // Combine loading states
    const isLoading = authLoading || profileLoading;

    const value = {
        user,
        session,
        userProfile,
        signInWithAzure,
        signOut,
        loading: isLoading,
    };

    return (
        <AuthContext.Provider value={value}>
            {!isLoading ? children : <div>Loading...</div>}{" "}
        </AuthContext.Provider>
    );
}
