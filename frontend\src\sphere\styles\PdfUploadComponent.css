/* frontend/src/sphere/styles/PdfUploadComponent.css */

.pdf-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 2rem;
}

.pdf-upload-card {
    background-color: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 4px 12px var(--shadow-color);
    padding: 2rem;
    width: 100%;
    max-width: 600px;
    transition: all 0.3s ease;
}

.pdf-upload-title {
    color: var(--text-color);
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-align: center;
}

.pdf-upload-description {
    color: var(--text-secondary);
    font-size: 1rem;
    text-align: center;
    margin-bottom: 2rem;
}

.pdf-drop-zone {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 2.5rem 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    position: relative;
    margin-bottom: 1.5rem;
    background-color: var(--input-background);
}

.pdf-drop-zone:hover {
    border-color: var(--button-primary-bg);
    background-color: rgba(0, 120, 212, 0.05);
}

.pdf-drop-zone.active {
    border-color: var(--button-primary-bg);
    background-color: rgba(0, 120, 212, 0.1);
}

/* Error styles removed in favor of toast notifications */

.pdf-drop-zone.success {
    border-color: var(--message-success-text);
    background-color: rgba(21, 87, 36, 0.05);
}

.pdf-file-input {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
    z-index: -1;
}

.pdf-drop-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.pdf-upload-icon {
    font-size: 2.5rem;
    color: var(--button-primary-bg);
    margin-bottom: 1rem;
}

.pdf-drop-text {
    color: var(--text-secondary);
    font-size: 1rem;
    text-align: center;
}

.pdf-file-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.pdf-file-icon {
    font-size: 2.5rem;
    color: var(--button-primary-bg);
    margin-bottom: 1rem;
}

.pdf-file-name {
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-align: center;
    word-break: break-word;
    max-width: 100%;
}

.pdf-file-size {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pdf-upload-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.pdf-spinner-icon {
    font-size: 2rem;
    color: var(--button-primary-bg);
    margin-bottom: 1rem;
    animation: spin 1.5s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pdf-progress-text {
    color: var(--text-color);
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.pdf-progress-bar-container {
    width: 100%;
    height: 8px;
    background-color: var(--border-color-light);
    border-radius: 4px;
    overflow: hidden;
}

.pdf-progress-bar {
    height: 100%;
    background-color: var(--button-primary-bg);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.pdf-upload-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.pdf-success-icon {
    font-size: 2.5rem;
    color: var(--message-success-text);
    margin-bottom: 1rem;
}

.pdf-success-text {
    color: var(--message-success-text);
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
}

/* Error display styles removed in favor of toast notifications */

.pdf-upload-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.pdf-upload-button {
    background-color: var(--button-primary-bg);
    color: var(--button-primary-text);
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pdf-upload-button:hover:not(:disabled) {
    background-color: var(--button-primary-hover-bg);
}

.pdf-upload-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.pdf-reset-button {
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-text);
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pdf-reset-button:hover {
    background-color: var(--button-secondary-hover-bg);
}

/* Responsive styles */
@media (max-width: 768px) {
    .pdf-upload-container {
        padding: 1rem;
    }

    .pdf-upload-card {
        padding: 1.5rem;
    }

    .pdf-drop-zone {
        padding: 2rem 1rem;
        min-height: 180px;
    }

    .pdf-upload-title {
        font-size: 1.5rem;
    }

    .pdf-upload-description {
        font-size: 0.9rem;
    }
}

/* Accessibility focus styles */
.pdf-drop-zone:focus-visible {
    outline: 2px solid var(--button-primary-bg);
    outline-offset: 2px;
}

.pdf-upload-button:focus-visible,
.pdf-reset-button:focus-visible {
    outline: 2px solid var(--button-primary-bg);
    outline-offset: 2px;
}
