# Cluster Predictor Script

This script extracts features from an image using VGG16 and HOG (Histogram of Oriented Gradients) and predicts the cluster to which the image belongs using a pre-trained KMeans model.

## Dependencies

- `numpy`
- `PIL` (Pillow)
- `skimage.feature` (HOG)
- `tensorflow.keras` (VGG16)
- `joblib`

## Classes

### FeatureExtractor

This class is responsible for extracting features from an image using VGG16 and HOG.

#### __init__(self)
Initialises the `FeatureExtractor` class and loads the VGG16 model.

#### extract_vgg16_features(self, img)
Extracts VGG16 features from an image.

- **Parameters**:
  - `img` (PIL.Image): Image object.

- **Returns**:
  - `vgg16_feature` (numpy.ndarray): Flattened VGG16 features.

#### extract_hog_features(self, img)
Extracts HOG features from an image.

- **Parameters**:
  - `img` (PIL.Image): Image object.

- **Returns**:
  - `hog_feature` (numpy.ndarray): HOG features.

#### extract_combined_features(self, img)
Extracts combined VGG16 and HOG features from an image.

- **Parameters**:
  - `img` (PIL.Image): Image object.

- **Returns**:
  - `combined_features` (numpy.ndarray): Concatenated VGG16 and HOG features.

## Functions

### predict_cluster(image, model_file, feature_extractor)
Predicts the cluster to which the image belongs using a pre-trained KMeans model.

- **Parameters**:
  - `image` (PIL.Image): Image object.
  - `model_file` (str): Path to the pre-trained KMeans model file.
  - `feature_extractor` (FeatureExtractor): Instance of the `FeatureExtractor` class.

- **Returns**:
  - `cluster` (int): Predicted cluster number.

## Main Script Execution

The script does not contain a main execution block. It is intended to be used as a module for feature extraction and cluster prediction.


## Important Notes

- Ensure that the image is loaded correctly and is in the required format for feature extraction.
- The script uses a pre-trained VGG16 model and extracts features using the `fc1` layer.
- The KMeans model should be pre-trained and saved using `joblib`.

## Error Handling

The script assumes that the image and model file paths provided are correct and that the necessary dependencies are installed.
