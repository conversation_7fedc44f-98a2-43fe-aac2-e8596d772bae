/* frontend/src/shared/styles/design-system.css */

/**
 * Design System
 *
 * This file contains all design tokens and variables used across the application.
 * Use these variables to maintain consistency in the UI.
 */

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* ======== COLORS ======== */

  /* Brand Colors */
  --ds-primary: #0078d4;
  --ds-primary-light: #1188e0;
  --ds-primary-dark: #005a9e;
  --ds-secondary: #00a246;

  /* Accent Colors */
  --ds-accent-1: #0078d4; /* Blue */
  --ds-accent-2: #00a246; /* Green */
  --ds-accent-3: #9c27b0; /* Purple */

  /* Status Colors */
  --ds-status-success: #4caf50;
  --ds-status-warning: #ff9800;
  --ds-status-error: #f44336;
  --ds-status-info: #2196f3;

  /* Status Background Colors */
  --ds-status-success-bg: rgba(76, 175, 80, 0.1);
  --ds-status-warning-bg: rgba(255, 152, 0, 0.1);
  --ds-status-error-bg: rgba(244, 67, 54, 0.1);
  --ds-status-info-bg: rgba(33, 150, 243, 0.1);

  /* Message Colors */
  --ds-message-success-bg: #d4edda;
  --ds-message-success-bg-rgb: 212, 237, 218;
  --ds-message-success-text: #155724;
  --ds-message-error-bg: #f8d7da;
  --ds-message-error-bg-rgb: 248, 215, 218;
  --ds-message-error-text: #721c24;
  --ds-message-info-bg: #d1ecf1;
  --ds-message-info-bg-rgb: 209, 236, 241;
  --ds-message-info-text: #0c5460;

  /* Neutral Colors - Light Theme */
  --ds-background: #f5f5f5;
  --ds-background-light: #f8f8f8;
  --ds-background-hover: #f0f0f0;
  --ds-card: #ffffff;
  --ds-text: #333333;
  --ds-text-secondary: #666666;
  --ds-border: #e0e0e0;
  --ds-border-light: #eeeeee;
  --ds-border-lighter: #f0f0f0;
  --ds-shadow: rgba(0, 0, 0, 0.1);

  /* ======== TYPOGRAPHY ======== */

  /* Font Family */
  --ds-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Font Sizes */
  --ds-font-size-xs: 0.75rem;    /* 12px */
  --ds-font-size-sm: 0.875rem;   /* 14px */
  --ds-font-size-md: 1rem;       /* 16px */
  --ds-font-size-lg: 1.125rem;   /* 18px */
  --ds-font-size-xl: 1.25rem;    /* 20px */
  --ds-font-size-2xl: 1.5rem;    /* 24px */
  --ds-font-size-3xl: 1.875rem;  /* 30px */
  --ds-font-size-4xl: 2.25rem;   /* 36px */
  --ds-font-size-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --ds-font-weight-light: 300;
  --ds-font-weight-regular: 400;
  --ds-font-weight-medium: 500;
  --ds-font-weight-semibold: 600;
  --ds-font-weight-bold: 700;

  /* Line Heights */
  --ds-line-height-tight: 1.2;
  --ds-line-height-normal: 1.5;
  --ds-line-height-relaxed: 1.75;

  /* Letter Spacing */
  --ds-letter-spacing-tight: -0.5px;
  --ds-letter-spacing-normal: 0;
  --ds-letter-spacing-wide: 0.5px;

  /* ======== SPACING ======== */

  --ds-spacing-xs: 4px;
  --ds-spacing-sm: 8px;
  --ds-spacing-md: 12px;
  --ds-spacing-lg: 16px;
  --ds-spacing-xl: 20px;
  --ds-spacing-2xl: 24px;
  --ds-spacing-3xl: 32px;
  --ds-spacing-4xl: 40px;
  --ds-spacing-5xl: 48px;
  --ds-spacing-6xl: 64px;

  /* ======== BORDERS ======== */

  --ds-border-radius-sm: 4px;
  --ds-border-radius-md: 8px;
  --ds-border-radius-lg: 12px;
  --ds-border-radius-xl: 16px;
  --ds-border-radius-full: 9999px;

  /* Border Styles */
  --ds-border-width: 1px;
  --ds-border-width-thick: 2px;
  --ds-border-width-accent: 4px;

  /* ======== SHADOWS ======== */

  --ds-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --ds-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --ds-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --ds-shadow-xl: 0 15px 25px rgba(0, 0, 0, 0.15);
  --ds-shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.05);

  /* Card Shadows */
  --ds-card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --ds-card-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);

  /* ======== GRADIENTS ======== */

  --ds-gradient-primary: linear-gradient(135deg, rgba(0, 120, 212, 0.05) 0%, rgba(0, 120, 212, 0.01) 100%);
  --ds-gradient-secondary: linear-gradient(135deg, rgba(0, 120, 212, 0.08) 0%, rgba(0, 120, 212, 0.02) 100%);
  --ds-gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);

  /* ======== ANIMATIONS ======== */

  --ds-transition-fast: 0.15s ease;
  --ds-transition-normal: 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  --ds-transition-slow: 0.5s ease-out;
  --ds-transition-shine: 1s ease;

  /* ======== Z-INDEX ======== */

  --ds-z-index-dropdown: 1000;
  --ds-z-index-sticky: 1020;
  --ds-z-index-fixed: 1030;
  --ds-z-index-modal-backdrop: 1040;
  --ds-z-index-modal: 1050;
  --ds-z-index-popover: 1060;
  --ds-z-index-tooltip: 1070;

  /* ======== COMPONENT SPECIFIC ======== */

  /* Buttons */
  --ds-button-padding-y: 8px;
  --ds-button-padding-x: 16px;
  --ds-button-radius: var(--ds-border-radius-md);
  --ds-button-secondary-bg: #f1f1f1;
  --ds-button-secondary-text: #333333;
  --ds-button-secondary-hover-bg: #e0e0e0;
  --ds-button-tertiary-warning-bg: #fffbeb;
  --ds-button-tertiary-warning-text: #b45309;
  --ds-button-tertiary-warning-hover-bg: #fef3c7;

  /* Tables */
  --ds-table-header-bg: var(--ds-background-light);
  --ds-table-row-hover: var(--ds-background-hover);

  /* Forms */
  --ds-input-bg: var(--ds-card);
  --ds-input-disabled-bg: var(--ds-background-light);

  /* Navbar */
  --ds-navbar-background: var(--ds-card);
  --ds-navbar-shadow: var(--ds-shadow);

  /* ======== PATTERNS ======== */

  --ds-pattern-dots: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230078d4' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");

  /* ======== COMPONENT VARIANTS ======== */

  /* Card Borders */
  --ds-card-border-subtle: 1px solid rgba(0, 120, 212, 0.1);
  --ds-card-border-accent: 1px solid rgba(0, 120, 212, 0.2);
}

/* Dark Theme Overrides */
html.dark-mode {
  /* Neutral Colors - Dark Theme */
  --ds-background: #121212;
  --ds-background-light: #1a1a1a;
  --ds-background-hover: #252525;
  --ds-card: #1e1e1e;
  --ds-text: #e0e0e0;
  --ds-text-secondary: #aaaaaa;
  --ds-border: #333333;
  --ds-border-light: #2a2a2a;
  --ds-border-lighter: #2c2c2c;
  --ds-shadow: rgba(0, 0, 0, 0.3);

  /* Table Colors */
  --ds-table-header-bg: #2d2d2d;
  --ds-table-row-hover: #2a2a2a;

  /* Card Overrides */
  --ds-card-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  --ds-card-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.3);

  /* Gradient Overrides */
  --ds-gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);

  /* Button Overrides */
  --ds-button-secondary-bg: #333333;
  --ds-button-secondary-text: #e0e0e0;
  --ds-button-secondary-hover-bg: #444444;
  --ds-button-tertiary-warning-bg: #451a03;
  --ds-button-tertiary-warning-text: #fcd34d;
  --ds-button-tertiary-warning-hover-bg: #78350f;

  /* Message Overrides */
  --ds-message-success-bg: #1e4620;
  --ds-message-success-bg-rgb: 30, 70, 32;
  --ds-message-success-text: #a3d9a5;
  --ds-message-error-bg: #4c1d1f;
  --ds-message-error-bg-rgb: 76, 29, 31;
  --ds-message-error-text: #f5c6cb;
  --ds-message-info-bg: #0c3c48;
  --ds-message-info-bg-rgb: 12, 60, 72;
  --ds-message-info-text: #a6d5df;

  /* Navbar Overrides */
  --ds-navbar-background: #1e1e1e;
  --ds-navbar-shadow: rgba(0, 0, 0, 0.3);
}

/* Animation Keyframes */
@keyframes ds-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes ds-fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ds-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes ds-pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}
