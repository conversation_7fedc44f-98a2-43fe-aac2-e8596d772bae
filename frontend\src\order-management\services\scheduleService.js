// frontend/src/order-management/services/scheduleService.js

import { supabase } from '../../shared/services/supabase';

// Helper to get authentication token
const getToken = async () => {
    const { data: sessionData } = await supabase.auth.getSession();
    return sessionData?.session?.access_token;
};

// Helper for API requests with authentication
const authFetch = async (endpoint, options = {}) => {
    const token = await getToken();

    if (!token) {
        throw new Error('Authentication token not found');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
    };

    const response = await fetch(endpoint, {
        ...options,
        headers
    });

    const text = await response.text();

    let data;
    try {
        data = JSON.parse(text);
    } catch (parseError) {
        // Friendly fallback for any HTML or garbage response
        throw new Error('Server returned an unexpected response. Please try again later.');
    }

    if (!response.ok) {
        // If your API gives { message: '…' } on errors, bubble that up
        throw new Error(data.message || 'Something went wrong on the server.');
    }

    return data;
};

// Get all schedules with pagination and optional filters
export const getSchedules = async (page = 1, pageSize = 10, filters = {}) => {
    const queryParams = new URLSearchParams({
        page,
        page_size: pageSize,
        ...filters
    });

    return authFetch(`/om_api/schedules/schedules?${queryParams.toString()}`);
};

// Get a specific schedule with all details
export const getScheduleDetails = async (scheduleId) => {
    return authFetch(`/om_api/schedules/schedules/${scheduleId}`);
};

// Get all eligible customers (for dropdown selectors)
export const getEligibleCustomers = async () => {
    return authFetch('/om_api/schedules/customers');
};

// Get customer details with contacts and schedule
export const getCustomerDetails = async (AddressCode) => {
    return authFetch(`/om_api/schedules/customers/${AddressCode}`);
};

// Update a schedule
export const updateSchedule = async (scheduleId, scheduleData) => {
    return authFetch(`/om_api/schedules/schedules/${scheduleId}`, {
        method: 'PUT',
        body: JSON.stringify(scheduleData)
    });
};

// Sync schedules
export const syncSchedules = async () => {
    return authFetch('/om_api/refresh/start-sync', {
        method: 'POST'
    });
};
