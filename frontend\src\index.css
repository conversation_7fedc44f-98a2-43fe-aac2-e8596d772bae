/* src/index.css */

/* Basic reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body.no-scroll-for-review-page {
  overflow: hidden; /* This is correct for ReviewPage */
}
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  transition: background-color 0.3s ease;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main.main-constrained {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  overflow-y: auto;
}

main.main-full-bleed {
  flex: 1;
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
/* Navbar styles */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: var(--navbar-background);
  box-shadow: 0 2px 4px var(--navbar-shadow);
  transition: background-color 0.3s ease;
}

.navbar-brand a {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-brand .home-icon {
  font-size: 1.6rem;
  transition: transform 0.2s ease;
}

.navbar-brand a:hover .home-icon {
  transform: scale(1.1);
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.navbar-item {
  color: var(--text-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.navbar-item:hover {
  background-color: var(--background-light);
}

.navbar-item .nav-icon {
  font-size: 1rem;
  color: var(--text-secondary);
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-color);
}

.navbar-user button {
  background-color: var(--button-secondary-bg);
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--button-secondary-text);
}

/* Login page styles */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 60px);
}

.login-card {
  background-color: var(--card-background);
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 8px var(--shadow-color);
  text-align: center;
  max-width: 400px;
  width: 100%;
  transition: background-color 0.3s ease;
}

.login-card h1 {
  margin-bottom: 10px;
  color: var(--text-color);
}

.login-card p {
  margin-bottom: 30px;
  color: var(--text-secondary);
}

.login-button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  margin-bottom: 10px;
}

.login-button.azure {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

/* Not Found page */
.not-found {
  text-align: center;
  padding: 50px 20px;
}

.not-found h1 {
  font-size: 80px;
  margin-bottom: 0;
  color: var(--text-color);
}

.not-found .btn {
  display: inline-block;
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  padding: 10px 20px;
  border-radius: 4px;
  text-decoration: none;
  margin-top: 20px;
}

/* Loading indicator */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: var(--text-color);
}
