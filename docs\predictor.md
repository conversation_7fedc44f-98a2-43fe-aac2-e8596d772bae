# Main Prediction Script

This script converts a PDF into images, extracts features from each image, and predicts the cluster to which each image belongs using a pre-trained KMeans model.

## Dependencies

- `pdf_to_imgs` (PDFConverter)
- `cluster_predictor` (FeatureExtractor, predict_cluster)

## Main Script Execution

The main block of the script performs the following steps:

1. **Convert PDF to Images**:
   - Instantiates the `PDFConverter` class.
   - Converts the PDF into individual images.
   - Assigns the images to variables in a list.

2. **Predict Clusters**:
   - Specifies the path to the pre-trained KMeans model.
   - Creates an instance of the `FeatureExtractor` class.
   - Iterates over each image, extracts features, and predicts the cluster using the pre-trained KMeans model.
   - Prints the predicted cluster for each image.

## Usage

Run the script using Python:

```bash
python predictor.py
```

## Important Notes

- Ensure that the PDF file path provided is correct and that the necessary dependencies are installed.
- The script uses a pre-trained KMeans model to predict the cluster for each image.
- The feature extraction is done using both VGG16 and HOG features.

## Error Handling

The script does not include explicit error handling. Ensure that the PDF file exists and is accessible, and that the necessary libraries are installed and imported correctly.
