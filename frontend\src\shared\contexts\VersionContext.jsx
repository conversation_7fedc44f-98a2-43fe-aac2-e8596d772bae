// frontend/src/shared/contexts/VersionContext.jsx

import React, { createContext, useContext, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { FaSync } from 'react-icons/fa';

const VersionContext = createContext();

export const useVersion = () => {
    const context = useContext(VersionContext);
    if (context === undefined) {
        throw new Error('useVersion must be used within a VersionProvider');
    }
    return context;
};

// Helper to safely access localStorage
const getStoredVersion = () => {
    try {
        return localStorage.getItem('appVersion');
    } catch (e) {
        console.warn('localStorage access denied or unavailable.');
        return null;
    }
};

export const VersionProvider = ({ children }) => {
    const [currentVersion, setCurrentVersion] = useState(() => {
        const storedVersion = getStoredVersion();
        // Restore robust fallback logic
        return storedVersion || null; // Use null if no version known initially
    });

    useEffect(() => {
        let isMounted = true; // Flag to prevent state updates on unmounted component

        const checkForUpdates = async () => {
            try {
                const response = await fetch(
                    '/package.json?t=' + new Date().getTime(),
                );
                if (!response.ok) {
                    // Don't log error repeatedly on interval if server is just down
                    // console.error('Failed to fetch package.json:', response.status);
                    return;
                }

                const data = await response.json();
                if (!data || !data.version) {
                    console.error('Invalid package.json format received.');
                    return;
                }
                const latestVersion = data.version;

                // Check if component is still mounted before proceeding
                if (!isMounted) return;

                const storedVersion = getStoredVersion();

                if (latestVersion !== storedVersion && storedVersion) {
                    // Update available and user has visited before
                    toast(
                        (t) => (
                            <div className="update-toast">
                                <span>A new version is available!</span>
                                <button
                                    onClick={() => {
                                        toast.dismiss(t.id);
                                        try {
                                            localStorage.removeItem('appVersion');
                                        } catch (e) {
                                            console.warn('Failed to remove item from localStorage.');
                                        }
                                        window.location.reload();
                                    }}
                                    className="refresh-button"
                                >
                                    <FaSync /> Refresh
                                </button>
                            </div>
                        ),
                        {
                            duration: Infinity,
                            id: 'app-update-toast', // Unique ID
                            // No inline style needed here anymore
                        },
                    );
                } else if (latestVersion !== currentVersion) {
                    // First load, or versions now match after refresh: update storage & state
                    try {
                        localStorage.setItem('appVersion', latestVersion);
                    } catch (e) {
                        console.warn('Failed to set item in localStorage.');
                    }
                    setCurrentVersion(latestVersion);
                    toast.dismiss('app-update-toast'); // Dismiss if shown previously
                } else {
                    // Versions match, ensure state is up-to-date if needed
                    if (currentVersion !== latestVersion) {
                        setCurrentVersion(latestVersion);
                    }
                    toast.dismiss('app-update-toast'); // Dismiss if shown previously
                }
            } catch (error) {
                // Log fetch errors (network issues, etc.)
                console.error('Error checking for updates:', error);
            }
        };

        checkForUpdates(); // Initial check
        const intervalId = setInterval(checkForUpdates, 5 * 60 * 1000); // Poll every 5 mins

        // Cleanup function
        return () => {
            isMounted = false; // Set flag on unmount
            clearInterval(intervalId); // Clear interval
            toast.dismiss('app-update-toast'); // Dismiss toast on navigation/unmount
        };
    }, [currentVersion]); // Rerun effect if currentVersion changes externally

    // Provide a manual refresh function (optional, could be removed if not used)
    const refreshApp = () => {
        try {
            localStorage.removeItem('appVersion');
        } catch (e) {
            console.warn('Failed to remove item from localStorage.');
        }
        window.location.reload();
    };

    // Provide only what's needed. currentVersion might not be needed externally.
    return (
        <VersionContext.Provider value={{ refreshApp }}>
            {children}
        </VersionContext.Provider>
    );
};
