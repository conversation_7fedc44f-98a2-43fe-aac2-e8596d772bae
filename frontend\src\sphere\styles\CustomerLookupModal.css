/* frontend/src/sphere/styles/CustomerLookupModal.css */

/* Modified modal styles for right-side positioning */
.customer-lookup-modal-overlay {
  position: fixed;
  top: 0;
  left: 50%; /* Start from the middle of the screen */
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
  transition: all 0.2s ease-in-out;
}

.customer-lookup-modal {
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out; /* Changed from modalFadeIn to modalSlideIn */
  border: 1px solid var(--border-color-light);
  position: center;
}

.customer-lookup-modal.search-results-modal {
  max-width: 100%; /* Increased from 600px to provide more space for customer details */
}

/* Customer search form */
.customer-search-form {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color-light);
}

.search-input-container {
  display: flex;
  gap: 10px;
  margin-top: 12px;
}

/* Suggested search values */
.suggested-search-values {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed var(--border-color-light);
}

.suggested-values-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.suggested-values-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-value-button {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 0.85rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.suggested-value-button:hover {
  background-color: var(--border-color-lighter);
  border-color: var(--border-color-dark);
  transform: translateY(-1px);
}

.customer-search-input {
  flex-grow: 1;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.95rem;
  color: var(--text-color);
  background-color: var(--card-background);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.customer-search-input:focus {
  border-color: var(--button-primary-bg);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--button-primary-bg-rgb), 0.2);
}

.search-button {
  padding: 10px 16px;
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.search-button:hover:not(:disabled) {
  background-color: var(--button-primary-hover-bg);
  transform: translateY(-1px);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Results count */
.results-count {
  margin-bottom: 16px;
}

/* Customer search results */
.customer-result-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.customer-result-header {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
  gap: 16px;
  align-items: center;
}

.customer-result-name {
  font-weight: 600;
  font-size: 1.05rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.customer-result-company {
  font-weight: 500;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.customer-result-contact-info {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
  gap: 16px;
  margin-top: 4px;
}

.customer-result-email,
.customer-result-phone {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.9rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.customer-result-address {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 4px;
  line-height: 1.4;
}

/* Select customer button */
.select-customer-button {
  padding: 8px 16px;
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.select-customer-button:hover {
  background-color: var(--button-primary-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* No results state */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
}

.warning-icon {
  font-size: 2.5rem;
  color: var(--button-tertiary-warning-text);
  margin-bottom: 16px;
}

/* Styles for when the customer lookup modal is open */
body.customer-lookup-modal-open .pdf-review-left-column {
  pointer-events: auto; /* Ensure left column remains interactive */
  z-index: 1; /* Keep it below the modal but above other elements */
}

body.customer-lookup-modal-open .pdf-review-right-column {
  position: relative;
}

/* Animation for modal entry */
@keyframes modalSlideIn {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.customer-lookup-modal {
  animation: modalSlideIn 0.3s ease-out;
}

/* Postcode match styling */
.customer-postcode {
  display: inline;
}

.customer-postcode.exact-match {
  color: var(--ds-message-success-text);
  font-weight: var(--ds-font-weight-semibold);
}

.customer-postcode.partial-match {
  color: var(--ds-button-tertiary-warning-text);
  font-weight: var(--ds-font-weight-medium);
}

.customer-postcode.no-match {
  color: var(--ds-message-error-text);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* For mobile, we revert to full-screen modal */
  .customer-lookup-modal-overlay {
    left: 0;
    justify-content: center;
  }

  .customer-lookup-modal {
    margin-right: 0;
    max-width: 90%;
  }

  .customer-result-header,
  .customer-result-contact-info {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .search-input-container {
    flex-direction: column;
  }

  .search-button {
    align-self: flex-end;
  }
}
