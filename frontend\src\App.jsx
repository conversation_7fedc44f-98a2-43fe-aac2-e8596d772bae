// frontend/src/App.jsx

import React from 'react';
import {
    BrowserRouter as Router,
    Routes,
    Route,
    Navigate,
    useLocation,
} from 'react-router-dom';
import { AuthProvider } from './shared/contexts/AuthContext';
import { ThemeProvider } from './shared/contexts/ThemeContext';
import { VersionProvider } from './shared/contexts/VersionContext';
import ProtectedRoute from './shared/components/ProtectedRoute';
import LoginPage from './shared/pages/LoginPage';
import ManageUsersPage from './shared/pages/ManageUsersPage';
import UnauthorizedPage from "./shared/pages/UnauthorizedPage";
import NotFoundPage from './shared/pages/NotFoundPage';
import DashboardPage from './shared/pages/DashboardPage';
import Navbar from './shared/components/Navbar';

import AdHocOrdersPage from './order-management/pages/AdHocOrdersPage';
import SchedulesPage from './order-management/pages/SchedulesPage';
import ScheduleDetailsPage from './order-management/pages/ScheduleDetailsPage';

import PdfUploadPage from './sphere/pages/PdfUploadPage';
import PendingJobsPage from './sphere/pages/PendingJobsPage';
import ReviewPage from "./sphere/pages/ReviewPage";

import './shared/styles/theme.css';
import './shared/styles/VersionUpdate.css';

import { Toaster } from "react-hot-toast";

// Define roles
const ROLES = {
    VIEWER: "viewer",
    ADMIN: "admin",
    SUPERADMIN: "superadmin",
};

// Create a new component to encapsulate content that uses hooks like useLocation
function AppContent() {
    const location = useLocation();
    // Check if the current path starts with the review page path
    const isReviewPageActive = location.pathname.startsWith('/pdfs/review/');

    return (
        <div className="app">
            <Navbar />
            <main className={isReviewPageActive ? 'main-full-bleed' : 'main-constrained'}>
                <Routes>
                    {/* Public routes */}
                    <Route path="/login" element={<LoginPage />} />
                    <Route path="/unauthorized" element={<UnauthorizedPage />} />

                    {/* Viewer level protected routes */}
                    <Route element={<ProtectedRoute requiredRoles={[ROLES.VIEWER, ROLES.ADMIN, ROLES.SUPERADMIN]} />}>
                        <Route path="/dashboard" element={<DashboardPage />} />
                        <Route path="/ad-hoc-lookup" element={<AdHocOrdersPage />} />
                        <Route path="/schedules" element={<SchedulesPage />} />
                        <Route path="/schedules/:id" element={<ScheduleDetailsPage />} />
                        <Route path="/upload-pdf" element={<PdfUploadPage />} />
                        <Route path="/pending-jobs" element={<PendingJobsPage />} />
                        <Route path="/pdfs/review/:pdfId" element={<ReviewPage />} />
                    </Route>

                    {/* Admin level protected routes */}
                    <Route element={<ProtectedRoute requiredRoles={[ROLES.ADMIN, ROLES.SUPERADMIN]} />}>
                        <Route path="/manage-users" element={<ManageUsersPage />} />
                    </Route>

                    {/* Redirect home to dashboard or login */}
                    <Route path="/" element={<Navigate replace to="/dashboard" />} />

                    {/* 404 route */}
                    <Route path="*" element={<NotFoundPage />} />
                </Routes>
            </main>
        </div>
    );
}


function App() {
    return (
        <AuthProvider>
            <ThemeProvider>
                <VersionProvider />
                <Toaster
                    position="top-right"
                    toastOptions={{
                        duration: 5000,
                        style: {
                            background: "var(--card-background)",
                            color: "var(--text-color)",
                            boxShadow: "0 2px 8px var(--shadow-color)",
                        },
                        success: {
                            duration: 3000,
                        },
                        error: {
                            duration: 5000,
                        },
                    }}
                    containerStyle={{
                        top: '65px'
                    }}
                />
                <Router>
                    <AppContent />
                </Router>
            </ThemeProvider>
        </AuthProvider >
    );
}

export default App;
