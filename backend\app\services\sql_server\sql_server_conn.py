# backend/app/services/sql_server/sql_server_conn.py

from sqlalchemy import create_engine
import pyodbc
import os


def get_server_config():
    return {
        "driver": os.getenv("DB_DRIVER", "ODBC Driver 17 for SQL Server"),
        "server": os.getenv("DB_SERVER"),
        "database": os.getenv("DB_DATABASE"),
        "uid": os.getenv("DB_UID"),
        "pwd": os.getenv("DB_PWD"),
    }


def get_engine():
    config = get_server_config()
    connection_string = (
        f"mssql+pyodbc://{config['uid']}:{config['pwd']}@{config['server']}/{config['database']}"
        f"?driver={config['driver'].replace(' ', '+')}"
    )
    engine = create_engine(connection_string)
    return engine
