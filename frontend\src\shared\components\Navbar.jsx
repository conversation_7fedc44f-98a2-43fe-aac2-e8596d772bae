// frontend/src/shared/components/Navbar.jsx
import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { FaSun, FaMoon, FaUser, FaHome, FaSearch, FaCalendarAlt, FaUpload, FaClipboardList } from 'react-icons/fa';

// Helper function to determine which section the user is in based on the URL path
const getSectionFromPath = (pathname) => {
    if (pathname.startsWith('/upload-pdf') || pathname.startsWith('/pending-jobs') || pathname.startsWith('/pdfs/review')) {
        return 'sphere';
    } else if (pathname.startsWith('/ad-hoc-lookup') || pathname.startsWith('/schedules')) {
        return 'order-management';
    } else if (pathname.startsWith('/manage-users')) {
        return 'admin';
    } else {
        return 'dashboard';
    }
};

const Navbar = () => {
    const { user, userProfile, signOut } = useAuth();
    const { darkMode, toggleTheme } = useTheme();
    const navigate = useNavigate();
    const location = useLocation();
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Determine which section the user is currently in
    const currentSection = getSectionFromPath(location.pathname);

    const handleSignOut = async () => {
        await signOut();
        navigate('/login');
        setDropdownOpen(false);
    };

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <nav className="navbar">
            <div className="navbar-brand">
                <Link to="/dashboard" aria-label="Home">
                    <FaHome className="home-icon" />
                </Link>
            </div>

            <div className="navbar-menu">
                {/* Order Management section navigation items */}
                {user && (currentSection === 'order-management') && (
                    <>
                        <Link to="/ad-hoc-lookup" className="navbar-item">
                            <FaSearch className="nav-icon" />
                            <span>Ad Hoc Lookup</span>
                        </Link>
                        <Link to="/schedules" className="navbar-item">
                            <FaCalendarAlt className="nav-icon" />
                            <span>Schedules</span>
                        </Link>
                    </>
                )}

                {/* Sphere section navigation items */}
                {user && (currentSection === 'sphere') && (
                    <>
                        <Link to="/upload-pdf" className="navbar-item">
                            <FaUpload className="nav-icon" />
                            <span>Upload</span>
                        </Link>
                        <Link to="/pending-jobs" className="navbar-item">
                            <FaClipboardList className="nav-icon" />
                            <span>Pending Orders</span>
                        </Link>
                    </>
                )}
                <button
                    className="theme-toggle-button"
                    onClick={toggleTheme}
                    aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
                >
                    {darkMode ? <FaSun /> : <FaMoon />}
                </button>

                {user && (
                    <div className="user-dropdown" ref={dropdownRef}>
                        <button
                            className="user-icon-button"
                            onClick={() => setDropdownOpen(!dropdownOpen)}
                            aria-label="User menu"
                        >
                            <FaUser />
                        </button>

                        {dropdownOpen && (
                            <div className="dropdown-menu">
                                <div className="dropdown-header">
                                    <span className="user-name">{user.user_metadata?.full_name || 'User'}</span>
                                    <span className="user-email">{user.email}</span>
                                </div>
                                <div className="dropdown-divider"></div>
                                <button className="dropdown-item" onClick={handleSignOut}>
                                    Sign Out
                                </button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </nav>
    );
};

export default Navbar;
