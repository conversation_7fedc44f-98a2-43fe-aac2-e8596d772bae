/* frontend/src/shared/styles/version-update.css */

.update-toast {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  border-left: 4px solid var(--primary-color);
}

.refresh-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: none; 
  border-radius: 4px; 
  font-size: 1rem; 
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, opacity 0.2s;
  text-decoration: none;
  white-space: nowrap;
  /* Colors from original .primary-button */
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  /* Keep margin for spacing within the toast */
  margin-left: 12px;
}

.refresh-button:hover:not(:disabled) {
  background-color: var(--button-primary-hover-bg);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-button svg {
  font-size: 1em;
}
