# Split Labelled Output Data Script

This script splits a single JSON file containing multiple annotations into individual JSON files for each annotation. The individual JSON files are saved in a structured directory based on the image paths.

## Dependencies

- `json`
- `os`

## Functions

### split_and_save_json(data)
Splits and saves individual JSON files from the input data.

- **Parameters**:
  - `data` (list): List of annotation items to be split and saved.

- **Functionality**:
  - For each item in the input data, the function extracts the annotations and the image path.
  - It creates directories based on the image paths if they do not already exist.
  - It prepares the output file path by replacing the image extension with `.json`.
  - It creates a new dictionary for each individual annotation.
  - It saves the individual annotation to a JSON file in the appropriate directory.

## Main Script Execution

The main block of the script performs the following steps:
1. Loads the JSON file containing multiple annotations.
2. Calls the `split_and_save_json` function to split the annotations and save them as individual JSON files.

## Usage

Run the script using Python:

```bash
python split_labelled_output_data.py
```

## Directory Structure

The script saves the individual JSON files in a structured directory under `data/images/labelled/`. The directory structure is based on the image paths extracted from the annotations.

## Error Handling

The script ensures that directories are created if they do not already exist. Any errors encountered during the JSON splitting and saving process should be handled appropriately within the context of your application.
