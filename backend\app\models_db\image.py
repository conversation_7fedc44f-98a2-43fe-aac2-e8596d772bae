# app/models/image.py

from sqlalchemy import (
    Column,
    Integer,
    String,
    ForeignKey,
    DateTime,
)
from sqlalchemy.orm import relationship
from datetime import datetime
from app.extensions import db
from .pdf import PDF


class Image(db.Model):
    __tablename__ = "images"
    id = Column(Integer, primary_key=True)
    pdf_id = Column(Integer, ForeignKey("pdfs.id"))
    filename = Column(String(255), nullable=False)
    storage_path = Column(String(255), nullable=False)
    processed_at = Column(DateTime, default=datetime.utcnow)

    pdf = relationship("PDF", back_populates="images")
