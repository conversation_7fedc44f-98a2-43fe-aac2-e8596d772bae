# Create LLMv3 Dataset Script

This script processes images in a specified folder using OCR (Optical Character Recognition) to generate annotations and predictions. The processed data is saved in JSON format suitable for use with Label Studio.

## Dependencies

- `os`
- `json`
- `uuid`
- `numpy`
- `PIL` (Pillow)
- `paddleocr`
- `logging`

## Class: OCRProcessor

### __init__(self, use_gpu=True)
Initialises the `OCRProcessor` class, setting up the OCR engine.

- **Parameters**:
  - `use_gpu` (bool): Whether to use GPU for OCR. Default is `True`.

### create_image_url(filename)
Creates a URL for accessing an image file.

- **Parameters**:
  - `filename` (str): The filename of the image.

- **Returns**:
  - `url` (str): The URL to access the image.

### convert_bounding_box(bounding_box)
Converts the bounding box coordinates to the required format.

- **Parameters**:
  - `bounding_box` (list): List of bounding box coordinates [x1, y1, x2, y2].

- **Returns**:
  - `converted_bbox` (list): Converted bounding box [x, y, width, height].

### process_images_in_folder(self, images_folder_path, output_folder_path)
Processes all images in a specified folder, performing OCR and saving the results in JSON format.

- **Parameters**:
  - `images_folder_path` (str): Path to the folder containing image files.
  - `output_folder_path` (str): Path to the folder where JSON files will be saved.

### process_single_image(self, image_path)
Processes a single image, performing OCR and returning the result as a JSON object.

- **Parameters**:
  - `image_path` (str): Path to the image file.

- **Returns**:
  - `output_json` (dict): JSON object containing OCR results and annotations.

## Main Script Execution

The main block of the script performs the following steps:
1. Initialises the `OCRProcessor` class.
2. Defines the paths for the images folder and output folder.
3. Creates the output folder if it does not exist.
4. Processes all images in the specified folder, performing OCR and saving the results in JSON format.

## Usage

Run the script using Python:

```bash
python create_llmv3_dataset.py
```

## Logging

The script uses the `logging` module to log information about the OCR processing, including progress and any errors encountered.

## Error Handling

Errors encountered during the OCR processing are logged using the `logging` module, allowing for easier debugging and tracking of issues.
