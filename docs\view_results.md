# View Results Script

This script processes an image, performs inference using a pretrained LayoutLMv3 model, decodes the predictions, and visualises the results.

## Dependencies

- `sys`
- `os`
- `json`
- `src.model_dev.inference.process_image` (ImageProcessor)
- `src.model_dev.inference.inference` (ModelInference)
- `src.model_dev.inference.visualise` (AnnotationVisualizer)

## Functions

### main()
The main function that orchestrates the processing, inference, decoding, and visualisation of the results.

- **Functionality**:
  - Defines paths for the image, product codes file, model, and processor.
  - Initialises the `ImageProcessor`, `ModelInference`, and `AnnotationVisualizer` components.
  - Processes the image to extract OCR annotations.
  - Performs inference on the processed data.
  - Decodes the model predictions to return meaningful results.
  - Prepares the output in the desired format.
  - Visualises the annotations on the image.

## Script Execution

The main block of the script performs the following steps:

1. **Define Paths**:
   - Specifies the paths for the image file, product codes file, pretrained model, and processor.

2. **Initialise Components**:
   - Initialises the `ImageProcessor` with the product codes file.
   - Initialises the `ModelInference` with the model and processor paths.

3. **Process the Image**:
   - Calls the `process_image` method of `ImageProcessor` to extract OCR annotations from the image.
   - Checks if any data was processed from the image.

4. **Perform Inference**:
   - Calls the `predict` method of `ModelInference` to perform inference on the processed data.

5. **Decode Predictions**:
   - Calls the `decode_predictions` method of `ModelInference` to decode the model predictions.

6. **Prepare Output**:
   - Formats the decoded predictions into a JSON-like structure.

7. **Visualise Annotations**:
   - Initialises the `AnnotationVisualizer` with the formatted output.
   - Calls the `visualize` method to display the annotations on the image.

## Usage

Run the script using Python:

```bash
python view_results.py
```

## Important Notes

- Ensure that the paths to the image file, product codes file, pretrained model, and processor are correctly specified.
- The script assumes that the `ImageProcessor`, `ModelInference`, and `AnnotationVisualizer` classes are implemented and available in the specified modules.
- The script processes the image, performs inference using the pretrained LayoutLMv3 model, decodes the predictions, and visualises the results.
