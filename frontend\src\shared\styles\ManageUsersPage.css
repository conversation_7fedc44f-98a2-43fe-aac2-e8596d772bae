/* frontend/src/shared/styles/ManageUsersPage.css */
/* Styles specific to the Manage Users Page, using variables from theme.css */

.manage-users-container {
    padding: 20px 0; /* Vertical padding */
  }
  
  .manage-users-container h1 {
    color: var(--text-color);
    font-size: 1.8rem;
    margin-bottom: 25px;
  }
  
  /* Use message styles from theme/dashboard */
  .manage-users-container .message-container {
    padding: 15px 20px;
    border-radius: 4px;
    border: 1px solid transparent;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .manage-users-container .message-container p {
    margin: 0;
  }
  .manage-users-container .message-container.success {
    background-color: var(--message-success-bg);
    color: var(--message-success-text);
    border-color: var(--message-success-bg);
  }
  .manage-users-container .message-container.error {
    background-color: var(--message-error-bg);
    color: var(--message-error-text);
    border-color: var(--message-error-bg);
  }
  
  /* Use loading indicator styles from dashboard */
  .manage-users-container .loading-indicator {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
  .manage-users-container .large-spinner {
    font-size: 2rem;
  }
  .manage-users-container .spinner {
    animation: spin 1s linear infinite;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  
  .users-table-container {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    overflow-x: auto; /* Allow horizontal scroll on small screens */
  }
  
  .users-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .users-table th,
  .users-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    vertical-align: middle;
  }
  
  .users-table th {
    background-color: var(--table-header-bg);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    color: var(--text-secondary);
  }
  
  .users-table tbody tr:last-child td {
    border-bottom: none;
  }
  
  .users-table tbody tr:hover {
    background-color: var(--table-row-hover);
  }
  
  .users-table td[data-label="Email"] {
  /* Add any specific styles for email column if desired */
  word-break: break-all; /* Allow long emails to wrap */
  }
  
  .role-toggle-button {
    /* Inherit base button styles from theme/dashboard */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 6px 12px; /* Slightly smaller padding */
    border: none;
    border-radius: 4px;
    font-size: 0.85rem; /* Smaller font size */
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, opacity 0.2s;
    text-decoration: none;
    white-space: nowrap;
    width: 170px; /* Ensure buttons have same width */
  }
  
  .role-toggle-button.primary-button {
    background-color: var(--button-primary-bg);
    color: var(--button-primary-text);
  }
  .role-toggle-button.primary-button:hover:not(:disabled) {
    background-color: var(--button-primary-hover-bg);
  }
  
  .role-toggle-button.tertiary-warning-button {
    background-color: var(--button-tertiary-warning-bg);
    color: var(--button-tertiary-warning-text);
  }
  .role-toggle-button.tertiary-warning-button:hover:not(:disabled) {
    background-color: var(--button-tertiary-warning-hover-bg);
  }
  
  .role-toggle-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .role-toggle-button .icon {
    font-size: 1em; /* Match button font size */
  }
  
  /* Responsive adjustments (optional) */
  @media (max-width: 768px) {
    .users-table thead {
      display: none; /* Hide table header on small screens */
    }
    .users-table tr {
      display: block;
      margin-bottom: 15px;
      border-bottom: 2px solid var(--button-primary-bg); /* Separator */
    }
    .users-table td {
      display: block;
      text-align: right; /* Align value to the right */
      padding-left: 50%; /* Make space for label */
      position: relative;
      border-bottom: 1px solid var(--border-color-light);
    }
    .users-table td::before {
      content: attr(data-label); /* Use data-label for pseudo-header */
      position: absolute;
      left: 10px;
      width: calc(50% - 20px); /* Adjust width */
      padding-right: 10px;
      white-space: nowrap;
      text-align: left;
      font-weight: bold;
      color: var(--text-color);
    }
    .users-table td:last-child {
       border-bottom: none;
    }
    .role-toggle-button {
        width: 100%; /* Make button full width */
        margin-top: 5px;
    }
  }
  