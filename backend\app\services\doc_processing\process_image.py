# backend/app/services/doc_processing/process_image.py

import json
import logging

from .create_dataset import OCRProcessor
from .split_dataset import JSONUpdater

logger = logging.getLogger(__name__)

label_mapping = {
    "PO_Number": 1,
    "Contact_Name": 2,
    "Contact_Phone": 3,
    "Contact_Email": 4,
    "Delivery_Address": 5,
    "Invoice_Address": 6,
    "Product_Code": 7,
    "Product_Qty": 8,
    "Invoice_Email": 9,
    "Product_Name": 0,
    "Unit_Cost": 10,
    "Line_Value": 11,
    "Total_Value": 12,
    "Ignore_Text": 13,
    "Order_Date": 14,
    "Invoice_Phone": 15,
}


class ImageProcessor:
    def __init__(self, product_codes_file):
        try:
            self.ocr_processor = OCRProcessor(use_gpu=False)
            self.json_updater = JSONUpdater(product_codes_file=product_codes_file)
            logger.info("OCRProcessor and JSONUpdater initialized successfully.")
        except Exception as e:
            logger.error(f"Error initializing ImageProcessor: {e}")
            raise

    def convert_bounding_box(
        self,
        x,
        y,
        width,
        height,
        original_width,
        original_height,
        new_width,
        new_height,
    ):
        """
        Converts the given bounding box coordinates to normalised values.

        Args:
        x: The x-coordinate of the top-left corner of the bounding box in percentages.
        y: The y-coordinate of the top-left corner of the bounding box in percentages.
        width: The width of the bounding box in percentages.
        height: The height of the bounding box in percentages.
        original_width: The original width of the image.
        original_height: The original height of the image.
        new_width: The new width of the image after resizing.
        new_height: The new height of the image after resizing.

        Returns:
        A tuple of four coordinates (x1, y1, x2, y2) normalised to [0, 1].
        """
        try:
            x = x / 100
            y = y / 100
            width = width / 100
            height = height / 100

            x1 = x
            y1 = y
            x2 = x + width
            y2 = y + height
            return [x1, y1, x2, y2]
        except Exception as e:
            logger.error(f"Error converting bounding box: {e}")
            raise

    def process_image(self, file_path=None, image=None):
        try:
            # Process the image with OCR
            annotated_json = self.ocr_processor.process_single_image(image=image)

            if not annotated_json:
                # Skip logging here and return None to avoid duplicate log entries
                return None

            # Convert the result into a proper JSON format
            json_string = json.dumps(annotated_json)
            correct_json_format = f"[{json_string}]"
            annotated_json = json.loads(correct_json_format)

            # Update the JSON with additional processing
            data = self.json_updater.update_json(input_json=annotated_json)

            output = []
            unknown_labels = []

            for annotated_image in data:
                if (
                    "predictions" not in annotated_image
                    or not annotated_image["predictions"]
                ):
                    return None

                annotations_list = annotated_image["predictions"][0]["result"]
                if not annotations_list:
                    return None

                data_entry = {}
                annotations = []

                file_name = (
                    annotated_image.get("data", {}).get("ocr", "").split("8080/")[-1]
                )
                if not file_name:
                    return None

                data_entry["file_name"] = file_name

                original_width, original_height = image.size

                data_entry["height"] = original_height
                data_entry["width"] = original_width

                for result in annotations_list:
                    bbox = result.get("value", {})
                    if result["type"] == "rectangle":
                        ann_dict = {
                            "box": self.convert_bounding_box(
                                bbox["x"],
                                bbox["y"],
                                bbox["width"],
                                bbox["height"],
                                original_width,
                                original_height,
                                original_width,
                                original_height,
                            ),
                            "text": None,
                            "label": None,
                        }
                        annotations.append(ann_dict)
                    elif result["type"] == "textarea":
                        for ann_dict in annotations:
                            if (
                                self.convert_bounding_box(
                                    bbox["x"],
                                    bbox["y"],
                                    bbox["width"],
                                    bbox["height"],
                                    original_width,
                                    original_height,
                                    original_width,
                                    original_height,
                                )
                                == ann_dict["box"]
                            ):
                                ann_dict["text"] = result["value"].get("text", [None])[
                                    0
                                ]
                    elif result["type"] == "labels":
                        for ann_dict in annotations:
                            if (
                                self.convert_bounding_box(
                                    bbox["x"],
                                    bbox["y"],
                                    bbox["width"],
                                    bbox["height"],
                                    original_width,
                                    original_height,
                                    original_width,
                                    original_height,
                                )
                                == ann_dict["box"]
                            ):
                                label = result["value"]["labels"][0]
                                if label not in label_mapping:
                                    unknown_labels.append(label)
                                ann_dict["label"] = label_mapping.get(label, None)

                # Check if any valid annotations exist before adding to output
                if annotations:
                    data_entry["annotations"] = annotations
                    output.append(data_entry)

            logger.info("Image processing completed successfully.")
            return output if output else None

        except Exception as e:
            logger.error(f"Error processing image: {e}")
            return None
