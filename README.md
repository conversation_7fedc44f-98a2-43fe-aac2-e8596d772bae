# Automated Purchase Order Invoice Processing

## Project Overview

This project aims to automate the extraction of details from purchase order invoices received as PDFs. The extracted details are displayed to an end-user for review and adjustment before being fed into an ERP system. This process minimises manual data entry, reduces errors, and improves efficiency.

## Dataset

- **Source:** Approximately 1100 purchase order invoice PDFs.
- **Preprocessing:** Each PDF is converted into images, with one image per page.
- **Annotations:** Using PaddleOCR for initial text and bounding box extraction, followed by manual annotation and validation using Label Studio.

## Technologies and Tools

1. **PaddleOCR and PaddlePaddle:** For extracting text and bounding boxes from the images.
2. **Label Studio:** For manually labeling and validating the extracted text and bounding boxes.
3. **LayoutLMv3:** A specialised transformer model for document understanding, used to train a custom model for extracting specific fields from the invoices.
4. **Clustering Algorithms:**
   - **PCA:** For dimensionality reduction.
   - **KMeans and Agglomerative Clustering:** To cluster similar invoice layouts, allowing for the development of specialised models for each cluster.

## Directory Structure

```plaintext
data/
├── images/
│   └── raw/ # Each sub folder here represents one PDF
├── pdfs/ # All initial PDFs
├── label_studio_files/
│   └── cluster_0_done_min.json # Export from label studio
│   └── cluster_0_input_file.json # File to import to label studio
│   └── cluster_0_training.json # Labelled file converted to layoutlmv3 style
├── cluster_labels.pkl
├── features.pkl
├── product_codes.txt # List of all product codes
LayoutLM_Models/
├──layoutlmv3Microsoft # Pre trained model, stored locally
paddle_env/
saved_model/ # Trained model
src/
├── clustering/
│   ├── clustering.py
│   ├── feature_extraction.py
│   ├── load_clusters.py
│   ├── pdf_to_jpg.py
│   ├── review_clustering.py
├── model_dev/
│   └── cluster_0/
│       ├── check_llmv3_dataset.py
│       ├── create_llmv3_dataset.py
│       ├── inference.py
│       ├── label_studio_to_llmv3.py
│       ├── split_llmv3_boxes.py
│       ├── trainer.py
├── simple_http_server.py
tensor_env/
.gitignore
paddle_requirements.txt
tensor_requirements.txt
README.md
```

## Installation and Setup

### Prerequisites

1. **CUDA Toolkit 11.7**
2. **cuDNN for CUDA 11.x**
   - [Download cuDNN](https://developer.nvidia.com/rdp/cudnn-archive#a-collapse897-118)
   - Copy all `bin`, `include`, and `lib` files from the cuDNN install to the CUDA installation directory.

### Virtual Environments

- You will need two virtual environments: one for PaddlePaddle and one for training with LayoutLMv3.
- These must both be using Python 3.11

### Setting up the Paddle Environment

**You must use this up until training and evaluation**
1. Create and activate the Paddle virtual environment:
    ```bash
    python -m venv paddle_env
    source paddle_env/bin/activate  # On Windows use `paddle_env\Scripts\activate`
    ```

2. Install the required packages:
    ```bash
    pip install -r paddle_requirements.txt
    ```

3. Install PaddlePaddle GPU:
    ```bash
    pip install paddlepaddle-gpu
    ```

### Setting up the Training Environment

**You must use this up during training and evaluation**
1. Create and activate the training virtual environment:
    ```bash
    python -m venv tensor_env
    source tensor_env/bin/activate  # On Windows use `tensor_env\Scripts\activate`
    ```

2. Install the required packages:
    ```bash
    pip install -r tensor_requirements.txt
    ```

### Setup Labeling

1. Run `simple_http_server.py` from the project main directory using the Paddle environment:
    ```bash
    source paddle_env/bin/activate
    python src/simple_http_server.py
    ```

2. Run Label Studio on port 8081 from the training environment:
    ```bash
    source tensor_env/bin/activate
    label-studio -p 8081
    ```

## Usage

### Step 1: PDF to Image Conversion

Convert PDFs to images using:
```bash
python src/clustering/pdf_to_jpg.py
```

### Step 2: Feature Extraction and Clustering

Extract features and cluster images:
```bash
python src/clustering/feature_extraction.py
python src/clustering/clustering.py
```

### Step 3: Prepare Data for Label Studio Annotation
Prepare the dataset:
```bash
python src/model_dev/cluster_0/create_llmv3_dataset.py
python src/model_dev/cluster_0/split_llmv3_boxes.py
```

### Step 4: Label the data

Label the data:
```bash
Access localhost on port 8081
```

### Step 5: Once labelled, export and convert for llmv3

Label the data:
```bash
python src/model_dev/cluster_0/label_studio_to_llmv3.py
```

### Step 6: Train the Model

Train the LayoutLMv3 model:
```bash
python src/model_dev/cluster_0/trainer.py
```

### Step 7: Run Inference

Run inference on new data:
```bash
python src/model_dev/cluster_0/inference.py
```

## Contributors

- Finlay Riggott