// frontend/src/sphere/hooks/useProductVerification.js
import { useState, useRef } from 'react';
import toast from 'react-hot-toast';
import { lookupProductByCode, lookupProductByName } from '../services/pdfService';

/**
 * Custom hook for product verification functionality
 * @param {Object} options - Configuration options
 * @param {Function} options.updateProductField - Function to update a product field
 * @param {Array} options.products - Array of products
 * @param {Function} options.setVerifiedProducts - Function to update verified products state
 * @param {Object} options.selectedCustomer - The selected customer object containing DefaultHorizontalMatrix_Id
 * @returns {Object} - Product verification state and handlers
 */
export const useProductVerification = ({
    updateProductField,
    products,
    setVerifiedProducts = () => { }, // Optional callback for tracking verified products
    selectedCustomer = null // The selected customer object
}) => {
    // Modal state
    const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
    const [verificationLoading, setVerificationLoading] = useState(false);
    const [verificationError, setVerificationError] = useState(null);
    const [verifiedProduct, setVerifiedProduct] = useState(null);
    const [searchResults, setSearchResults] = useState(null);
    const [currentProductIndex, setCurrentProductIndex] = useState(null);

    // Store original product data before verification to enable unconfirmation
    const originalProductsRef = useRef(new Map());

    /**
     * Check a product by its code or name
     * @param {number} rowIndex - Index of the product in the products array
     */
    const handleCheckProduct = async (rowIndex) => {
        // The component will check if the product is verified and call handleUnconfirmProduct directly
        // if needed, so we don't need to check here

        setCurrentProductIndex(rowIndex);

        // Get product details
        const product = products[rowIndex];
        const productQty = product.Product_Qty;
        const productCode = product.Product_Code;
        const productName = product.Product_Name;

        // Check if both product code AND product name are empty
        if ((!productCode || productCode.trim() === '') &&
            (!productName || productName.trim() === '')) {
            toast.error("Product verification requires either a code or name");
            return;
        }

        // Validate quantity before proceeding
        // Check if quantity is empty
        if (!productQty || productQty.trim() === '') {
            toast.error("Please enter a quantity before verifying this product");
            return;
        }

        // Check if quantity is a whole number (integer)
        const qtyValue = parseFloat(productQty);
        if (isNaN(qtyValue) || !Number.isInteger(qtyValue) || qtyValue <= 0) {
            toast.error("Product quantity must be a whole number");
            return;
        }

        // Store the original product data before verification
        if (!originalProductsRef.current.has(rowIndex)) {
            originalProductsRef.current.set(rowIndex, { ...products[rowIndex] });
        }

        // Initialize verification state
        setVerificationLoading(true);
        setVerificationError(null);
        setVerifiedProduct(null);
        setSearchResults(null);
        setIsVerificationModalOpen(true);

        // If product code is empty but name exists, skip code verification and go directly to name search
        if ((!productCode || productCode.trim() === '') &&
            productName && productName.trim() !== '') {
            // Call handleSearchByName directly after setting up the modal
            try {
                // Get the address id selected customer
                const addressId = selectedCustomer?.address_id || null;

                const results = await lookupProductByName(
                    productName,
                    product.Unit_Cost?.replace(/[^0-9.]/g, ''),
                    addressId
                );
                setSearchResults(results);
            } catch (error) {
                setVerificationError("No matching products found.");
            } finally {
                setVerificationLoading(false);
            }
            return;
        }

        // Otherwise, proceed with code verification as before
        try {
            // Get the address id from the selected customer available
            const addressId = selectedCustomer?.address_id || null;

            const product = await lookupProductByCode(productCode, addressId);
            setVerifiedProduct(product);
        } catch (error) {
            setVerificationError("Product code not found.");
        } finally {
            setVerificationLoading(false);
        }
    };

    /**
     * Check if a product is verified - this is a placeholder
     * The actual implementation is in the component
     * @returns {boolean} - Whether the product is verified
     */
    const isProductVerified = () => {
        // This is just a placeholder - the actual implementation is in the component
        return false;
    };

    /**
     * Unconfirm a verified product
     * @param {number} rowIndex - Index of the product in the products array
     */
    const handleUnconfirmProduct = (rowIndex) => {
        // Get the original product data
        const originalProduct = originalProductsRef.current.get(rowIndex);

        if (!originalProduct) {
            toast.error("Original product data not found.");
            return;
        }

        // Restore the original product data
        Object.keys(originalProduct).forEach(key => {
            updateProductField(rowIndex, key, originalProduct[key]);
        });

        // Remove the product from the verified products set
        setVerifiedProducts(prev => {
            const newVerified = new Set(prev);
            newVerified.delete(rowIndex);
            return newVerified;
        });

        // Remove the original product data
        originalProductsRef.current.delete(rowIndex);

        toast.success("Product verification removed.");
    };

    /**
     * Search for products by name
     * This is called when the user clicks the "Search by Name" button in the verification modal
     */
    const handleSearchByName = async () => {
        if (currentProductIndex === null) return;

        setVerificationLoading(true);
        setVerificationError(null);
        setVerifiedProduct(null);
        setSearchResults(null);

        const product = products[currentProductIndex];
        const productName = product.Product_Name;
        const productCost = product.Unit_Cost?.replace(/[^0-9.]/g, '');

        if (!productName) {
            setVerificationLoading(false);
            setVerificationError("Product name is required for search.");
            return;
        }

        try {
            // Get the DefaultHorizontalMatrix_Id from the selected customer if available
            const defaultHorizontalMatrixId = selectedCustomer?.DefaultHorizontalMatrix_Id || null;

            const results = await lookupProductByName(productName, productCost, defaultHorizontalMatrixId);
            setSearchResults(results);
        } catch (error) {
            setVerificationError(error.message || "No matching products found.");
        } finally {
            setVerificationLoading(false);
        }
    };

    /**
     * Select a product from verification results
     * @param {Object} product - The selected product
     */
    const handleSelectProduct = (product) => {
        if (currentProductIndex === null) return;

        // Make sure we have stored the original product data
        if (!originalProductsRef.current.has(currentProductIndex)) {
            originalProductsRef.current.set(currentProductIndex, { ...products[currentProductIndex] });
        }

        // Update the product fields with the selected product data
        updateProductField(currentProductIndex, "Product_Code", product.ProductCode);
        updateProductField(currentProductIndex, "Product_Name", product.ProductName);
        updateProductField(currentProductIndex, "Unit_Cost", `£${parseFloat(product.Price).toFixed(2)}`);

        // Calculate Line_Value if Product_Qty exists
        const qty = products[currentProductIndex].Product_Qty;
        if (qty) {
            const lineValue = parseFloat(qty) * parseFloat(product.Price);
            updateProductField(currentProductIndex, "Line_Value", `£${lineValue.toFixed(2)}`);
        }

        // Mark this product as verified
        setVerifiedProducts(prev => {
            const newVerified = new Set(prev);
            newVerified.add(currentProductIndex);
            return newVerified;
        });

        // Close the modal
        setIsVerificationModalOpen(false);
        toast.success("Product details updated successfully.");
    };

    /**
     * Close the verification modal
     */
    const handleCloseVerificationModal = () => {
        setIsVerificationModalOpen(false);
    };

    return {
        // State
        isVerificationModalOpen,
        verificationLoading,
        verificationError,
        verifiedProduct,
        searchResults,
        currentProductIndex,
        originalProductsRef,

        // Handlers
        handleCheckProduct,
        handleSearchByName,
        handleSelectProduct,
        handleCloseVerificationModal,
        handleUnconfirmProduct,

        // Helpers
        isProductVerified
    };
};
