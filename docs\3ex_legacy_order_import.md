## Legacy Order Import

### Contents

1. [Clear Import Tables](#step-1-clear-import-tables)
2. [Loop Through CSV Files](#step-2-loop-through-csv-files)
   - [Delivery Address Import Data Flow](#delivery-address-import-data-flow)
     - [1. Delivery Address Details](#1-delivery-address-details)
     - [2. Defaults](#2-defaults)
     - [3. <PERSON>-<PERSON><PERSON>](#3-de-dupe)
     - [4. Lookup Address On Address Fields](#4-lookup-address-on-address-fields)
     - [5. Conditional Branching](#5-conditional-branching)
       - [i. If Lookup Match Output (Address Exists)](#i-if-lookup-match-output-address-exists)
       - [ii. If Lookup No Match Output (Lookup CountryCode)](#ii-if-lookup-no-match-output-lookup-countrycode)
       - [6. Conditional Branching After Lookup CountryCode](#6-conditional-branching-after-lookup-countrycode)
         - [i. If Lookup Match Output (Multicast)](#i-if-lookup-match-output-multicast)
         - [ii. If Lookup No Match Output (Issue with Country Code)](#ii-if-lookup-no-match-output-issue-with-country-code)
     - [7. Delivery Address Import](#i-delivery-address-import)
     - [8. Delivery Contact Import](#ii-delivery-contact-import)
3. [Run Delivery Address Import](#step-3-run-delivery-address-import)
4. [Sleep Step](#step-4-sleep-step)
5. [For Loop Container (Status Check)](#step-5-for-loop-container-status-check)
   - [i. Get Job Status](#i-get-job-status)
   - [ii. Sleep](#ii-sleep)
6. [For Loop Container (Transaction Import and Archiving)](#step-6-for-loop-container-transaction-import-and-archiving)
   - [i. Transaction Import](#i-transaction-import)
     - [1. Get Data](#1-get-data)
     - [2. Defaults](#2-defaults-transaction-import)
     - [3. Lookup Address Exists](#3-lookup-address-exists)
     - [4. Address Issue](#4-address-issue)
     - [5. Lookup Transaction Check](#5-lookup-transaction-check)
     - [6. Lookup Product Exists](#6-lookup-product-exists)
     - [7. Product Issue](#7-product-issue)
     - [8. Lookup Delivery Address Exists](#8-lookup-delivery-address-exists)
     - [9. Delivery Issue](#9-delivery-issue)
     - [10. Multicast](#10-multicast)
     - [11. Line Defaults](#11-line-defaults)
     - [12. Line Number](#12-line-number)
     - [13. TransactionLineImport](#13-transactionlineimport)
     - [14. Header Defaults](#14-header-defaults)
     - [15. Row Count](#15-row-count)
     - [16. TransactionHeaderImport](#16-transactionheaderimport)
     - [17. Multicast 1](#17-multicast-1)
     - [18. Special Instructions Defaults](#18-special-instructions-defaults)
     - [19. RelatedDataHeaderResponseImport](#19-relateddataheaderresponseimport)
   - [ii. Archive](#ii-archive)
   - [iii. Failed Archive](#iii-failed-archive)

---


### Step 1: Clear Import Tables
#### Task: **Clear Import Tables**
- **Type:** Execute SQL Task
- **Purpose:** Clear existing records with specific import identifiers to prepare tables for new data.

##### SQL Script
```sql
DELETE FROM AddressImport WHERE ImportIdentifier = 'ORD-DEL';
DELETE FROM TransactionHeaderImport WHERE ImportIdentifier = 'ORD';
DELETE FROM TransactionLineImport WHERE ImportIdentifier = 'ORD';
DELETE FROM RelatedDataTransactionHeaderResponseImport WHERE ImportIdentifier = 'ORD';
DELETE FROM AddressContactImport WHERE ImportIdentifier = 'ORD-DEL';
```

**Explanation:** This step removes any pre-existing data with the specified `ImportIdentifier` values from the tables involved in the order import process. It ensures that each import run starts with a clean slate.

---

### Step 2: Loop Through CSV Files
#### Task: **Foreach Loop Container**
- **Type:** Foreach Loop Container
- **Purpose:** Iterates through each CSV file in the order import folder and performs the 'Delivery Address' import.

**Configuration:**
- **Folder Path:** `D:\3exNet\OrderImport`
- **File Type:** CSV

**Workflow**
- For each CSV file found in the specified folder:
  - Perform the **Delivery Address Import** (details in next step)

**Explanation:** This loop container reads each CSV file in the folder, applying the same processing steps to each, ensuring all data files are imported without manual intervention.

#### **Delivery Address Import Data Flow**

The following steps outline the process within the "Delivery Address" data flow, including transformation details, mappings, and error-handling logic.

---

#### 1. Delivery Address Details
- **Description:** Reads external columns from CSV files and maps them to output columns for further processing.
- **Error Handling:**
  - **On Error:** Fail the component.
  - **On Truncation:** Fail the component.
- **Columns:** 
![](/docs/img/Delivery_Address/delivery_address_details.png)
---

#### 2. Defaults
- **Description:** Creates new column values by applying expressions to transformation input columns. Columns can be created or overwritten. 
  - **Example:** Concatenating values from 'First Name' and 'Last Name' columns to create a 'Full Name' column.
- **Expressions:** See image for specific expressions used to create or modify column values.
![](/docs/img/Delivery_Address/defaults.png)

---

#### 3. De-Dupe
- **Description:** Sorts input data in ascending or descending order to prevent duplicate entries. Useful for data flows where source sorting is unavailable.
  - **Note:** Not recommended for large data flows due to potential performance issues.
- **Configuration:** 

    | Input Column | Output Alias | Sort Type | Sort Order |
    | ------------ | ------------ | --------- | ---------- |
    | DeliveryName | DeliveryName | ascending |      2     |
    |    Email     |     Email    | ascending |      1     |

    - Remove rows with duplicate sort values
---

#### 4. Lookup Address on Address Fields
- **Description:** This step performs a lookup transformation to join additional address columns into the data flow. By comparing the input data with existing records in the `Address` table, it populates address details accurately where matches are found.
  
- **Purpose:** Ensures that address details are filled accurately by referencing existing records, reducing redundancy and maintaining data integrity.

- **Configuration:**
  - **Cache Mode:** Full Cache (for optimal performance with smaller tables that can fit in memory).
  - **Connection Type:** OLE DB Connection Manager.
  - **Connection:** Main database (`3EX.3exnet_cosy_live`).
  - **No Match Handling:** Rows with no matching entries in the `Address` table are redirected to the "No Match Output," allowing further processing for unmatched records.
  - **Error Output:** If an error occurs during lookup, the component is set to "Fail Component."

- **SQL Query for Lookup:**
  - A custom SQL query is used to retrieve the relevant address details from the `Address` table. This query includes a `SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED` statement to minimize locking and ensure the query completes quickly.
  - The query selects address lines, postcode, and address ID for entries with `AddressTypeId = 3`, converting address fields to uppercase for consistency.

    ```sql
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SELECT 
      UPPER(Address.AddressLine1) AS AddressLine1,
      UPPER(Address.AddressLine2) AS AddressLine2,
      UPPER(Address.AddressLine3) AS AddressLine3,
      UPPER(Address.AddressLine4) AS AddressLine4,
      UPPER(Address.AddressLine5) AS AddressLine5,
      UPPER(Address.Postcode) AS PostCode,
      Address.Id AS addressId 
    FROM Address
    WHERE Address.AddressTypeId = 3;
    ```

- **Lookup Columns and Mappings:**
  - **Lookup Column:** `addressId`
  - **Mapping of Input Columns to Lookup Columns:**
    - `DelLine1` maps to `AddressLine1`
    - `DelLine2` maps to `AddressLine2`
    - `DelLine3` maps to `AddressLine3`
    - `DelLine4` maps to `AddressLine4`
    - `DelLine5` maps to `AddressLine5`
    - `DelPost` maps to `PostCode`
  - **Output Alias:** The output alias for the lookup result is `addressId`, which is added as a new column to the data flow if a match is found.

- **Error Handling Configuration (as seen in the Error Output settings):**
  - **On Error:** The component is set to fail if there is an error in the lookup process for any row.
  - **On Truncation:** The component also fails if any truncation issues occur.

This configuration allows the "Lookup Address on Address Fields" step to efficiently join address data from the `Address` table, handling both matched and unmatched cases appropriately and ensuring smooth data flow through the pipeline.

---

#### 5. Conditional Branching

The conditional branching in the data flow occurs after the **Lookup Address on Address Fields** step. This step splits the data flow based on whether an address match was found in the lookup operation.



**i. If Lookup Match Output (Address Exists)**

- **Description:** The **Address Exists** path handles records where an address match was successfully found. This path leverages a **Derived Column Transformation** to create or modify columns based on the matched address data.
- **Purpose:** Adds new columns or modifies existing columns using transformation input columns, which allows additional data customization or enhancement if a matching address was located.
- **Transformation Type:** Derived Column.
- **Configuration:**
  - **Derived Column Settings:** Uses expressions to generate new columns or overwrite existing ones. An example might include concatenating certain columns or formatting address lines.
  - **Component Properties:** Refer to "Advanced Properties" to verify the exact configuration and input/output column settings, ensuring correct mapping and usage of fields.
  - **Error Handling:** Configured to proceed without error unless a failure explicitly occurs in downstream components.
- **Example Transformation:** Concatenate values from multiple fields, such as combining `FirstName` and `LastName` into a `FullName` column or appending a suffix to address data.

---

**ii. If Lookup No Match Output (Lookup CountryCode)**

- **Description:** The **Lookup CountryCode** path is triggered for records with no matching address. This lookup transformation attempts to retrieve a `CountryCode` based on the provided `Country` field.
- **Purpose:** Ensures that all records have a valid `CountryCode`, even if the address lookup fails, by mapping the `Country` to its respective `CountryCode` in the `Category` table.
- **Transformation Type:** Lookup Transformation.
- **Configuration:**
  - **Cache Mode:** Full Cache (pre-loads the entire lookup table into memory).
  - **Connection Type:** OLE DB Connection Manager.
  - **Database Connection:** `3EX.3exnet_cosy_live` (main database).
  - **SQL Query:**
    - This SQL query fetches the `Country` description and `Code` from the `Category` table for records where `CategoryUsageId` is 8. It uses `READ UNCOMMITTED` isolation level to minimize database locking.

      ```sql
      SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
      SELECT 
          UPPER(Description) AS Country, 
          Code AS AddressCountryCode 
      FROM Category 
      WHERE CategoryUsageId = 8;
      ```

  - **Mappings:**
    - **Input Column to Lookup Column:** `DelCountry` maps to `Country` in the lookup.
    - **Output Column (Alias):** The result, `AddressCountryCode`, is added to the data flow as a new column under the alias `AddressCountryCode`.
  
  - **Error Handling Configuration:**
    - **On Error:** If an error occurs during the lookup operation, the component is set to **Fail Component** to prevent continuation with incomplete data.
    - **No Match Handling:** Rows with no matching entries are redirected to the "No Match Output," enabling further processing for unresolved country codes.
  - **Example Use:** This lookup enables the addition of a `CountryCode` to records where only the `Country` is provided, ensuring each record includes a standardized country identifier.

---

#### 6. Conditional Branching After Lookup CountryCode

The flow splits based on the outcome of the **Lookup CountryCode** transformation:

**1. Lookup Match Output:** The flow moves to the **Multicast** step.

**2. Lookup No Match Output:** The flow moves to the **Issue with Country Code** transformation.

---

#### i. **If Lookup No Match Output (Issue with Country Code)**

- **Description:** The **Issue with Country Code** step is a derived column transformation applied to records where no match was found in the **Lookup CountryCode** step. This transformation creates or modifies columns using expressions, which may involve concatenating fields or applying conditional logic to handle missing or invalid country codes.
- **Purpose:** To create new columns or modify existing ones, particularly to handle cases where the country code is missing or invalid. This helps flag or prepare the data for further actions, such as logging issues or setting default values.
- **Transformation Type:** Derived Column Transformation.
- **Configuration:**
  - **Derived Column Logic:** Uses expressions to handle missing country codes. For example, it may concatenate a placeholder text to indicate an issue or set a default code.
  - **Example Transformation:** Concatenate `FirstName` and `LastName` fields to create a `FullName` column if such a transformation is needed for other logic.
- **Component Properties:** Refer to **"Advanced Properties"** for column mappings and expressions to verify that each input column is correctly configured and used within this transformation.

The `Multicast` transformation distributes every input row to multiple outputs. This approach enables parallel processing of rows, allowing different aspects of the data to be processed or stored simultaneously. 

#### i. **Delivery Address Import**
   - **Description:** 
     - Uses an **OLE DB Destination** component to import data into the `AddressImport` table in the primary database.
     - Configuration allows fast-loading, with batch size and constraints managed for optimal performance.
   - **Mappings:**
     - Maps specific fields such as `ImportIdentifier`, `DeliveryAddressLine1`, and `Postcode` to the appropriate columns in `AddressImport`.
   - **Error Handling:** 
     - Configured to fail the component if any error arises, ensuring only valid and complete records are loaded.

     ![](/docs/img/Delivery_Address/delivery_address_import.png)

#### ii. **Delivery Contact Import**
   - **Description:** 
     - An **OLE DB Destination** component for importing contact-specific data to the `AddressContactImport` table.
     - Enables separate handling of contact details while maintaining relational integrity with address records.
   - **Mappings:**
     - Includes mappings for fields like `ContactName`, `JobTitleCode`, and `MobileTelephone` to their corresponding columns in `AddressContactImport`.
   - **Error Handling:** 
     - Also set to fail on error, ensuring that incomplete or invalid records do not enter the database.
    ![](/docs/img/Delivery_Address/delivery_contact_import.png)

---

### Step 3: Run Delivery Address Import
#### Task: **Run Delivery Address Import**
- **Type:** Web Service Task
- **Purpose:** Executes a web service to initiate the "Delivery Address Import" by calling a specified job from the `jobmanagerwebservice.wsdl` file.

**Web Service Configuration**
- **Connection Manager:** HTTP Connection Manager pointing to `http://*********:9000/jobmanagerwebservice/service.asmx`.
- **WSDL File Path:** `D:\IntegrationConfigs\jobmanagerwebservice.wsdl`
- **Service Name:** Service (defined in the WSDL file)
- **Method:** `CreateBatchJob`
- **Input Parameters:**
  - **clientId:** `long`, fixed value `28`
  - **jobDetailId:** `int`, fixed value `52`
  - **userDetailId:** `int`, fixed value `18`

- **Output:**
  - **Output Variable:** `User::AddressJobResult` (stores the result of the job initiation for further steps in the package)

**Explanation:** This task leverages the `CreateBatchJob` method from the web service, which, according to the WSDL, creates a job that will initiate a batch of jobs for the specified `jobDetailId`. The WSDL file specifies the structure of the SOAP request and response, detailing the parameters required and response handling.

**WSDL Details for CreateBatchJob**
Here's a breakdown of the `CreateBatchJob` operation within the WSDL:

```xml
<s:element name="CreateBatchJob">
  <s:complexType>
    <s:sequence>
      <s:element minOccurs="1" maxOccurs="1" name="clientId" type="s:long" />
      <s:element minOccurs="1" maxOccurs="1" name="jobDetailId" type="s:int" />
      <s:element minOccurs="1" maxOccurs="1" name="userDetailId" type="s:int" />
    </s:sequence>
  </s:complexType>
</s:element>
```

**SOAP Action for CreateBatchJob**
- **SOAP Action URL:** `http://Exact.Exnet.JobManager/CreateBatchJob`
- **Binding Style:** Document-based SOAP (using `literal` for encoding)

This setup ensures the web service is called correctly with the parameters set in the task configuration, initiating the job specified by the `jobDetailId` to perform the delivery address import.

--- 

### Step 4: Sleep Step

In this **Script Task** for the **Sleep** function, the script essentially pauses execution by sleeping for 30 seconds. Here’s a breakdown of the key elements:

1. **Purpose of the Script Task**: 
   - The **Sleep** function is often used in integration workflows to introduce a delay, typically to allow other processes or services to catch up before proceeding to the next task.
   - In this context, the 30-second delay (`System.Threading.Thread.Sleep(30000);`) likely provides a buffer time, potentially waiting for a job to initialize or finish on the server before moving forward.

2. **Namespaces and Setup**:
   - The script uses standard namespaces like `System`, `System.Data`, and `Microsoft.SqlServer.Dts.Runtime`. This gives it access to basic .NET functionality and specific tools for SQL Server Integration Services (SSIS).
   - Various **Help** regions in the code explain how to interact with SSIS-specific features, such as variables, events, and connection managers. These sections are more instructional and do not directly affect the function of this script.

3. **Main Logic**:
   - The `Main()` method, which is the script's entry point, executes the sleep command (`System.Threading.Thread.Sleep(30000);`) to pause the script for 30 seconds.
   - After the delay, it sets the `Dts.TaskResult` to **Success**, indicating to SSIS that the script has completed successfully and the workflow can proceed.

4. **Script Results**:
   - The `ScriptResults` enum is a shorthand that allows the script to set the result to either `Success` or `Failure`, giving SSIS clear feedback on the script's outcome.

---

### Step 5: For Loop Container (Status Check)
The purpose of this step is to continuously check the job status after initiating the "Delivery Address Import". The container iterates and runs the **Get Job Status** SQL task, then pauses for a specified interval. It loops until a successful job status is detected or until a maximum number of iterations is reached.

#### Task: **Get Job Status**
- **Type:** Execute SQL Task
- **Purpose:** Queries the `SystemJob` table to check the status of the running job, specifically to identify if the job associated with `SystemJobDetailId = 52` has completed.
- **Connection Type:** OLE DB
- **Connection:** `3EX.3exnet_cosy_live` (main database)
- **Result Set:** Single row

**SQL Statement:**
```sql
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SELECT TOP 1
    (CASE WHEN Status = 2 THEN 1 ELSE 0 END) AS [Status]
FROM 
    SystemJob 
WHERE 
    SystemJobDetailId = 52 
ORDER BY
    Id DESC
```

**Explanation:**
- This query checks the `SystemJob` table for the most recent entry with `SystemJobDetailId = 52`.
- The `Status` field is evaluated to see if it equals `2`, indicating the job has completed successfully. If so, the query returns `1` for `Status`; otherwise, it returns `0`.
- Setting the transaction isolation level to **READ UNCOMMITTED** ensures that the query does not lock any resources, allowing it to run frequently without interference.

**Configuration Details:**
- **Parameter Mapping:** No parameters are mapped as this is a direct SQL query without dynamic inputs.
- **Result Set:** The result of the `Status` field is mapped to the `User::ImportStatus` variable, which the For Loop container evaluates in each iteration to determine if it should continue or exit.
  
#### Next Step in Loop
After each `Get Job Status` execution, the For Loop container moves to a **Sleep** task, introducing a delay before the next status check.

---

### Step 6: Transaction Import
This step involves processing data from a flat file and mapping it into the data flow for further transformations and eventual import into the database. 

#### 1. Get Data
- **Description:** Flat File Source
- **Purpose:** Reads data from the order import file, integrating it into the data flow pipeline.
- **Connection Manager:** Flat File Connection Manager linked to the **Order Import File**.

**Columns Configuration:**
- Mappings for each source column to an output column. Key columns include:
  - `CustomerAddressCode`, `OrderReference`, `ProductCode`, `ProductPrice`, `Qty`
  - `DeliveryName`, `DeliveryAddressLine1` to `DeliveryAddressLine5`, `Postcode`, `Country`
  - `WorkTelephone`, `MobileTelephone`, `Email`, `OrderType`, `PartShipment`
  - `Channel`, `LocalEducationAuthority`, `Delivery Instructions`

**Error Handling:**
- **Error Action:** Fail Component on both error and truncation.
- **Error Type:** Conversion errors will cause the component to fail if mismatches or data type issues occur, ensuring data integrity.

---

#### 2. Defaults (transaction import)
- **Description:** Derived Column Transformation
- **Purpose:** Adds new columns or modifies existing ones by applying expressions to input columns. For example, combining `DeliveryAddressLine1` to `DeliveryAddressLine5` into uppercase format.
  
**Configuration:**
- **Expressions Used:**
  - `ImportIdentifier`: Adds a constant value `OrderReference` as a new column.
  - `DelLine1` to `DelLine5`, `DelPost`, and `DelName`: Converts `DeliveryAddressLine1` to `DeliveryAddressLine5`, `Postcode`, and `DeliveryName` to uppercase using `UPPER()` function.
  - `CategoryCode1`, `CategoryCode2`, etc.: Sets static category codes used in the import process.
  - `TransactionDate`: Sets the current date and time with `GETDATE()` function.
  - **Example Columns Created:**
    - `ImportReference`: Adds a reference value based on `OrderReference`.
    - `WorkflowCode`: Adds workflow identifiers (`SOE` and `SO`) for processing.

**Variables and Parameters:**
- Derived column transformations create or overwrite columns as per the variables and parameters set in the transformation editor.

---

#### 3. Lookup Address Exists
- **Description:** Joins additional columns to the data flow by performing a lookup transformation to retrieve values from a specified table. This step checks for existing addresses in the `Address` table based on the `AddressTypeId`.
- **Purpose:** Ensures that any existing address information is pulled from the database, allowing further data enrichment or validation.
  
**SQL Query for Lookup:**
```sql
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SELECT 
    Id AS AddressId, 
    AddressCode 
FROM 
    Address 
WHERE 
    AddressTypeId = 1;
```

- **Connection Type:** OLE DB Connection Manager
- **Connection:** `3EX.3exnet_cosy_live`
- **Cache Mode:** Full Cache (recommended for lookup tables that fit into memory)
- **Mappings:** The `CustomerAddressCode` from the input columns maps to `AddressCode` in the lookup table. The lookup result, `AddressId`, is added as a new column to the data flow.
- **No Match Handling:** Rows with no matching entries are redirected to the **Address Issue** output.
- **Error Handling:** If an error occurs in the lookup, the component is set to fail to ensure data accuracy.

**Explanation:** This step compares input records against the `Address` table to see if an address already exists. If found, the address ID is pulled into the flow for further processing.

---

#### i. Address Issue
- **Description:** Creates new column values by applying expressions to transformation input columns. It is configured to handle cases where no match was found during the **Lookup Address Exists** step.
- **Purpose:** To allow custom transformations or error-handling actions when address lookup fails.

**Example Transformation:** Concatenates `DeliveryAddressLine1` to `DeliveryAddressLine5` to form a consolidated address column or flags the record for further investigation.

**Configuration:** Derived column expressions are set to handle missing data or apply default values where required.

---

#### 4. Lookup Transaction Check
- **Description:** Another lookup transformation that enriches the data flow by retrieving existing transaction references from the `TransactionHeader` table.
- **Purpose:** Ensures that records are matched with existing transaction references, preventing duplication or overwriting during import.

**SQL Query for Lookup:**
```sql
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SELECT 
    importedreference 
FROM 
    transactionheader;
```

- **Connection Type:** OLE DB Connection Manager
- **Connection:** `3EX.3exnet_cosy_live`
- **Cache Mode:** Full Cache
- **Mappings:** The input column `ImportReference` is mapped to `importedreference` in the lookup table.
- **No Match Handling:** Rows with no matching entries are redirected for additional processing.
- **Error Handling:** Fail on error if a lookup operation encounters issues.

**Explanation:** This step checks if the `ImportReference` in the input data already exists in `TransactionHeader`, helping maintain consistency by linking data to existing transactions.

---

#### 5: Lookup Product Exists

#### Task: **Lookup Product Exists**
- **Description:** Joins additional columns to the data flow by performing a lookup transformation on the `Product` table. This transformation is used to retrieve the `ProductId` based on the `ProductCode` from the source data. This approach is recommended for cases where the lookup table can fit into memory for optimal performance.
- **Purpose:** Ensures that each transaction line references a valid `ProductId` by performing a lookup using the `ProductCode`.
  
**Configuration:**
- **Cache Mode:** Full Cache (loads the entire lookup table into memory for faster access).
- **Connection Type:** OLE DB Connection Manager.
- **Connection:** Main database (`3EX.3exnet_cosy_live`).
- **SQL Query for Lookup:**
  
  ```sql
  SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
  SELECT Code, Id AS ProductId FROM Product;
  ```
  This query retrieves the `Code` and `Id` from the `Product` table, mapping the `Code` to the input `ProductCode` and the resulting `Id` to the `ProductId` in the data flow.

- **Mappings:**
  - **Input Column to Lookup Column:** `ProductCode` maps to `Code`.
  - **Output Alias:** The resulting `ProductId` is added to the data flow under the alias `ProductId`.

- **Error Handling Configuration:**
  - **On Error:** Set to fail the component if there is any error in the lookup process.
  - **On Truncation:** Also set to fail if truncation issues occur.

This configuration allows the "Lookup Product Exists" transformation to verify the existence of each product in the `Product` table, ensuring that transaction records only proceed with valid product data.

--- 

#### 6. Product Issue
- **Description:** Derived Column Transformation
- **Purpose:** Handles records for which no match was found in the **Lookup Product Exists** step, typically indicating an invalid or missing product code. This transformation allows custom handling for records missing a valid product ID, such as flagging the record for review or adding default/error values.

**Configuration:**
- **Transformation Logic:** Adds or modifies columns to indicate a product issue. For example, a derived column could be created to concatenate the `ProductCode` with an error flag or to populate a field indicating "Product Not Found."
- **Example Expression:** A derived column might concatenate the string "ERROR_" with the `ProductCode` to create a new field, `ProductIssueFlag`, that flags the row for further investigation.
  
**Error Handling:** The component is configured to fail if there are any issues during this transformation, ensuring no incomplete or incorrect data passes through undetected.

**Explanation:** This transformation serves as an error-handling route, identifying records with missing or invalid products and marking them for further review. This step is essential for ensuring data integrity by filtering out records with issues before they proceed in the workflow.

---

#### 7. Lookup Delivery Address Exists
- **Description:** This lookup transformation enriches the data flow by checking for existing delivery addresses in the `Address` table. The goal is to find and retrieve any existing address data to avoid duplicating information in the database.
- **Purpose:** This step helps reduce data redundancy by linking each record to an existing delivery address, if available, thereby ensuring accurate and consistent address information.

**SQL Query for Lookup:**
```sql
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SELECT 
    Address.Id AS AddressId, 
    AddressCode, 
    UPPER(Address.AddressLine1) AS DeliveryAddressLine1,
    UPPER(Address.AddressLine2) AS DeliveryAddressLine2,
    UPPER(Address.AddressLine3) AS DeliveryAddressLine3,
    UPPER(Address.AddressLine4) AS DeliveryAddressLine4,
    UPPER(Address.AddressLine5) AS DeliveryAddressLine5,
    UPPER(Address.Postcode) AS Postcode
FROM 
    Address 
WHERE 
    AddressTypeId = 3;
```

- **Connection Type:** OLE DB Connection Manager
- **Connection:** `3EX.3exnet_cosy_live`
- **Cache Mode:** Full Cache (recommended for lookups with smaller datasets that fit into memory)
- **Mappings:**
  - **Input Column to Lookup Column:** `DeliveryAddressCode` from the input data maps to `AddressCode` in the `Address` table.
  - **Output Columns:** If a match is found, the output columns `AddressId`, `DeliveryAddressLine1` to `DeliveryAddressLine5`, and `Postcode` are added to the data flow, enriching each record with existing delivery address details.

- **No Match Handling:** Rows with no matching entries are redirected to the **Delivery Issue** transformation, which handles records without a valid delivery address.
- **Error Handling:** If an error occurs during the lookup, the component is set to fail, ensuring data integrity and preventing incomplete data from progressing.

**Explanation:** This step verifies whether a delivery address already exists in the `Address` table. When a match is found, the existing address information is retrieved and added to the record, avoiding data duplication and ensuring accuracy in delivery address mapping.

---

#### i. Delivery Issue
- **Description:** Derived Column Transformation
- **Purpose:** Handles cases where no delivery address was found during the **Lookup Delivery Address Exists** step. This transformation allows for additional handling, such as marking records missing valid delivery addresses for further review or applying default values as needed.

**Example Transformation:** A derived column might be created to indicate an "Address Not Found" status or to set a default value. For instance, a new column `DeliveryAddressFlag` could be added with the value "MISSING" to flag records that lack a valid delivery address.

**Configuration:** This transformation uses expressions to set default values or indicators for records with missing delivery addresses, making them easy to identify in later processing steps.

**Explanation:** This step ensures that records without a valid delivery address are clearly flagged or handled, enabling accurate tracking and data integrity for subsequent processing.

---

#### 8. Multicast
- **Description:** The multicast transformation duplicates the data flow, sending every input row to multiple outputs. This enables parallel processing on different branches for various tasks.
- **Purpose:** Allows data to be split into multiple streams, enabling tasks such as deduplication or line-level default settings to occur simultaneously.

**Branches from Multicast:**

1. **Dedupe Headers**
   - **Description:** This transformation sorts input data based on specific columns to remove duplicates. It sorts by `DeliveryName` and `Email` in ascending order, eliminating rows with identical values for these columns.
   - **Purpose:** Ensures only unique header information is retained, contributing to clean and efficient data management.
   - **Sorting Configuration:**
     - **Columns:** 
       - `Email` (ascending, Sort Order 1)
       - `DeliveryName` (ascending, Sort Order 2)

2. **Line Defaults**
   - **Description:** Applies expressions to create new column values or modify existing ones. This transformation standardises line-level data by setting default values or deriving values from other columns as needed.
   - **Purpose:** Prepares the data for consistent formatting at the line level, ensuring that records are properly structured before final import.

---

#### 9. Line Number
- **Branches from LineDefaults**
- **Description:** This step includes a **Script Transformation** that adds a line number to each record. This script can include custom business rules, such as incrementing values or adding specific identifiers to rows for further processing.
- **Purpose:** The line number is essential for tracking each line of data in the transaction, allowing each record to be uniquely identified within the transaction batch.

**Configuration:**
- **Script Language:** Microsoft Visual C# 2012
- **ReadOnlyVariables and ReadWriteVariables:** These are set as per requirements to facilitate the line numbering process.
- **Input Columns:** Includes columns such as `CustomerAddressCode`, `OrderReference`, `ProductCode`, `ProductPrice`, `Qty`, `DeliveryName`, and address details.
- **Output Columns:** The output includes a new `RowNumber` or `LineNumber` column, which uniquely identifies each line in the transaction data.

**Explanation:** The **Line Number** transformation runs a custom script that assigns a unique line number to each record. This helps in tracking and managing each individual line in the transaction during the import process, ensuring clarity and traceability.

---

#### 10. TransactionLineImport
- **Branches from Line Number**
- **Description:** This is an **OLE DB Destination** step that writes the final transformed data into the database table `[dbo].[TransactionLineImport]`. It maps each input column to the corresponding destination column, ensuring the data is structured and stored accurately.
- **Purpose:** The purpose of this step is to store the processed transaction data into the database for further use or reporting.

**Connection Configuration:**
- **Connection Manager:** `3EX.3exnet_cosy_live`
- **Data Access Mode:** Table or view - fast load (optimised for batch loading)
- **Destination Table:** `[dbo].[TransactionLineImport]`
- **Settings:** Options like `Table Lock` and `Check Constraints` are enabled to ensure data integrity and optimise performance during the import.

**Mappings:**
- The main columns and their mappings include:
  - **ProductCode** → **ProductCode**
  - **ProductPrice** → **Price**
  - **Qty** → **Quantity**
  - **ImportIdentifier** → **ImportIdentifier**
  - **ImportReference** → **ImportReference**
  - **RowNumber (or LineNumber)** → **LineNumber**
  - Additional mappings as shown in the image, including fields such as `DeliveryName`, `AddressId`, and category codes.

**Explanation:** In the **TransactionLineImport** step, the data is finally written to the `[dbo].[TransactionLineImport]` table in the database. The table structure includes mapped columns for each field, ensuring that all transformed data, including the line number, product details, and address information, is saved accurately. This marks the completion of the data import process, with the data now ready for downstream processes or reporting.

---

#### 11. Multicast 1

- **Description:** The **Multicast** transformation duplicates the data flow, distributing each row to multiple outputs for parallel processing.
- **Purpose:** This enables different transformations to be applied to the same data without affecting each other, allowing separate branches for specialised data processing tasks.

#### 12: Special Instructions Defaults
- **Branches off multicast 1**
- **Description:** A **Derived Column Transformation** that applies expressions to create or modify columns related to special instructions.
- **Purpose:** This step standardises the processing of special instructions by setting default values and ensuring consistency.

**Configuration:**
- **Derived Columns:**
  - `AnswerNumber`: Set to a constant value of `1` to indicate the first answer in the instructions set.
  - `TextAnswer`: Maps to `[Delivery Instructions]`, capturing the special instructions from the input.
  - `RelatedDataElementCode`: Set to `"SPECIALINS"` to categorise the entry as special instructions.

**Explanation:** By setting default values and transforming special instruction data, this step prepares the data to be stored in a structured and uniform manner, ensuring easy retrieval and consistency.

#### 13: RelatedDataHeaderResponseImport

- **Branches off special instructions defaults**
- **Description:** This is the final destination for special instructions data, where it is stored in the database for reference in subsequent processes.
- **Purpose:** To import and store structured special instructions data in the `RelatedDataTransactionHeaderResponseImport` table.

**Connection Configuration:**
- **Connection Manager:** `3EX.3exnet_cosy_live`
- **Data Access Mode:** Table or view - fast load
- **Destination Table:** `[dbo].[RelatedDataTransactionHeaderResponseImport]`
- **Settings:** `Table Lock` and `Check Constraints` options are enabled for integrity and performance.

**Mappings:**
- **Input Column to Destination Column Mappings:**
  - `ImportIdentifier` → `ImportIdentifier`
  - `ImportReference` → `ImportReference`
  - `RelatedDataElementCode` → `RelatedDataElementCode`
  - `TextAnswer` → `TextAnswer`
  - `AnswerNumber` → `AnswerNumber`

**Explanation:** This step completes the special instructions import by mapping the relevant fields to the `RelatedDataTransactionHeaderResponseImport` table in the database. By storing the special instructions data in a well-organised format, it can be easily referenced for reporting or further processing, ensuring that all special instructions are accounted for and linked with the main transaction records. 

Here's an extended write-up detailing the **Header Defaults**, **Row Count**, and **TransactionHeaderImport** transformations in alignment with the rest of the process:

---

#### 14. Header Defaults
- **Description:** This step is a **Derived Column Transformation** that applies expressions to the input columns to create new values or overwrite existing ones, preparing header-level data before it is counted and sent to the destination table.
- **Purpose:** The purpose of this step is to set default values for specific header columns. This standardisation ensures consistency and prepares the data for structured import into the database.

**Configuration:**
- **Derived Columns:** Expressions are used to populate or modify specific columns. Examples may include concatenating address information or creating default values for fields that might otherwise be null.
  - **Example Expressions:** Concatenate fields like `CustomerAddressCode` and `OrderReference` to create a comprehensive header identifier if required.
- **Key Columns Created/Modified:**
  - Columns like `CategoryUsageIdForCategory1`, `CategoryCode1`, etc., are set or defaulted, ensuring consistency across categories.
  
**Explanation:** The **Header Defaults** step creates or modifies header-level data by applying default values or derived expressions. By the time data leaves this step, it is well-structured with standardised values that support accurate tracking and grouping of records.

---

#### 15. Row Count
- **Description:** This **Row Count Transformation** calculates the total number of rows that pass through the data flow and assigns the result to a variable. This count can later be used for auditing, logging, or conditional operations based on row totals.
- **Purpose:** Counting rows at this stage provides a metric for the entire import process, helping in validation and ensuring that all records are accounted for.

**Configuration:**
- **Variable:** The transformation saves the row count to the `User::RowCount` variable, which can be accessed later in the process or used for logging and validation.

**Explanation:** The **Row Count** transformation is an essential step for tracking the volume of data passing through the flow. This metric is valuable for downstream processes that might need to verify record totals or for reporting purposes to confirm successful data import.

---

#### 16. TransactionHeaderImport
- **Branches from Row Count**
- **Description:** This final step is an **OLE DB Destination** transformation that maps the processed header data to the `[dbo].[TransactionHeaderImport]` table. This transformation loads header-level data into the destination table within the database.
- **Purpose:** The purpose of **TransactionHeaderImport** is to store the final processed header records in the database, completing the import process.

**Connection Configuration:**
- **Connection Manager:** Utilises the `3EX.3exnet_cosy_live` OLE DB connection manager.
- **Data Access Mode:** Configured as "Table or view - fast load" for optimised batch loading.
- **Destination Table:** `[dbo].[TransactionHeaderImport]`
- **Settings:** `Table Lock` and `Check Constraints` options are enabled, ensuring that data integrity constraints are upheld during the import.

**Mappings:**
- The key mappings from input columns to destination columns include:
  - **CustomerAddressCode** → **AddressCodeForPrimary**
  - **ImportIdentifier** → **ImportIdentifier**
  - **AddressCode** → **AddressCodeForOther1**
  - **CategoryUsageIdForCategory1** → **CategoryUsageIdForCategory1**
  - **CategoryCode1** → **CategoryCode1**
  - **CategoryUsageIdForCategory2** → **CategoryUsageIdForCategory2**
  - **CategoryCode2** → **CategoryCode2**
  - **CategoryUsageIdForCategory3** → **CategoryUsageIdForCategory3**
  - **CategoryCode3** → **CategoryCode3**
  - **ImportReference** → **ImportReference**
  - **WorkflowCode** → **WorkflowCode**
  - **WorkflowStageCode** → **WorkflowStageCode**
  - **CompanyDivisionId** → **CompanyDivisionId**
  - **TransactionDate** → **TransactionDate**
  - **AccountOrderNumber** → **AccountOrderNumber**
  - Mappings continue for additional category fields, including `CategoryUsageIdForCategory4` to `CategoryCode6`, as well as other specific address and transaction fields, as outlined in the configuration images.

**Explanation:** The **TransactionHeaderImport** transformation is the final stage in loading the processed header data into the target database table. This step concludes the data flow for header records, ensuring that all relevant information, from address details to category and workflow codes, is accurately stored for future use or reporting. This completes the header portion of the transaction import process, making it available in the system for subsequent business operations.