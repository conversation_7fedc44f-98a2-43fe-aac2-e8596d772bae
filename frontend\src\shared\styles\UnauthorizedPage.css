/* frontend/src/shared/styles/UnauthorizedPage.css */

.unauthorized-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh; /* Ensure it takes up significant vertical space */
    padding: 40px 20px;
    box-sizing: border-box;
  }
  
  .unauthorized-content {
    max-width: 500px;
    text-align: center;
    background-color: var(
      --card-background
    ); /* Optional: Use card background for consistency */
    padding: 30px 40px; /* Optional: Padding if using card background */
    border-radius: 8px; /* Optional: Rounded corners if using card background */
    box-shadow: 0 2px 8px var(--shadow-color); /* Optional: Shadow if using card background */
  }
  
  .unauthorized-icon {
    font-size: 3rem; /* Make the icon prominent */
    color: var(--message-error-text); /* Use error text color for emphasis */
    margin-bottom: 20px;
  }
  
  .unauthorized-heading {
    color: var(--text-color);
    font-size: 1.6rem;
    margin-bottom: 15px;
    font-weight: 600;
  }
  
  .unauthorized-text {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 10px;
  }
  
  .unauthorized-text:last-of-type {
    margin-bottom: 0; /* Remove margin from the last paragraph */
  }