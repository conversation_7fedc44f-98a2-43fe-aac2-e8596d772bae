/* frontend/src/order-management/styles/DashboardPage.css */

/* Styles specific to the Dashboard Page, using variables from theme.css */

/* ======================================== */
/* --- General Layout --- */
/* ======================================== */
.dashboard-container {
  padding: 20px 0; /* Vertical padding only */
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-header h1 {
  color: var(--text-color);
  font-size: 1.8rem;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* ======================================== */
/* --- Card Styles --- */
/* ======================================== */
.search-card,
.results-card {
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
  transition: background-color 0.3s ease; /* Keep transition if needed */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.card-header h2 {
  color: var(--text-color);
  font-size: 1.2rem;
  margin: 0;
}

/* ======================================== */
/* --- Search Form & Tabs --- */
/* ======================================== */
.search-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: 12px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
  transition: color 0.2s, border-color 0.2s;
}

.tab-button:hover:not(:disabled) {
  color: var(--text-color);
}

.tab-button.active {
  color: var(--button-primary-bg); /* Use theme color */
  border-bottom-color: var(--button-primary-bg);
  font-weight: 600;
}

.tab-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.search-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-color);
  font-weight: 500;
}

.form-group input[type="text"] {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  background-color: var(--input-background);
  color: var(--text-color);
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input[type="text"]:focus {
  outline: none;
  border-color: var(--button-primary-bg);
  /* Keep focus ring, adjust color if needed */
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3);
}

.form-group input[type="text"]:disabled {
  background-color: var(--input-disabled-bg);
  cursor: not-allowed;
  opacity: 0.7; /* Optional: slightly dim disabled input */
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

/* ======================================== */
/* --- Buttons (Dashboard Specific) --- */
/* ======================================== */
/* General button styling uses theme variables */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, opacity 0.2s;
  text-decoration: none;
  white-space: nowrap;
}

.primary-button {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

.primary-button:hover:not(:disabled) {
  background-color: var(--button-primary-hover-bg);
}

.secondary-button {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--border-color);
}

.secondary-button:hover:not(:disabled) {
  background-color: var(--button-secondary-hover-bg);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button .icon {
  font-size: 1em;
}

.export-button {
  /* Inherits .button styles */
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  font-size: 0.9rem;
}

.export-button:hover:not(:disabled) {
  background-color: var(--button-secondary-hover-bg);
}

/* ======================================== */
/* --- Status Tabs --- */
/* ======================================== */

.status-tabs {
  display: flex;
  gap: 10px;
  padding: 15px 20px 10px;   /* align with tab bar */
  border-bottom: 1px solid var(--border-color);
}

.status-button {
  padding: 8px 18px;
  background: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 30px;
  font-size: 0.85rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: background-color 0.2s, colour 0.2s, border-color 0.2s;
  min-width: 80px;
}

.status-button:hover:not(:disabled) {
  background-color: var(--table-row-hover);
  color: var(--text-color);
}

.status-button.active {
  background-color: var(--button-primary-bg);
  border-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  font-weight: 600;
}

.status-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ======================================== */
/* --- Loading --- */
/* ======================================== */

.loading-indicator {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.large-spinner {
  font-size: 2rem;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ======================================== */
/* --- Hierarchical Results List --- */
/* ======================================== */
.hierarchical-results .results-list-container {
  padding: 0;
}

.origin-group,
.reference-group {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  /* margin-bottom: 15px; */ /* Removed bottom margin */ 
  background-color: var(--card-background);
  overflow: hidden;
}

.origin-group:last-child,
.reference-group:last-child {
  margin-bottom: 0;
}

.reference-group {
    border-radius: 4px;
    margin-bottom: 10px;
}

.group-header {
  display: flex;
  align-items: center; /* Align icon and content vertically */
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid var(--border-color);
  gap: 10px; /* Gap between icon, grid, line-count */
}

/* Remove bottom border if group is collapsed */
.origin-group:not(:has(.references-container)) .origin-header,
.reference-group:not(:has(.lines-container)) .reference-header {
  border-bottom: none;
}

.origin-header {
  background-color: var(--table-header-bg);
  font-size: 1.05rem;
}

.origin-header:hover {
  background-color: var(--table-row-hover);
}

.reference-header {
  font-size: 0.95rem;
  background-color: var(--input-background);
}

.reference-header:hover {
  background-color: var(--table-row-hover);
}

.expand-icon {
  color: var(--text-secondary);
  width: 16px; /* Fixed width for icon */
  text-align: center;
  flex-shrink: 0; /* Prevent icon shrinking */
  font-size: 0.8em;
  transition: transform 0.2s ease-in-out;
  margin-top: 2px; /* Fine-tune vertical alignment with text */
}

/* --- Grid Layout for Header Details (Req 2) --- */
.header-grid {
  display: grid;
  gap: 8px 15px; /* Row and Column gap */
  align-items: baseline; /* Align text within grid cells */
  flex-grow: 1; /* Allow grid to take available space */
  overflow: hidden; /* Prevent grid from overflowing */
}

.origin-header-grid {
  /* Origin | Created | Postcode | Account No (optional) */
  /* Use fixed widths for initial columns for better alignment */
  /* Adjust px values based on typical content + desired spacing */
  grid-template-columns: 180px 180px 180px 1fr;
  /* Fallback for when Account No is hidden (adjust column count/template if needed) */
  /* Consider dynamically changing the template based on showAccountNo if complex */
}

.reference-header-grid {
  /* Reference | Stage */
  /* Use fixed widths for initial columns */
  /* Adjust px values based on typical content + desired spacing */
  grid-template-columns: 200px 1fr;
}

/* Style for individual detail items within the grid */
.header-detail {
  font-size: 0.9rem;
  color: var(--text-secondary);
  white-space: nowrap;
  /* Allow content to be truncated if grid column is constrained */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Value alignment handled in value-display class */

.header-detail strong {
  color: var(--text-color);
  font-weight: 600;
  margin-right: 5px;
  display: inline-block; /* Or block if needed */
}

/* Line count pushed to the right */
.line-count {
  margin-left: auto; /* Keep this to push it right */
  padding-left: 15px;
  white-space: nowrap;
  font-size: 0.85rem;
  color: var(--text-secondary);
  flex-shrink: 0; /* Prevent shrinking */
  align-self: center; /* Vertically align with icon/grid */
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Value display within line count */
.value-display {
  color: var(--text-color);
  font-size: 0.9rem;
  white-space: nowrap;
}

.references-container {
  padding: 10px 10px 10px 30px;
}

.lines-container {
  padding: 10px 15px 15px 30px;
}

/* --- Line Item Table Styles --- */
.lines-header,
.line-data-row {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color-light);
  font-size: 0.9rem;
}

.lines-header {
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 5px;
}

.line-data-row:last-child {
  border-bottom: none;
}

.line-cell {
  padding: 0 8px;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

.line-data-row .line-cell {
  min-height: 2.5em;
  align-items: flex-start;
  padding-top: 8px;
  padding-bottom: 8px;
}

/* Column Widths & Specific Styles */
.product-cell { flex-basis: 15%; }
.name-cell {
  flex-basis: 30%;
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
}
.qty-cell { flex-basis: 10%; justify-content: flex-end; }
.value-cell { flex-basis: 15%; justify-content: flex-end; }
.dispatch-cell { flex-basis: 30%; }

/* Header cell alignment */
.lines-header .qty-cell,
.lines-header .value-cell {
  justify-content: flex-end;
}