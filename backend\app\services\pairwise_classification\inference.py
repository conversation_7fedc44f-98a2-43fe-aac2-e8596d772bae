# backend/app/services/pairwise_classification/inference.py

import logging
import torch
from .load_data import <PERSON><PERSON><PERSON><PERSON>, PairGenerator
from .feat_extraction import (
    create_feature_vector_batch,
)
import numpy as np
from .neural_network import SimpleNN
from .grouping import (
    build_graph,
    find_clusters_with_index,
)
import json
import copy
import os


logger = logging.getLogger(__name__)

# Load the saved model
input_size = 1564
model = SimpleNN(input_size)

# Check if GPU is available and set the device accordingly
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Get model path
NN_MODEL_PATH = os.environ["NN_MODEL_PATH"]

# Always map the model to CPU
model.load_state_dict(torch.load(NN_MODEL_PATH, map_location=device))
model.eval()
model.to(device)


def predict_pairs(all_pairs, batch_size=64):
    predictions = []
    with torch.no_grad():  # Disable gradient computation
        for i in range(0, len(all_pairs), batch_size):
            batch_pairs = all_pairs[i : i + batch_size]
            feature_vectors, result_ids = create_feature_vector_batch(batch_pairs)
            feature_vectors = torch.tensor(feature_vectors).float().to(device)

            outputs = model(feature_vectors)
            predicted_labels = torch.round(outputs).cpu().numpy()

            # Store predictions with their original result IDs
            for (result_id1, result_id2), prediction in zip(
                result_ids, predicted_labels
            ):
                predictions.append(
                    (
                        result_id1,
                        result_id2,
                        bool(prediction),
                    )
                )

    return predictions


def get_groups(json):
    # Filter out annotations that aren't product related
    allowed_labels = [
        "Product_Code",
        "Product_Name",
        "Product_Qty",
        "Unit_Cost",
        "Line_Value",
    ]

    json_copy = copy.deepcopy(json)
    for item in json_copy:
        item["annotations"] = [
            annotation
            for annotation in item["annotations"]
            if annotation["label"] in allowed_labels
        ]

    # Count the occurrences of each label
    label_count = {label: 0 for label in allowed_labels}
    for item in json_copy:
        for annotation in item["annotations"]:
            label_count[annotation["label"]] += 1

    # Check if there are equal numbers of annotations for each label (allowing some labels to be zero)
    non_zero_counts = [count for count in label_count.values() if count > 0]

    # Proceed if all non-zero counts are equal
    if len(set(non_zero_counts)) == 1:
        logger.info("Equal number of annotations for each non-zero label.")
        # Assign nth appearance grouping
        appearance_counts = {
            label: 0 for label in allowed_labels
        }  # Track appearance counts per label

        for item in json_copy:
            for annotation in item["annotations"]:
                label = annotation["label"]
                if (
                    label_count[label] > 0
                ):  # Only assign group if the label has non-zero count
                    appearance_counts[label] += 1
                    annotation["group"] = appearance_counts[
                        label
                    ]  # Assign the nth appearance as group

        # Return dictionary of annotation IDs and their assigned groups
        return {
            annotation["id"]: annotation["group"]
            for item in json_copy
            for annotation in item["annotations"]
        }

    # Load data and generate pairs
    try:
        data_loader = DataLoader()
    except Exception as e:
        logger.error(f"Error initializing DataLoader: {str(e)}")
    try:
        data_loader.input_json(json_copy)
    except Exception as e:
        logger.error(f"Error loading JSON data: {str(e)}")
    try:
        parsed_data = data_loader.get_annotation_data()
    except Exception as e:
        logger.error(f"Error parsing JSON data: {str(e)}")

    # Generate all pairs
    try:
        pair_generator = PairGenerator()
    except Exception as e:
        logger.error(f"Error initializing PairGenerator: {str(e)}")
    try:
        pair_generator.generate_inference_pairs(parsed_data)
    except Exception as e:
        logger.error(f"Error generating pairs: {str(e)}")
    try:
        all_pairs = pair_generator.get_pairs(all=True)
    except Exception as e:
        logger.error(f"Error getting pairs: {str(e)}")

    # Predict which pairs belong to the same product group
    try:
        predictions = predict_pairs(all_pairs)
    except Exception as e:
        logger.error(f"Error predicting pairs: {str(e)}")

    # Build the graph from predictions
    try:
        G = build_graph(predictions)
    except Exception as e:
        logger.error(f"Error building graph: {str(e)}")

    # Find clusters and get a dictionary of result_id to cluster index
    try:
        cluster_dict = find_clusters_with_index(G)
    except Exception as e:
        logger.error(f"Error finding clusters: {str(e)}")

    # Return the cluster dictionary
    if cluster_dict:
        logger.info("Groups found successfully.")
    return cluster_dict


if __name__ == "__main__":

    # Read JSON data from backend/data/RESULTS_json/06999-Cosy_0.json
    with open("test.json") as f:
        data = json.load(f)

    cluster_dict = get_groups(data)

    print(cluster_dict)
