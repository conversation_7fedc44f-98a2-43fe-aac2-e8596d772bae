# Feature Extraction Script

This script extracts features from images using a combination of the VGG16 model and Histogram of Oriented Gradients (HOG). The extracted features are saved in a numpy file for further processing.

## Dependencies

- `os`
- `numpy`
- `PIL` (Pillow)
- `skimage.feature` (HOG)
- `tensorflow`
- `tensorflow.keras.applications.vgg16`
- `tensorflow.keras.preprocessing`
- `tensorflow.keras.models`

## Class: FeatureExtractor

### __init__(self)
Initializes the `FeatureExtractor` class by loading the pre-trained VGG16 model up to the `fc1` layer.

### extract_vgg16_features(self, img_path)
Extracts features from an image using the VGG16 model.

- **Parameters**:
  - `img_path` (str): Path to the image file.

- **Returns**:
  - `vgg16_feature` (numpy.ndarray): Flattened array of VGG16 features.

### extract_hog_features(self, img_path)
Extracts Histogram of Oriented Gradients (HOG) features from an image.

- **Parameters**:
  - `img_path` (str): Path to the image file.

- **Returns**:
  - `hog_feature` (numpy.ndarray): Array of HOG features.

### extract_combined_features(self, img_path)
Extracts and combines VGG16 and HOG features from an image.

- **Parameters**:
  - `img_path` (str): Path to the image file.

- **Returns**:
  - `combined_features` (numpy.ndarray): Concatenated array of VGG16 and HOG features.

## Function: save_features(image_folder, output_file)
Extracts features from all images in a specified folder and saves them in a numpy file.

- **Parameters**:
  - `image_folder` (str): Path to the folder containing image files.
  - `output_file` (str): Path to the output numpy file where features will be saved.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the image folder and output file paths.
2. Calls `save_features` to extract and save features from all images in the specified folder.

## Usage

Run the script using Python:

```bash
python feature_extraction.py
```

## Logging

The script prints information about the feature extraction process for each image, helping to track progress and identify any issues.

## Error Handling

Errors encountered during the feature extraction process should be handled appropriately within the context of your application. Currently, the script will print the progress and any issues encountered during feature extraction.
