/* frontend/src/sphere/styles/PendingJobsPage.css */

/* Import design system */
@import '../../shared/styles/design-system.css';

/* Main Container */
.pending-jobs-page {
    padding: var(--ds-spacing-3xl) 0;
    font-family: var(--ds-font-family);
    background-attachment: fixed;
    position: relative;
}

/* Subtle background pattern */
.pending-jobs-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: var(--ds-pattern-dots);
    opacity: 0.5;
    z-index: -1;
    pointer-events: none;
}

/* Page Header */
.page-header {
    margin-bottom: var(--ds-spacing-4xl);
    position: relative;
    padding-bottom: var(--ds-spacing-lg);
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: var(--ds-border-width-accent);
    background: var(--ds-accent-1);
    border-radius: 2px;
}

.page-header h1 {
    color: var(--ds-text);
    font-size: var(--ds-font-size-4xl);
    margin-bottom: var(--ds-spacing-md);
    font-weight: var(--ds-font-weight-bold);
    letter-spacing: var(--ds-letter-spacing-tight);
    position: relative;
    display: inline-block;
}

.page-subtitle {
    color: var(--ds-text-secondary);
    font-size: var(--ds-font-size-lg);
    line-height: var(--ds-line-height-normal);
    max-width: 700px;
    font-weight: var(--ds-font-weight-regular);
}

/* Table Container */
.table-container {
    background-color: var(--ds-card);
    border-radius: var(--ds-border-radius-lg);
    box-shadow: var(--ds-card-shadow);
    overflow: hidden;
    transition: all var(--ds-transition-normal);
    border: var(--ds-card-border-subtle);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
    animation: ds-fade-in-up 0.6s ease-out forwards;
}

/* Table Styling */
.jobs-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: var(--ds-font-size-sm);
}

.jobs-table th,
.jobs-table td {
    padding: var(--ds-spacing-lg) var(--ds-spacing-xl);
    text-align: left;
    border-bottom: var(--ds-border-width) solid var(--ds-border-light);
    color: var(--ds-text);
    vertical-align: middle;
    transition: background-color var(--ds-transition-fast);
}

.jobs-table th {
    background-color: var(--ds-card);
    font-weight: var(--ds-font-weight-semibold);
    font-size: var(--ds-font-size-xs);
    text-transform: uppercase;
    letter-spacing: var(--ds-letter-spacing-wide);
    color: var(--ds-text-secondary);
    padding-top: var(--ds-spacing-xl);
    padding-bottom: var(--ds-spacing-xl);
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 0 var(--ds-border-light);
}

.jobs-table tbody tr {
    transition: all var(--ds-transition-normal);
}

.jobs-table tbody tr:last-child td {
    border-bottom: none;
}

.jobs-table tbody tr:hover {
    background-color: var(--ds-background-light);
    box-shadow: var(--ds-shadow-sm);
}

/* Status Indicators */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--ds-spacing-xs) var(--ds-spacing-md);
    border-radius: var(--ds-border-radius-full);
    font-size: var(--ds-font-size-xs);
    font-weight: var(--ds-font-weight-medium);
    gap: var(--ds-spacing-xs);
    transition: all var(--ds-transition-fast);
}

.status-badge .status-icon {
    font-size: var(--ds-font-size-md);
}

.status-badge.pending {
    background-color: var(--ds-status-info-bg);
    color: var(--ds-status-info);
}

.status-badge.processing {
    background-color: var(--ds-status-warning-bg);
    color: var(--ds-status-warning);
}

.status-badge.completed {
    background-color: var(--ds-status-success-bg);
    color: var(--ds-status-success);
}

.status-badge.failed {
    background-color: var(--ds-status-error-bg);
    color: var(--ds-status-error);
}

/* PDF Name Column */
.pdf-name-cell {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-md);
}

.pdf-icon {
    color: var(--ds-primary);
    font-size: var(--ds-font-size-xl);
    opacity: 0.8;
}

.pdf-name {
    font-weight: var(--ds-font-weight-medium);
    word-break: break-word;
    max-width: 300px;
}

/* Action Buttons */
.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-sm);
    padding: var(--ds-button-padding-y) var(--ds-button-padding-x);
    border-radius: var(--ds-button-radius);
    font-size: var(--ds-font-size-xs);
    font-weight: var(--ds-font-weight-medium);
    cursor: pointer;
    transition: all var(--ds-transition-normal);
    text-decoration: none;
    border: var(--ds-border-width) solid transparent;
    background-color: var(--ds-background-light);
    color: var(--ds-text);
    position: relative;
    overflow: hidden;
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--ds-gradient-shine);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.action-button:hover {
    background-color: var(--ds-background-hover);
    border-color: var(--ds-border-light);
}

.action-button:hover::before {
    transform: translateX(100%);
    transition: transform var(--ds-transition-shine);
}

.action-button.primary {
    background-color: var(--ds-primary);
    color: white;
}

.action-button.primary:hover {
    background-color: var(--ds-primary-dark);
}

/* Loading State */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--ds-spacing-6xl) 0;
    gap: var(--ds-spacing-xl);
    animation: ds-fade-in 0.5s ease-out;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 120, 212, 0.1);
    border-radius: 50%;
    border-top-color: var(--ds-primary);
    animation: ds-spin 1s linear infinite;
}

.loading-text {
    color: var(--ds-text-secondary);
    font-size: var(--ds-font-size-lg);
    font-weight: var(--ds-font-weight-medium);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--ds-spacing-6xl) 0;
    gap: var(--ds-spacing-xl);
    text-align: center;
    animation: ds-fade-in 0.5s ease-out;
}

.empty-icon {
    font-size: var(--ds-font-size-5xl);
    color: var(--ds-text-secondary);
    opacity: 0.5;
}

.empty-title {
    font-size: var(--ds-font-size-2xl);
    font-weight: var(--ds-font-weight-semibold);
    color: var(--ds-text);
    margin-bottom: var(--ds-spacing-sm);
}

.empty-description {
    color: var(--ds-text-secondary);
    max-width: 400px;
    margin-bottom: var(--ds-spacing-xl);
}

/* Error State */
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--ds-spacing-6xl) 0;
    gap: var(--ds-spacing-xl);
    text-align: center;
    animation: ds-fade-in 0.5s ease-out;
}

.error-icon {
    font-size: var(--ds-font-size-5xl);
    color: var(--ds-status-error);
    opacity: 0.8;
}

.error-title {
    font-size: var(--ds-font-size-2xl);
    font-weight: var(--ds-font-weight-semibold);
    color: var(--ds-text);
    margin-bottom: var(--ds-spacing-sm);
}

.error-message {
    color: var(--ds-text-secondary);
    max-width: 400px;
    margin-bottom: var(--ds-spacing-xl);
}

/* Animations are now imported from design-system.css */

/* Responsive Adjustments */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: var(--ds-font-size-3xl);
    }

    .page-subtitle {
        font-size: var(--ds-font-size-md);
    }

    .jobs-table {
        display: block;
    }

    .jobs-table thead {
        display: none;
    }

    .jobs-table tbody,
    .jobs-table tr,
    .jobs-table td {
        display: block;
        width: 100%;
    }

    .jobs-table tr {
        margin-bottom: var(--ds-spacing-lg);
        border-bottom: var(--ds-border-width-thick) solid var(--ds-border);
        padding: var(--ds-spacing-md);
        border-radius: var(--ds-border-radius-md);
        background-color: var(--ds-card);
    }

    .jobs-table td {
        text-align: right;
        padding: var(--ds-spacing-md) var(--ds-spacing-sm);
        position: relative;
        padding-left: 50%;
    }

    .jobs-table td::before {
        content: attr(data-label);
        position: absolute;
        left: var(--ds-spacing-sm);
        width: 45%;
        padding-right: var(--ds-spacing-sm);
        text-align: left;
        font-weight: var(--ds-font-weight-semibold);
        color: var(--ds-text-secondary);
        font-size: var(--ds-font-size-xs);
    }

    .pdf-name-cell {
        justify-content: flex-end;
    }

    .pdf-name {
        text-align: right;
    }
}

/* Dark mode enhancements */
html.dark-mode .table-container {
    backdrop-filter: blur(5px);
    background-color: rgba(30, 30, 30, 0.8);
}
