# backend/app/auth/jwt_auth.py
import os
import logging
from functools import wraps
from flask import request, jsonify, current_app, g
from gotrue.errors import AuthApiError  # Correct error type from gotrue-py
from supabase import create_client


def validate_token_with_supabase(token: str):
    """
    Validates the JWT by calling Supabase's auth.get_user endpoint.
    """
    logger = current_app.logger if current_app else logging.getLogger(__name__)
    try:
        # Get the initialized Supabase client from the app
        supabase = create_client(
            current_app.config.get("SUPABASE_URL"),
            current_app.config.get("SUPABASE_KEY"),
        )

        # Ask Supabase to validate the token and return user info
        user_response = supabase.auth.get_user(token)

        # If successful, user_response.user contains user details
        user = user_response.user
        if user and user.id:
            logger.info(
                f"Token validated successfully via Supabase for user: {user.id}"
            )
            return user
        else:
            logger.error(
                "Supabase get_user succeeded but returned invalid user object."
            )
            raise AuthApiError("Invalid user data received from Supabase", 500)

    except AuthApiError as e:
        logger.error(
            f"Supabase token validation failed: {e.message} (Status: {e.status})"
        )
        raise ValueError(f"Invalid or expired token: {e.message}")
    except Exception as e:
        logger.error(f"Unexpected error during Supabase token validation: {e}")
        raise ValueError(f"Unexpected error validating token: {e}")


def requires_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        logger = current_app.logger if current_app else logging.getLogger(__name__)
        auth_header = request.headers.get("Authorization", None)
        if not auth_header:
            logger.warning("Authorization header missing.")
            return jsonify({"error": "Authorization header missing"}), 401

        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            logger.warning("Invalid authorization header format.")
            return jsonify({"error": "Invalid authorization header format"}), 401

        token = parts[1]

        try:
            user_data = validate_token_with_supabase(token)
            g.user = user_data
            g.access_token = token
            logger.info(f"Successfully authenticated user via Supabase: {user_data.id}")
        except ValueError as e:
            logger.error(f"Authentication failed: {e}")
            return jsonify({"error": "Invalid or expired token"}), 401
        except Exception as e:
            logger.error(f"Unexpected authentication error: {e}")
            return jsonify({"error": "Authentication failed due to server error"}), 500

        return f(*args, **kwargs)

    return decorated


def get_current_user():
    """Helper function to get the current authenticated user object"""
    return g.user if hasattr(g, "user") else None


def get_current_token():
    """Helper function to get the current authenticated user's token"""
    return g.get("access_token", None)
