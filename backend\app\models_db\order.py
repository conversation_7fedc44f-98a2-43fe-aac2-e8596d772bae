# app/models/order.py
from sqlalchemy import Column, Integer, String, ForeignKey, Date, DateTime, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime
from app.extensions import db


class Order(db.Model):
    __tablename__ = "orders"
    id = Column(Integer, primary_key=True)
    pdf_id = Column(Integer, ForeignKey("pdfs.id"), nullable=True)
    # Order details
    address_code = Column(String(255))
    po_number = Column(String(255))
    part_shipment = Column(String(50))
    channel = Column(String(50))
    lea = Column(String(100))
    delivery_instructions = Column(String(255))
    # order_date = Column(Date)
    # contact_name = Column(String(255))
    contact_phone = Column(String(50))
    contact_email = Column(String(255))
    # Delivery address fields
    delivery_organisation_name = Column(String(255))
    delivery_house = Column(String(255))
    delivery_street = Column(String(255))
    delivery_locality = Column(String(255))
    delivery_town = Column(String(255))
    delivery_county = Column(String(255))
    delivery_postcode = Column(String(50))
    delivery_country = Column(String(255))
    # Invoice address fields
    # invoice_organisation_name = Column(String(255))
    # invoice_house = Column(String(255))
    # invoice_street = Column(String(255))
    # invoice_locality = Column(String(255))
    # invoice_town = Column(String(255))
    # invoice_county = Column(String(255))
    # invoice_postcode = Column(String(50))
    # invoice_country = Column(String(255))
    # invoice_email = Column(String(255))
    # invoice_phone = Column(String(50))
    # total_value = Column(Numeric(10, 2))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    lines = relationship(
        "OrderLine", back_populates="order", cascade="all, delete-orphan"
    )
    pdf = relationship("PDF", back_populates="order", uselist=False)


class OrderLine(db.Model):
    __tablename__ = "order_lines"
    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    product_code = Column(String(50))
    product_name = Column(String(255))
    product_qty = Column(Integer)
    unit_cost = Column(Numeric(10, 2))
    # line_value = Column(Numeric(10, 2))

    # Relationships
    order = relationship("Order", back_populates="lines")
