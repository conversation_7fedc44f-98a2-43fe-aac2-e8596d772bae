## Project Overview

Your project is designed to automate the process of extracting, processing, clustering, and labeling purchase order documents. It involves converting PDF documents into images, extracting features, clustering, creating and updating datasets, and finally training machine learning models. Here's a detailed breakdown of each step:

### 1. Convert PDF to Images (`pdf_to_imgs.py`)
This script converts each page of a PDF file into high-quality images using PyMuPDF and PIL. It allows you to transform your purchase order documents into image format for further processing.

### 2. Extract Features (`feature_extraction.py`)
This script extracts features from the converted images using a combination of VGG16 (a pre-trained deep learning model) and HOG (Histogram of Oriented Gradients). The extracted features are used for clustering the images.

### 3. Cluster Images (`clustering.py`)
This script clusters the images based on their extracted features using KMeans clustering. It helps in organizing the images into different clusters based on their content, making it easier to manage and label them.

### 4. Create LLMv3 Dataset (`create_llmv3_dataset.py`)
This script processes the clustered images to create a dataset suitable for training the LayoutLMv3 model. It includes steps like OCR (Optical Character Recognition) to extract text from the images and format the data accordingly.

### 5. Update Dataset (`update_llmv3_dataset.py`)
This script updates the created dataset to split certain strings, making it more suitable for training. It ensures that important strings like product codes and phone numbers are correctly identified and annotated.

### 6. Group Updated LLMv3 Dataset (`group_updated_llmv3_dataset.py`)
This script groups the updated dataset into clusters, preparing it for labeling. It organizes the data by clusters to facilitate easier labeling and management.

### 7. Label Images Using Label Studio
Run the `simple_http_server.py` script to host your images locally, making them accessible for labeling using Label Studio. This script sets up a simple HTTP server with CORS enabled.

### 8. Export and Split Labelled Data (`split_labelled_output_data.py`)
After labeling the images in Label Studio, export the labeled data and use this script to split the labeled annotations into individual files.

### 9. Convert Labeled Data for Training (`labelled_to_training.py`)
This script converts the labeled data into a format suitable for training machine learning models. It normalizes the bounding boxes and prepares the annotations for training.

### 10. Group Training Data by Cluster (`group_training_data.py`)
This script groups the training data by cluster, organizing it for training separate models for each cluster.

### 11. Train Models (`trainer.py`)
Finally, use this script to train a LayoutLMv3 model on each cluster. You can adjust the cluster number to train different models for different clusters.

## Workflow

1. **Convert PDF to Images**: Use `pdf_to_imgs.py` to convert your purchase order PDFs into images.
2. **Extract Features**: Extract features from these images using `feature_extraction.py`.
3. **Cluster Images**: Cluster the images using `clustering.py` to organize them into groups.
4. **Create LLMv3 Dataset**: Use `create_llmv3_dataset.py` to create a dataset suitable for training from the clustered images.
5. **Update Dataset**: Update the dataset to split certain strings using `update_llmv3_dataset.py`.
6. **Group Updated Dataset**: Group the updated dataset into clusters using `group_updated_llmv3_dataset.py`.
7. **Label Images**: Run `simple_http_server.py` to host your images and label them using Label Studio.
8. **Export and Split Labeled Data**: Export the labeled data from Label Studio and split it using `split_labelled_output_data.py`.
9. **Convert Labeled Data**: Convert the labeled data to a format suitable for training using `labelled_to_training.py`.
10. **Group Training Data**: Group the training data by cluster using `group_training_data.py`.
11. **Train Models**: Train a model on each cluster using `trainer.py`, adjusting the cluster number as needed.

## Purpose

The primary purpose of this project is to automate the processing and labeling of purchase order documents. By converting PDFs into images, extracting meaningful features, clustering similar documents, creating and updating datasets, and using machine learning models for predictions, you streamline the workflow of document management and labeling, making it more efficient and scalable.

This project is particularly useful for businesses dealing with large volumes of purchase orders, enabling them to automate the extraction and processing of information from these documents, ultimately improving the accuracy and efficiency of their operations.