// frontend/src/shared/pages/UnauthorizedPage.jsx

import React from "react";
import { FaLock } from "react-icons/fa"; // Using FaLock as an example icon
import "../styles/UnauthorizedPage.css"; // We'll create this next

const UnauthorizedPage = () => {
    return (
        <div className="unauthorized-container">
            <div className="unauthorized-content">
                <FaLock className="unauthorized-icon" aria-hidden="true" />
                <h2 className="unauthorized-heading">Unauthorized Access</h2>
                <p className="unauthorized-text">
                    Sorry, you do not have the necessary permissions to access this page.
                </p>
                <p className="unauthorized-text">
                    Please contact an administrator if you believe this is an error.
                </p>
            </div>
        </div>
    );
};

export default UnauthorizedPage;
