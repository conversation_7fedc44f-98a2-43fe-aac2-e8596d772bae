// frontend/src/order-management/pages/AdHocOrdersPage.jsx

import React, { useState, useRef, useCallback } from 'react';
import { useAuth } from '../../shared/contexts/AuthContext';
import {
    FaSearch,
    FaSpinner,
    FaFileDownload,
    FaChevronDown,
    FaChevronRight,
} from 'react-icons/fa';
import { formatTitleCase, formatCurrency } from '../../shared/utils/formatters';
import toast from 'react-hot-toast';
import '../styles/AdHocOrdersPage.css';

// Helper to parse "dd/mm/yyyy" reliably
const parseDMY = (dateString) => {
    if (!dateString || typeof dateString !== 'string') {
        return null; // Or return new Date(0) if you prefer epoch start for invalid
    }
    const parts = dateString.split('/');
    if (parts.length === 3) {
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10); // Month is 1-based in the string
        const year = parseInt(parts[2], 10);
        // Check if parts are valid numbers and within reasonable ranges
        if (!isNaN(day) && !isNaN(month) && !isNaN(year) &&
            month >= 1 && month <= 12 && day >= 1 && day <= 31) {
            // Note: JavaScript month is 0-indexed (0=Jan, 11=Dec)
            return new Date(year, month - 1, day);
        }
    }
    // Return null or epoch start if parsing fails
    // Returning null is often better to signal an actual invalid date
    return null;
};

const safeJson = async (response) => {
    const text = await response.text();
    try {
        return JSON.parse(text);
    } catch {
        // This is the placeholder your users will actually see
        throw new Error('Sorry, the server returned an unexpected response. Please inform an admin if this repeats.');
    }
};

// Helper Function for Data Grouping
const groupOrderData = (flatOrderLines = []) => {
    const byOrigin = {};

    flatOrderLines.forEach((ln) => {
        const o = ln.OriginReference;
        const r = ln.Reference;
        const lineDateStr = ln['Date Created']

        // Initialize origin group if it doesn't exist
        if (!byOrigin[o]) {
            byOrigin[o] = {
                originReference: o,
                accountOrderNumber: ln['Account Order Number'],
                dateCreated: lineDateStr,
                deliveryPostcode: ln['Delivery Postcode'],
                orderValue: 0,
                references: {},
            };
        }

        // Sum up origin orderValue
        byOrigin[o].orderValue += Number(ln.LineValue) || 0;

        // Track the earliest dateCreated for the origin
        // Ensure valid dates are compared; handle potential null/undefined
        const currentOriginDate = new Date(byOrigin[o].dateCreated);
        const lineDate = parseDMY(lineDateStr);

        // Compare valid dates
        if (lineDate && (!currentOriginDate || lineDate < currentOriginDate)) {
            // Store the string representation of the earliest date found
            byOrigin[o].dateCreated = lineDateStr;
        }

        // Initialize reference group within the origin if it doesn't exist
        if (!byOrigin[o].references[r]) {
            byOrigin[o].references[r] = {
                reference: r,
                stage: ln.Stage,
                orderValue: 0,
                lines: [],
            };
        }

        // Sum up reference orderValue
        byOrigin[o].references[r].orderValue += Number(ln.LineValue) || 0;

        // Add line item details to the reference
        byOrigin[o].references[r].lines.push({
            product: ln.Product,
            name: ln.ProductDescription, // Use 'name' consistently
            expectedDispatchDate: ln['ExpectedDispatchDate'],
            dateInvoiced: ln.DateInvoiced,
            dateCancelled: ln.DateCancelled,
            lineValue: ln.LineValue,
            qty: ln.Qty,
            availability: ln.Availability,
        });
    });

    // Convert the nested objects into arrays for rendering
    const grouped = Object.values(byOrigin).map((orig) => ({
        ...orig,
        references: Object.values(orig.references),
    }));

    // Sort by dateCreated, newest to oldest
    const sortedGrouped = grouped.sort((a, b) => {
        const dateA = parseDMY(a.dateCreated);
        const dateB = parseDMY(b.dateCreated);

        // Handle cases where dates might be null (invalid)
        if (!dateA && !dateB) return 0; // Both invalid, treat as equal
        if (!dateA) return 1;          // Invalid A comes after valid B
        if (!dateB) return -1;         // Invalid B comes after valid A
        // Ascending (Oldest First)
        return dateA - dateB;
    });

    return sortedGrouped;
};

// --- Main Component ---
const AdHocOrdersPage = () => {
    const { session } = useAuth();
    const [searchParams, setSearchParams] = useState({
        reference: '',
        originReference: '',
        addressCode: '',
    });
    const [searchType, setSearchType] = useState('reference');
    const [orderStatus, setOrderStatus] = useState('open');
    const [loading, setLoading] = useState(false);
    // Removed unused orderData state
    // Removed unused expandedRows state
    const [groupedOrders, setGroupedOrders] = useState([]);
    const [expandedOrigins, setExpandedOrigins] = useState(new Set());
    const [expandedRefs, setExpandedRefs] = useState(new Set());

    const fetchControllerRef = useRef(null);

    const backendApiEndpoint = '/om_api/ad-hoc-report/orders';

    // --- Event Handlers ---
    const handleInputChange = useCallback((e) => {
        const { name, value } = e.target;
        setSearchParams((prevParams) => ({ ...prevParams, [name]: value }));
    }, []);

    const handleSearchTypeChange = useCallback((type) => {
        setSearchType(type);
        setSearchParams({ reference: '', originReference: '', addressCode: '' });
        setGroupedOrders([]); // Clear results
        setExpandedOrigins(new Set()); // Reset expansion
        setExpandedRefs(new Set()); // Reset expansion
        if (fetchControllerRef.current) fetchControllerRef.current.abort();
    }, []);

    const resetForm = useCallback(() => {
        setSearchParams({ reference: '', originReference: '', addressCode: '' });
        setGroupedOrders([]); // Clear results
        setExpandedOrigins(new Set()); // Reset expansion
        setExpandedRefs(new Set()); // Reset expansion
        setOrderStatus('open'); // Reset order status to open
        if (fetchControllerRef.current) fetchControllerRef.current.abort();
    }, []);

    // --- Form Validation ---
    const validateForm = () => {
        let paramValue = '';
        switch (searchType) {
            case 'reference':
                paramValue = searchParams.reference;
                break;
            case 'originReference':
                paramValue = searchParams.originReference;
                break;
            case 'addressCode':
                paramValue = searchParams.addressCode;
                break;
            default:
                toast.error('Invalid search type selected.');
                return false;
        }
        if (!paramValue.trim()) {
            const fieldName = searchType.replace(/([A-Z])/g, ' $1').toLowerCase();
            toast.error(`Please enter a value for ${fieldName}.`);
            return false;
        }
        return true;
    };

    // --- Data Fetching ---
    const fetchOrderData = useCallback(async () => {
        if (!validateForm()) return;
        if (!backendApiEndpoint) {
            toast.error('Backend API endpoint is not configured. Please contact support.');
            return;
        }

        if (fetchControllerRef.current) fetchControllerRef.current.abort();
        const ctrl = new AbortController();
        fetchControllerRef.current = ctrl;

        setLoading(true);
        setGroupedOrders([]); // Clear previous results immediately

        try {
            const token = session?.access_token;
            if (!token) {
                throw new Error(
                    'Authentication token not available. Please log in again.'
                );
            }

            const params = new URLSearchParams();
            if (searchType === 'reference')
                params.append('reference', searchParams.reference.trim());
            else if (searchType === 'originReference')
                params.append('origin_reference', searchParams.originReference.trim());
            else params.append('address_code', searchParams.addressCode.trim());
            params.append('order_status', orderStatus);

            const res = await fetch(`${backendApiEndpoint}?${params}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
                signal: ctrl.signal,
            });

            let body;
            if (!res.ok) {
                // If not ok, try parse JSON
                const errBody = await safeJson(res);
                throw new Error(errBody.message || `HTTP error ${res.status}`);
            }


            body = await safeJson(res);
            if (body.status !== 'success') {
                throw new Error(body.message || 'Failed to retrieve data.');
            }

            const flatOrderLines = body.data?.orders || [];

            // Use the helper function to group data
            const groupedData = groupOrderData(flatOrderLines);
            setGroupedOrders(groupedData);

            // Use toast for success/info message
            if (groupedData.length) {
                toast.success(
                    `Found ${flatOrderLines.length} line items across ${groupedData.length} origin references.`
                );
            } else {
                // Use a standard toast for info, or toast.success if preferred
                toast('No matching orders found.', { icon: 'ℹ️' });
            }
        } catch (e) {
            if (e.name !== 'AbortError') {
                console.error('Fetch Error:', e);
                toast.error(e.message || 'An error occurred while fetching data.');
            }
        } finally {
            setLoading(false);
            fetchControllerRef.current = null;
        }
    }, [searchParams, searchType, orderStatus, session]); // Dependencies for useCallback

    // --- Expansion Toggles ---
    const toggleOrigin = useCallback((originReference) => {
        setExpandedOrigins((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(originReference)) {
                newSet.delete(originReference);
            } else {
                newSet.add(originReference);
            }
            return newSet;
        });
    }, []);

    const toggleReference = useCallback((originReference, reference) => {
        const key = `${originReference}::${reference}`;
        setExpandedRefs((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(key)) {
                newSet.delete(key);
            } else {
                newSet.add(key);
            }
            return newSet;
        });
    }, []);

    // --- Render JSX ---
    return (
        <div className="dashboard-container">
            <div className="dashboard-header">
                <h1>Order Status Lookup</h1>
            </div>

            <div className="dashboard-content">
                {/* Search Card */}
                <div className="search-card">
                    <div className="card-header">
                        <h2>Search Orders</h2>
                    </div>
                    <div className="search-tabs">
                        <button
                            className={`tab-button ${searchType === 'reference' ? 'active' : ''
                                }`}
                            onClick={() => handleSearchTypeChange('reference')}
                            disabled={loading}
                        >
                            Order Reference
                        </button>
                        <button
                            className={`tab-button ${searchType === 'originReference' ? 'active' : ''
                                }`}
                            onClick={() => handleSearchTypeChange('originReference')}
                            disabled={loading}
                        >
                            Origin Reference
                        </button>
                        <button
                            className={`tab-button ${searchType === 'addressCode' ? 'active' : ''
                                }`}
                            onClick={() => handleSearchTypeChange('addressCode')}
                            disabled={loading}
                        >
                            Address Code
                        </button>
                    </div>

                    <div className="status-tabs" role="group" aria-label="Order status">
                        {['open', 'closed', 'all'].map((s) => (
                            <button
                                key={s}
                                className={`status-button ${orderStatus === s ? 'active' : ''}`}
                                onClick={() => setOrderStatus(s)}
                                disabled={loading}
                            >
                                {s === 'all' ? 'All' : s.charAt(0).toUpperCase() + s.slice(1)}
                            </button>
                        ))}
                    </div>

                    <div className="search-form">
                        {searchType === 'reference' && (
                            <div className="form-group">
                                <label htmlFor="reference">Order Reference</label>
                                <input
                                    id="reference"
                                    type="text"
                                    name="reference"
                                    placeholder="Enter order reference"
                                    value={searchParams.reference}
                                    onChange={handleInputChange}
                                    disabled={loading}
                                />
                            </div>
                        )}
                        {searchType === 'originReference' && (
                            <div className="form-group">
                                <label htmlFor="originReference">Origin Reference</label>
                                <input
                                    id="originReference"
                                    type="text"
                                    name="originReference"
                                    placeholder="Enter origin reference"
                                    value={searchParams.originReference}
                                    onChange={handleInputChange}
                                    disabled={loading}
                                />
                            </div>
                        )}
                        {searchType === 'addressCode' && (
                            <div className="form-group">
                                <label htmlFor="addressCode">Address Code</label>
                                <input
                                    id="addressCode"
                                    type="text"
                                    name="addressCode"
                                    placeholder="Enter address code"
                                    value={searchParams.addressCode}
                                    onChange={handleInputChange}
                                    disabled={loading}
                                />
                            </div>
                        )}
                        <div className="form-actions">
                            <button
                                className="button primary-button"
                                onClick={fetchOrderData}
                                disabled={loading}
                                aria-busy={loading}
                            >
                                {loading ? (
                                    <FaSpinner className="icon spinner" aria-hidden="true" />
                                ) : (
                                    <FaSearch className="icon" aria-hidden="true" />
                                )}
                                Search
                            </button>
                            <button
                                className="button secondary-button"
                                onClick={resetForm}
                                disabled={loading}
                            >
                                Clear
                            </button>
                        </div>
                    </div>
                </div>

                {/* Results Card */}
                {!loading && groupedOrders.length > 0 && (
                    <div className="results-card hierarchical-results">
                        <div className="card-header">
                            <h2>Orders Found</h2>
                            <button
                                className="export-button"
                                disabled
                                title="Export needs update for grouped data"
                            >
                                <FaFileDownload className="icon" aria-hidden="true" /> Export (TBD)
                            </button>
                        </div>
                        <div className="results-list-container">
                            {groupedOrders.map((orig) => {
                                const isOrigOpen = expandedOrigins.has(orig.originReference);
                                const totalValueFormatted = formatCurrency(orig.orderValue);
                                const showAccountNo = orig.accountOrderNumber && orig.accountOrderNumber !== '1';
                                const totalOriginLines = orig.references.reduce((sum, ref) => sum + ref.lines.length, 0);

                                return (
                                    <div key={orig.originReference} className="origin-group">
                                        <div
                                            className="group-header origin-header"
                                            onClick={() => toggleOrigin(orig.originReference)}
                                            role="button"
                                            aria-expanded={isOrigOpen}
                                            tabIndex={0}
                                            onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && toggleOrigin(orig.originReference)}
                                        >
                                            <span className="expand-icon" aria-hidden="true">
                                                {isOrigOpen ? <FaChevronDown /> : <FaChevronRight />}
                                            </span>
                                            {/* Grid container for details */}
                                            <div className="header-grid origin-header-grid">
                                                <span className="header-detail"><strong>Origin:</strong> {orig.originReference}</span>
                                                <span className="header-detail"><strong>Created:</strong> {orig.dateCreated || '-'}</span>
                                                <span className="header-detail"><strong>Postcode:</strong> {orig.deliveryPostcode || '-'}</span>
                                                {showAccountNo && (
                                                    <span className="header-detail"><strong>Account No:</strong> {orig.accountOrderNumber}</span>
                                                )}
                                            </div>
                                            <span className="line-count header-detail">
                                                <span className="value-display"><strong>Value:</strong> {totalValueFormatted}</span>
                                                ({totalOriginLines} line{totalOriginLines !== 1 ? 's' : ''})
                                            </span>
                                        </div>

                                        {/* References Container */}
                                        {isOrigOpen && (
                                            <div className="references-container">
                                                {orig.references.map((ref) => {
                                                    const key = `${orig.originReference}::${ref.reference}`;
                                                    const isRefOpen = expandedRefs.has(key);
                                                    const refValueFormatted = formatCurrency(ref.orderValue);
                                                    const refLineCount = ref.lines.length;

                                                    // Req 3: Consolidate Stage Display
                                                    const displayStage = (ref.stage === 'Invoiced' || ref.stage === 'Invoiced Drop Ship Sales Order')
                                                        ? 'Invoiced'
                                                        : (ref.stage || '-');
                                                    const isRefConsideredComplete =
                                                        ref.stage === 'Invoiced' ||
                                                        ref.stage === 'Invoiced Drop Ship Sales Order' ||
                                                        ref.stage === 'Lost Sales';

                                                    return (
                                                        <div key={key} className="reference-group">
                                                            {/* Reference Header - Req 2 (Alignment via CSS), Req 3 (Stage) */}
                                                            <div
                                                                className="group-header reference-header"
                                                                onClick={() => toggleReference(orig.originReference, ref.reference)}
                                                                role="button"
                                                                aria-expanded={isRefOpen}
                                                                tabIndex={0}
                                                                onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && toggleReference(orig.originReference, ref.reference)}
                                                            >
                                                                <span className="expand-icon" aria-hidden="true">
                                                                    {isRefOpen ? <FaChevronDown /> : <FaChevronRight />}
                                                                </span>
                                                                {/* Grid container for details */}
                                                                <div className="header-grid reference-header-grid">
                                                                    <span className="header-detail"><strong>Reference:</strong> {ref.reference}</span>
                                                                    <span className="header-detail"><strong>Stage:</strong> {displayStage}</span>
                                                                    {/* Postcode Removed */}
                                                                </div>
                                                                <span className="line-count header-detail">
                                                                    <span className="value-display"><strong>Value:</strong> {refValueFormatted}</span>
                                                                    ({refLineCount} line{refLineCount !== 1 ? 's' : ''})
                                                                </span>
                                                            </div>

                                                            {/* Lines Container - Req 4 (Conditional Header/Date) */}
                                                            {isRefOpen && (
                                                                <div className="lines-container">
                                                                    <div className="lines-header">
                                                                        <div className="line-cell product-cell">Product</div>
                                                                        <div className="line-cell name-cell">Name</div>
                                                                        <div className="line-cell qty-cell">Quantity</div>
                                                                        <div className="line-cell value-cell">Value</div>
                                                                        {/* Req 4: Conditional Header */}
                                                                        <div className="line-cell availability-cell">
                                                                            {isRefConsideredComplete ? 'Completion Date' : 'Availability'}
                                                                        </div>
                                                                    </div>
                                                                    {ref.lines.map((ln, idx) => {
                                                                        // Req 4: Conditional Date Value
                                                                        const displayValue = isRefConsideredComplete
                                                                            ? (ln.dateInvoiced || ln.dateCancelled || '-')
                                                                            : (ln.availability || '-');

                                                                        return (
                                                                            <div className="line-data-row" key={idx}>
                                                                                <div className="line-cell product-cell">{formatTitleCase(ln.product)}</div>
                                                                                <div className="line-cell name-cell">{formatTitleCase(ln.name)}</div>
                                                                                <div className="line-cell qty-cell">{ln.qty ?? '-'}</div>
                                                                                <div className="line-cell value-cell">{formatCurrency(ln.lineValue)}</div>
                                                                                <div className="line-cell availability-cell">{displayValue}</div>
                                                                            </div>
                                                                        );
                                                                    })}
                                                                </div>
                                                            )}
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}

                {/* Loading Indicator: Show only when loading AND no results are displayed yet */}
                {loading && !groupedOrders.length && (
                    <div className="loading-indicator">
                        <FaSpinner className="icon spinner large-spinner" />
                        <p>Loading…</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AdHocOrdersPage;
