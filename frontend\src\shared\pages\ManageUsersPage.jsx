// frontend/src/shared/pages/ManageUsersPage.jsx
import React, { useState, useEffect, useCallback } from "react";
import { supabase } from "../services/supabase";
import { useAuth } from "../contexts/AuthContext";
import toast from "react-hot-toast"
import { FaSpinner } from "react-icons/fa";
import "../styles/ManageUsersPage.css";

const ManageUsersPage = () => {
    const { userProfile: currentUserProfile } = useAuth();
    const [users, setUsers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [updatingUserId, setUpdatingUserId] = useState(null);

    // Fetch all users
    const fetchUsers = useCallback(async () => {
        setLoading(true);
        try {
            const { data, error: fetchError } = await supabase
                .from("profiles")
                .select("user_id, role, email") // Fetch only necessary columns
                .order("email");

            if (fetchError) throw fetchError;

            setUsers(data || []);
        } catch (err) {
            console.error("Error fetching users:", err);
            toast.error(
                `Failed to fetch users. Ensure RLS allows viewing all profiles. (${err.message})`
            );
            setUsers([]); // Clear users on error
        } finally {
            setLoading(false);
        }
    }, []);

    // Run fetchUsers on component mount
    useEffect(() => {
        fetchUsers();
    }, [fetchUsers]);

    // Handle role change toggle
    const handleRoleChange = useCallback(
        async (userId, currentRole, userEmail) => {
            // Only allow toggling between 'viewer' and 'unauthorized' via this UI
            if (!["viewer", "unauthorized"].includes(currentRole)) {
                toast.error(
                    `Cannot change role from '${currentRole}' using this interface.`
                );
                return;
            }

            const newRole =
                currentRole === "viewer" ? "unauthorized" : "viewer";

            setUpdatingUserId(userId);

            const updatePromise = supabase
                .from("profiles")
                .update({ role: newRole })
                .eq("user_id", userId)
                .then(({ error: updateError }) => {
                    // This runs inside the promise resolution
                    if (updateError) throw updateError; // Throw to trigger promise rejection

                    // Update local state on success
                    setUsers((prevUsers) =>
                        prevUsers.map((u) =>
                            u.user_id === userId ? { ...u, role: newRole } : u
                        )
                    );
                });

            // Display toast based on the promise state
            toast.promise(updatePromise, {
                loading: `Updating ${userEmail || userId}...`,
                success: `User ${userEmail || userId} role updated to ${newRole}.`,
                error: (err) =>
                    `Failed to update role: ${err.message || "Check RLS/connection."}`, // Function to get error message
            });

            // Wait for the promise to settle before resetting loading state
            try {
                await updatePromise;
            } catch (err) {
                console.error("Update failed (logged after toast):", err);
            } finally {
                setUpdatingUserId(null);
            }
        },
        []
    );

    // Determine if the action button should be disabled
    const isActionDisabled = (userRole) => {
        // Admins cannot modify superadmins (even if RLS prevents it, good UI practice)
        if (
            currentUserProfile?.role === "admin" &&
            userRole === "superadmin"
        ) {
            return true;
        }
        // Cannot modify roles other than 'viewer' or 'unauthorized' via this button
        if (!["viewer", "unauthorized"].includes(userRole)) {
            return true;
        }
        return false;
    };

    return (
        <div className="manage-users-container">
            <h1>Manage User Roles</h1>

            {loading ? (
                <div className="loading-indicator">
                    <FaSpinner className="icon spinner large-spinner" />
                    <p>Loading users...</p>
                </div>
            ) : (
                <div className="users-table-container">
                    <table className="users-table">
                        <thead>
                            <tr>
                                <th>Email</th>
                                <th>Current Role</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {users.length === 0 && !loading && (
                                <tr>
                                    <td colSpan="3">No users found.</td>
                                </tr>
                            )}
                            {users.map((user) => (
                                <tr key={user.user_id}>
                                    <td data-label="Email">{user.email}</td>
                                    <td data-label="Current Role">{user.role}</td>
                                    <td data-label="Action">
                                        <button
                                            className={`button ${user.role === "unauthorized"
                                                ? "primary-button"          // If unauthorized -> primary
                                                : user.role === "viewer"
                                                    ? "tertiary-warning-button" // If viewer -> tertiary-warning
                                                    : "secondary-button"        // Otherwise (N/A case) -> secondary
                                                } role-toggle-button`}
                                            onClick={() =>
                                                handleRoleChange(user.user_id, user.role, user.email)
                                            }
                                            disabled={
                                                updatingUserId === user.user_id ||
                                                isActionDisabled(user.role)
                                            }
                                        >
                                            {updatingUserId === user.user_id ? (
                                                <FaSpinner className="icon spinner" />
                                            ) : user.role === "unauthorized" ? (
                                                "Set to Viewer"
                                            ) : user.role === "viewer" ? (
                                                "Set to unauthorized"
                                            ) : (
                                                "N/A" // Button is disabled for other roles
                                            )}
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

export default ManageUsersPage;
