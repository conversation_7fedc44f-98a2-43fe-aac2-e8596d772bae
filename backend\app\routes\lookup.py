# app/lookup/routes.py

import logging
from flask import Blueprint, jsonify, request
from ..auth.jwt_auth import requires_auth
from ..services.sql_server.sql_queries import (
    find_customers_by_contact_info,
    find_product_details,
    find_products_by_name,
)
import re

lookup_bp = Blueprint("lookup", __name__)
logger = logging.getLogger(__name__)


@lookup_bp.route("/customer_lookup", methods=["POST"])
@requires_auth
def customer_lookup():
    data = request.get_json()
    phone_numbers = data.get("phone_numbers", [])
    emails = data.get("emails", [])
    postcodes = data.get("postcodes", [])

    # Validate input
    if not phone_numbers and not emails and not postcodes:
        return (
            jsonify(
                {"error": "At least one phone number, email, or postcode is required."}
            ),
            400,
        )

    try:
        customers = find_customers_by_contact_info(phone_numbers, emails, postcodes)
        return jsonify(customers), 200
    except Exception as e:
        logger.error(f"Error in customer_lookup: {e}")
        return jsonify({"error": "Internal server error"}), 500


@lookup_bp.route("/product_lookup", methods=["POST"])
@requires_auth
def product_lookup():
    data = request.get_json()
    product_code = str(data.get("product_code"))
    address_id = int(data.get("address_id"))

    # Validate input - empty check
    if not product_code:
        return jsonify({"error": "Product code is required."}), 400
    if not address_id:
        return jsonify({"error": "Address id is required."}), 400

    valid_product_code_pattern = re.compile(r"^[a-zA-Z0-9-]{1,20}$")
    if not valid_product_code_pattern.match(product_code):
        return (
            jsonify(
                {
                    "error": "Invalid product code format. Only letters, numbers, and hyphens are allowed (max 20 characters)."
                }
            ),
            400,
        )

    try:
        product = find_product_details(product_code, address_id)
        if product:
            return jsonify(product), 200
        else:
            return jsonify({"error": "Product not found."}), 404
    except Exception as e:
        logger.error(f"Error in product_lookup: {e}")
        return jsonify({"error": "Internal server error"}), 500


@lookup_bp.route("/product_lookup_by_name", methods=["POST"])
@requires_auth
def product_lookup_by_name():
    data = request.get_json()
    product_name = data.get("product_name")
    address_id = data.get("address_id")
    product_cost = data.get("product_cost")

    # Validate input
    if not product_name:
        return jsonify({"error": "Product name is required."}), 400
    if not address_id:
        return jsonify({"error": "Address id is required."}), 400

    try:
        product_name = str(product_name)
        address_id = int(address_id)
        products = find_products_by_name(
            product_name=product_name, address_id=address_id, product_cost=product_cost
        )
        if products and len(products) > 0:
            return jsonify(products), 200
        else:
            return jsonify({"error": "No matching products found."}), 404
    except Exception as e:
        logger.error(f"Error in product_lookup_by_name: {e}")
        return jsonify({"error": "Internal server error"}), 500
