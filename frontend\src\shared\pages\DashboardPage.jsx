// frontend/src/shared/pages/DashboardPage.jsx

import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
    FaSearch,
    FaCalendarAlt,
    FaUpload,
    FaClipboardList,
    FaArrowRight
} from 'react-icons/fa';
import '../styles/DashboardPage.css';

const DashboardPage = () => {
    const { userProfile } = useAuth();

    return (
        <div className="dashboard-page">
            <div className="dashboard-header">
                <h1>Dashboard</h1>
                <p className="dashboard-subtitle">
                    Welcome. Select a section to get started.
                </p>
            </div>

            <div className="dashboard-sections">
                {/* Order Management Section */}
                <div className="section-card">
                    <div className="section-header">
                        <h2>Order Management</h2>
                    </div>
                    <div className="section-content">
                        <p>
                            Access and view order information, search for specific orders,
                            and view or edit scheduled back order reports.
                        </p>

                        <div className="section-links">
                            <Link to="/ad-hoc-lookup" className="section-link">
                                <FaSearch className="section-icon" />
                                <span>Ad Hoc Lookup</span>
                                <FaArrowRight className="arrow-icon" />
                            </Link>

                            <Link to="/schedules" className="section-link">
                                <FaCalendarAlt className="section-icon" />
                                <span>Schedules</span>
                                <FaArrowRight className="arrow-icon" />
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Sphere Section */}
                <div className="section-card">
                    <div className="section-header">
                        <h2>Order Input</h2>
                    </div>
                    <div className="section-content">
                        <p>
                            Upload and process purchase orders, review pending
                            jobs, and manage document processing workflows.
                        </p>

                        <div className="section-links">
                            <Link to="/upload-pdf" className="section-link">
                                <FaUpload className="section-icon" />
                                <span>Upload</span>
                                <FaArrowRight className="arrow-icon" />
                            </Link>

                            <Link to="/pending-jobs" className="section-link">
                                <FaClipboardList className="section-icon" />
                                <span>Pending Orders</span>
                                <FaArrowRight className="arrow-icon" />
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Admin Section - Only visible to admins and superadmins */}
                {(userProfile?.role === 'admin' || userProfile?.role === 'superadmin') && (
                    <div className="section-card">
                        <div className="section-header">
                            <h2>Administration</h2>
                        </div>
                        <div className="section-content">
                            <p>
                                Manage user accounts, permissions, and system settings.
                            </p>

                            <div className="section-links">
                                <Link to="/manage-users" className="section-link">
                                    <FaSearch className="section-icon" />
                                    <span>Manage Users</span>
                                    <FaArrowRight className="arrow-icon" />
                                </Link>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default DashboardPage;
