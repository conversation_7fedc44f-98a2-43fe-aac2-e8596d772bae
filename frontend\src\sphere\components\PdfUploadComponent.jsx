// frontend/src/sphere/components/PdfUploadComponent.jsx

import React, { useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaUpload, FaFile, FaCheckCircle, FaSpinner } from 'react-icons/fa';
import { uploadPdf } from '../services/pdfService';
import toast from 'react-hot-toast';
import '../styles/PdfUploadComponent.css';

const PdfUploadComponent = () => {
    const [file, setFile] = useState(null);
    const [isDragging, setIsDragging] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadSuccess, setUploadSuccess] = useState(false);
    const fileInputRef = useRef(null);
    const navigate = useNavigate();

    // Handle file selection
    const handleFileChange = (event) => {
        const selectedFile = event.target.files[0];
        validateAndSetFile(selectedFile);
    };

    // Validate file and set it to state
    const validateAndSetFile = (selectedFile) => {
        if (!selectedFile) {
            return;
        }

        // Check file type
        if (selectedFile.type !== 'application/pdf') {
            toast.error('Only PDF files are allowed.');
            return;
        }

        // Check file size (10MB limit)
        const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
        if (selectedFile.size > MAX_FILE_SIZE) {
            toast.error('File size exceeds the limit of 10MB.');
            return;
        }

        setFile(selectedFile);
    };

    // Handle drag events
    const handleDragEnter = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    }, []);

    const handleDragLeave = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    }, []);

    const handleDragOver = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isDragging) {
            setIsDragging(true);
        }
    }, [isDragging]);

    const handleDrop = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        const droppedFile = e.dataTransfer.files[0];
        validateAndSetFile(droppedFile);
    }, []);

    // Handle file upload
    const handleUpload = async () => {
        if (!file) {
            toast.error('Please select a PDF file to upload.');
            return;
        }

        try {
            setIsUploading(true);
            setUploadProgress(0);
            setUploadSuccess(false);

            // Upload the file with progress tracking
            await uploadPdf(file, (progress) => {
                setUploadProgress(progress);
            });

            // Handle successful upload
            setUploadSuccess(true);
            toast.success('PDF uploaded successfully!');

            // Redirect to the pending jobs page after a short delay
            setTimeout(() => {
                navigate('/pending-jobs');
            }, 1500);
        } catch (error) {
            toast.error(error.message || 'Upload failed. Please try again.');
        } finally {
            setIsUploading(false);
        }
    };

    // Open file dialog when clicking on the drop zone
    const openFileDialog = () => {
        fileInputRef.current.click();
    };

    // Reset the component state
    const handleReset = () => {
        setFile(null);
        setUploadProgress(0);
        setUploadSuccess(false);
        setIsUploading(false);
        // Reset the file input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <div className="pdf-upload-container">
            <div className="pdf-upload-card">
                <h2 className="pdf-upload-title">Upload PDF</h2>
                <p className="pdf-upload-description">
                    Drag and drop a PDF file here or click to select one.
                </p>

                {/* Drop zone */}
                <div
                    className={`pdf-drop-zone ${isDragging ? 'active' : ''} ${uploadSuccess ? 'success' : ''}`}
                    onClick={!isUploading && !uploadSuccess ? openFileDialog : undefined}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                >
                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        accept="application/pdf"
                        className="pdf-file-input"
                        disabled={isUploading || uploadSuccess}
                        // Disable browser autocomplete
                        autoComplete="off"
                        name={`pdf_upload_${Math.random().toString(36).substring(2, 10)}`}
                    />

                    {!file && !isUploading && !uploadSuccess && (
                        <div className="pdf-drop-content">
                            <FaUpload className="pdf-upload-icon" />
                            <p className="pdf-drop-text">
                                {isDragging ? 'Drop your PDF file here' : 'Click or drag a PDF file here'}
                            </p>
                        </div>
                    )}

                    {file && !isUploading && !uploadSuccess && (
                        <div className="pdf-file-selected">
                            <FaFile className="pdf-file-icon" />
                            <p className="pdf-file-name">{file.name}</p>
                            <p className="pdf-file-size">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                    )}

                    {isUploading && (
                        <div className="pdf-upload-progress">
                            <FaSpinner className="pdf-spinner-icon" />
                            <p className="pdf-progress-text">Uploading... {uploadProgress}%</p>
                            <div className="pdf-progress-bar-container">
                                <div
                                    className="pdf-progress-bar"
                                    style={{ width: `${uploadProgress}%` }}
                                ></div>
                            </div>
                        </div>
                    )}

                    {uploadSuccess && (
                        <div className="pdf-upload-success">
                            <FaCheckCircle className="pdf-success-icon" />
                            <p className="pdf-success-text">Upload successful!</p>
                        </div>
                    )}
                </div>

                {/* Action buttons */}
                <div className="pdf-upload-actions">
                    {!uploadSuccess && (
                        <>
                            <button
                                className="pdf-upload-button"
                                onClick={handleUpload}
                                disabled={!file || isUploading || uploadSuccess}
                            >
                                {isUploading ? 'Uploading...' : 'Upload PDF'}
                            </button>

                            {file && !isUploading && (
                                <button
                                    className="pdf-reset-button"
                                    onClick={handleReset}
                                >
                                    Reset
                                </button>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PdfUploadComponent;
