# Simple HTTP Server for Hosting Images

This script sets up a simple HTTP server with CORS (Cross-Origin Resource Sharing) enabled. It serves files from the current directory and its subdirectories, making it suitable for hosting images for labelling using Label Studio.

## Dependencies

- `os`
- `http.server` (<PERSON><PERSON><PERSON><PERSON><PERSON>, SimpleHTTPRequestHandler, test)
- `sys`

## Classes

### CORSRequestHandler(SimpleHTTPRequestHandler)

This class extends `SimpleHTTPRequestHandler` to add CORS headers to the HTTP responses.

#### end_headers(self)
Adds the `Access-Control-Allow-Origin` header to allow cross-origin requests.

## Main Script Execution

The main block of the script performs the following steps:

1. Changes the working directory to the repository root.
2. Starts an HTTP server on port 8080 using the `CORSRequestHandler`.

## Usage

Run the script using Python:

```bash
./simple_http_server.py
```

Or, if the script does not have execution permissions:

```bash
python simple_http_server.py
```

This will start the HTTP server on port 8080 and serve files from the current directory and its subdirectories.

## Important Notes

- Ensure that you have the necessary permissions to run the script. You may need to add execute permissions using `chmod +x simple_http_server.py`.
- The script serves files over HTTP, making it suitable for development and testing purposes, including hosting images for labelling in Label Studio. For production use, consider using a more robust server setup.

## CORS Configuration

The script includes a CORS configuration to allow cross-origin requests by adding the `Access-Control-Allow-Origin` header with the value `*`.

## Error Handling

The script does not include explicit error handling. Ensure that the required ports are available and that the necessary libraries are installed and imported correctly.
