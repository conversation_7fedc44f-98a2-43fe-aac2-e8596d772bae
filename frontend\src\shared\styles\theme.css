/* frontend/src/shared/styles/theme.css */

/* Import the design system */
@import './design-system.css';

/* Legacy variable mappings for backward compatibility */
:root {
  /* Map old variable names to new design system variables */
  --background-color: var(--ds-background);
  --background-light: var(--ds-background-light);
  --background-hover: var(--ds-background-hover);
  --card-background: var(--ds-card);
  --text-color: var(--ds-text);
  --text-secondary: var(--ds-text-secondary);
  --border-color: var(--ds-border);
  --border-color-light: var(--ds-border-light);
  --border-color-lighter: var(--ds-border-lighter);
  --input-background: var(--ds-input-bg);
  --input-disabled-bg: var(--ds-input-disabled-bg);
  --button-primary-bg: var(--ds-primary);
  --button-primary-text: white;
  --button-primary-hover-bg: var(--ds-primary-dark);
  --button-secondary-bg: var(--ds-button-secondary-bg);
  --button-secondary-text: var(--ds-button-secondary-text);
  --button-secondary-hover-bg: var(--ds-button-secondary-hover-bg);
  --button-tertiary-warning-bg: var(--ds-button-tertiary-warning-bg);
  --button-tertiary-warning-text: var(--ds-button-tertiary-warning-text);
  --button-tertiary-warning-hover-bg: var(--ds-button-tertiary-warning-hover-bg);
  --shadow-color: var(--ds-shadow);
  --table-header-bg: var(--ds-table-header-bg);
  --table-row-hover: var(--ds-table-row-hover);
  --navbar-background: var(--ds-navbar-background);
  --navbar-shadow: var(--ds-navbar-shadow);
  --message-success-bg: var(--ds-message-success-bg);
  --message-success-bg-rgb: var(--ds-message-success-bg-rgb);
  --message-success-text: var(--ds-message-success-text);
  --message-error-bg: var(--ds-message-error-bg);
  --message-error-bg-rgb: var(--ds-message-error-bg-rgb);
  --message-error-text: var(--ds-message-error-text);
  --message-info-bg: var(--ds-message-info-bg);
  --message-info-bg-rgb: var(--ds-message-info-bg-rgb);
  --message-info-text: var(--ds-message-info-text);
  --primary-color: var(--ds-primary);
}

  /* Theme toggle button styling */
  .theme-toggle-button {
    background: none;
    border: none;
    color: var(--text-color);
    padding: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    transition: background-color 0.2s;
  }

  .theme-toggle-button:hover {
    background-color: var(--button-secondary-bg);
  }

  /* User dropdown styling */
  .user-dropdown {
    position: relative;
  }

  .user-icon-button {
    background: none;
    border: none;
    color: var(--text-color);
    padding: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    transition: background-color 0.2s;
  }

  .user-icon-button:hover {
    background-color: var(--button-secondary-bg);
  }

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 5px);
    right: 0;
    min-width: 220px;
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-color);
    overflow: hidden;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
  }

  /* Use the design system animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .dropdown-header {
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
  }

  .user-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
    font-size: 0.95rem;
  }

  .user-email {
    color: var(--text-secondary);
    font-size: 0.85rem;
  }

  .dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0;
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 10px 16px;
    text-align: left;
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .dropdown-item:hover {
    background-color: var(--button-secondary-bg);
  }
