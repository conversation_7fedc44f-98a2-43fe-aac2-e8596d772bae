# backend/app/services/pairwise_classification/load_data.py

import json
import itertools


class DataLoader:
    def __init__(self):
        self.data = None
        self.annotation_results = []

    def load_labelled_data(self, file_path):
        """
        Load labelled data from a given JSON file.
        """
        with open(file_path, "r") as f:
            self.data = json.load(f)

    def input_json(self, json_data):
        """
        Load labelled data from a given JSON object.
        """
        self.data = json_data

    def preprocess_item_training(self, data_item):
        """
        Preprocess the loaded data into a format suitable for training.
        """

        item_annotation_results = {}

        # Initialize counts of total occurrences of each label in the document
        label_counts_total = {
            "Product_Code": 0,
            "Product_Name": 0,
            "Product_Qty": 0,
            "Unit_Cost": 0,
            "Line_Value": 0,
        }

        # First pass: Count total occurrences of each label in the document
        for annotation in data_item["annotations"]:
            for result in annotation["result"]:
                if result["type"] == "labels":
                    label = result["value"]["labels"][0]
                    if label in label_counts_total:
                        label_counts_total[label] += 1

        # Initialize counts of current occurrences of each label
        label_counts_current = {
            "Product_Code": 0,
            "Product_Name": 0,
            "Product_Qty": 0,
            "Unit_Cost": 0,
            "Line_Value": 0,
        }

        # Initiate a count of the number of annotations
        annotation_count = 0

        # Second pass: Process annotations and compute nth appearance
        for annotation in data_item["annotations"]:
            for result in annotation["result"]:
                result_id = result["id"]

                # Initialize an entry for this result if not already present
                if result_id not in item_annotation_results:
                    item_annotation_results[result_id] = {
                        "id": result_id,  # Store the result ID
                    }

                # Parse different types of information from the result
                if result["type"] == "rectangle":
                    # Extract the bounding box information
                    item_annotation_results[result_id]["bbox"] = result["value"]
                elif result["type"] == "labels":
                    # Extract the label (e.g., Product_Name, Product_Qty, etc.)
                    label = result["value"]["labels"][0]
                    item_annotation_results[result_id]["label"] = result["value"][
                        "labels"
                    ]
                    # Update current count for this label
                    if label in label_counts_current:
                        label_counts_current[label] += 1
                        # Add the nth appearance of the label
                        current_count = label_counts_current[label]
                        # Store nth appearance
                        item_annotation_results[result_id][
                            "nth_appearance"
                        ] = current_count
                elif result["type"] == "textarea":
                    # Extract the text content within the bounding box
                    item_annotation_results[result_id]["text"] = result["value"][
                        "text"
                    ][0]
                elif result["type"] == "choices":
                    # Extract the product group information
                    item_annotation_results[result_id]["product_group"] = result[
                        "value"
                    ]["choices"][0]

                # Increment the annotation count
                annotation_count += 1

        if annotation_count == 0:
            # If no annotations were found, skip this item
            print("No annotations found for item.")
        else:
            # After looping through, annotation_results now holds all parsed data
            self.annotation_results.append(item_annotation_results)

    def get_annotation_data_training(
        self,
    ):
        """
        Returns the parsed annotation data.
        """
        for item in self.data:
            self.preprocess_item_training(item)
        return self.annotation_results

    def preprocess_item(self, data_item):
        """
        Preprocess the loaded data into a format suitable for training with the new structure.
        """
        item_annotation_results = {}

        # Initialize counts of total occurrences of each label in the document
        label_counts_total = {
            "Product_Code": 0,
            "Product_Name": 0,
            "Product_Qty": 0,
            "Unit_Cost": 0,
            "Line_Value": 0,
        }

        # First pass: Count total occurrences of each label in the document
        for annotation in data_item["annotations"]:
            label = annotation["label"]
            label_counts_total[label] += 1

        # Initialize counts of current occurrences of each label
        label_counts_current = {label: 0 for label in label_counts_total.keys()}

        # Initiate a count of the number of annotations
        annotation_count = 0

        # Second pass: Process annotations and compute nth appearance
        for annotation in data_item["annotations"]:
            result_id = annotation["id"]
            label = annotation["label"]

            # Update current count for this label
            label_counts_current[label] += 1
            current_count = label_counts_current[label]

            # Initialize an entry for this result if not already present
            item_annotation_results[result_id] = {
                "id": result_id,  # Store the result ID
                "bbox": {
                    "x": annotation["box"][0] / 10,
                    "y": annotation["box"][1] / 10,
                    "width": (annotation["box"][2] / 10) - (annotation["box"][0] / 10),
                    "height": (annotation["box"][3] / 10) - (annotation["box"][1] / 10),
                },
                "text": annotation["text"],
                "label": annotation["label"],
                "nth_appearance": current_count,
            }

            # Increment the annotation count
            annotation_count += 1

        if annotation_count == 0:
            pass
        else:
            # After looping through, annotation_results now holds all parsed data
            self.annotation_results.append(item_annotation_results)

    def get_annotation_data(
        self,
    ):
        """
        Returns the parsed annotation data.
        """
        for item in self.data:
            self.preprocess_item(item)
        return self.annotation_results


class PairGenerator:
    def __init__(self):
        self.positive_pairs = []
        self.negative_pairs = []
        self.all_pairs = []
        self.label_counts = {}

    def count_labels_in_document(self, document):
        """
        Count occurrences of specific labels in a document.
        """
        label_count = {
            "Product_Code": 0,
            "Product_Name": 0,
            "Product_Qty": 0,
            "Unit_Cost": 0,
            "Line_Value": 0,
        }

        for bbox_id, item in document.items():
            if "label" in item and item["label"]:
                label = item["label"]  # Without [0] for inference
                if label in label_count:
                    label_count[label] += 1

        return label_count

    def generate_pairs(self, data):
        """
        Generate positive and negative pairs from the given data.
        Positive pairs are bounding boxes with the same product group.
        Negative pairs are bounding boxes with different product groups.
        Add label counts directly as part of the pair.
        """
        # Iterate through the parsed data (each item is a document)
        for document in data:
            # Count labels in the current document
            label_count = self.count_labels_in_document(document)

            # Get all bounding box ids from the document
            result_ids = list(document.keys())

            # Iterate over all combinations of pairs of bounding boxes
            for bbox1, bbox2 in itertools.combinations(result_ids, 2):
                item1 = document[bbox1]
                item2 = document[bbox2]

                # Check if both items have a 'product_group'
                if "product_group" in item1 and "product_group" in item2:
                    # Create a positive pair if they have the same product group
                    if item1["product_group"] == item2["product_group"]:
                        self.positive_pairs.append(
                            (
                                item1["id"],
                                item2["id"],
                                bbox1,
                                bbox2,
                                item1,
                                item2,
                                1,
                                label_count,
                            )
                        )
                    else:
                        # Otherwise, create a negative pair
                        self.negative_pairs.append(
                            (
                                item1["id"],
                                item2["id"],
                                bbox1,
                                bbox2,
                                item1,
                                item2,
                                0,
                                label_count,
                            )
                        )
                else:
                    # If either item does not have a 'product_group', give pair with no label
                    self.all_pairs.append(
                        (
                            item1["id"],
                            item2["id"],
                            bbox1,
                            bbox2,
                            item1,
                            item2,
                            label_count,
                        )
                    )

    def generate_inference_pairs(self, data):
        """
        Generate positive and negative pairs from the given data.
        The first item in the pair should be either 'Product_Code' or 'Product_Name',
        and the second item can be any.
        Positive pairs are bounding boxes with the same product group.
        Negative pairs are bounding boxes with different product groups.
        Add label counts directly as part of the pair.
        """
        valid_first_labels = {"Product_Code", "Product_Name"}

        # Iterate through the parsed data (each item is a document)
        for document in data:
            # Count labels in the current document
            label_count = self.count_labels_in_document(document)

            # Get all bounding box ids from the document
            result_ids = list(document.keys())

            # Iterate over all combinations of pairs of bounding boxes
            for bbox1, bbox2 in itertools.combinations(result_ids, 2):
                item1 = document[bbox1]
                item2 = document[bbox2]

                # Check if item1 or item2 label is 'Product_Code' or 'Product_Name'
                if ("label" in item1 and item1["label"] in valid_first_labels) or (
                    "label" in item2 and item2["label"] in valid_first_labels
                ):  # Needs changing for inference
                    if "product_group" in item1 and "product_group" in item2:
                        # Create a positive pair if they have the same product group
                        if item1["product_group"] == item2["product_group"]:
                            self.positive_pairs.append(
                                (
                                    item1["id"],
                                    item2["id"],
                                    bbox1,
                                    bbox2,
                                    item1,
                                    item2,
                                    1,
                                    label_count,
                                )
                            )
                        else:
                            # Otherwise, create a negative pair
                            self.negative_pairs.append(
                                (
                                    item1["id"],
                                    item2["id"],
                                    bbox1,
                                    bbox2,
                                    item1,
                                    item2,
                                    0,
                                    label_count,
                                )
                            )
                    else:
                        # If either item does not have a 'product_group', give pair with no label
                        self.all_pairs.append(
                            (
                                item1["id"],
                                item2["id"],
                                bbox1,
                                bbox2,
                                item1,
                                item2,
                                0,
                                label_count,
                            )
                        )

    def get_pairs(self, all=False):
        """
        Return the generated positive and negative pairs.
        """
        if all == False:
            return self.positive_pairs, self.negative_pairs
        if all == True:
            return self.all_pairs
