// frontend/src/sphere/utils/formDataUtils.js
import {
    editableFieldDefinitions,
    addressTypesConfig,
    UK_POSTCODE_REGEX,
} from "../config/reviewPageConfig";

export const initializeFormValues = (reviewApiData) => {
    const initialFormValues = {};
    const { pdf, candidates, addresses, products } = reviewApiData;

    // Initialize from editableFieldDefinitions
    Object.entries(editableFieldDefinitions).forEach(([key, config]) => {
        const sourceObject = config.source === "pdf" ? pdf : candidates;
        let processedValue = "";

        const optionsSourceKey = config.optionsKey || key;
        const optionsArray = sourceObject?.[optionsSourceKey];

        if (Array.isArray(optionsArray) && optionsArray.length > 0) {
            processedValue = optionsArray[0];
        }

        initialFormValues[key] =
            processedValue === null || processedValue === undefined
                ? ""
                : String(processedValue);
    });

    // Initialize address fields
    addressTypesConfig.forEach((addrType) => {
        const rawAddressLines =
            addresses?.[addrType.prefix.toLowerCase()] || [];
        let tempLines = [...rawAddressLines];
        initialFormValues[`${addrType.prefix}_name`] =
            tempLines.length > 0 ? tempLines.shift() : "";
        let postcode = "";
        if (tempLines.length > 0) {
            const potentialPostcode = tempLines[tempLines.length - 1];
            if (UK_POSTCODE_REGEX.test(potentialPostcode)) {
                postcode = tempLines.pop();
            }
        }
        initialFormValues[`${addrType.prefix}_postcode`] = postcode;
        initialFormValues[`${addrType.prefix}_country`] = "United Kingdom"; // Default country or logic to determine it
        for (let i = 0; i < 5; i++) {
            initialFormValues[`${addrType.prefix}_addressLine${i + 1}`] =
                tempLines[i] || "";
        }
    });

    // Initialize products - create a mutable copy for editing
    if (Array.isArray(products)) {
        initialFormValues.products = products.map(product => ({ ...product }));
    } else {
        initialFormValues.products = []; // Default to empty array if no products
    }

    return initialFormValues;
};
