# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Install dependencies and packages
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libgomp1 \
    curl \
    apt-transport-https \
    gnupg \
    openssh-client \
    sshpass && \
    rm -rf /var/lib/apt/lists/*

# Install SQL Server ODBC driver
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
    curl https://packages.microsoft.com/config/debian/10/prod.list > /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql17 unixodbc-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install OpenSSL and configure protocols
RUN apt-get update -yqq \
    && apt-get install -y --no-install-recommends openssl \
    && rm -rf /var/lib/apt/lists/*

# Update OpenSSL configuration
RUN sed -i 's/openssl_conf = openssl_init/openssl_conf = default_conf/' /etc/ssl/openssl.cnf && \
    echo "\n[default_conf]\nssl_conf = ssl_sect\n\n[ssl_sect]\nsystem_default = system_default_sect\n\n[system_default_sect]\nMinProtocol = TLSv1.2\nCipherString = DEFAULT@SECLEVEL=0" >> /etc/ssl/openssl.cnf

# Copy requirements before installing
COPY requirements.txt .

# Install Python dependencies
# This will be cached if requirements.txt hasn't changed
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir pyodbc==5.2.0 \
    && pip install --no-cache-dir gunicorn==23.0.0

# Create non-root user and pre-make PaddleOCR dirs
RUN groupadd -r appuser && useradd -r -m -d /home/<USER>
    chown -R appuser:appuser /home/<USER>

# Copy the entire src directory into the container
COPY . .

# Ensure ownership is correct after copying files
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Create directory for uploaded files and output
RUN mkdir -p /app/uploads /app/output

# Expose port 5001
EXPOSE 5001

# Use Gunicorn as the production server
# --reload is for development, remove it for production
CMD ["gunicorn", "--bind", "0.0.0.0:5001", "--workers", "4", "--timeout", "120", "--reload", "--access-logfile", "-", "--error-logfile", "-", "run:app"]