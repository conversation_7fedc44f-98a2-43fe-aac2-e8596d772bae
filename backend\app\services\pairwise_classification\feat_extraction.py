# backend/app/services/pairwise_classification/inference.py

import os
import numpy as np
import torch
from transformers import <PERSON><PERSON>oken<PERSON>, BertModel
from tqdm import tqdm
from .load_data import DataLoader, PairGenerator

# Check if GPU is available and set the device accordingly
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Get model path
BERT_MODEL_PATH = os.environ["BERT_MODEL_PATH"]

# Load pre-trained BERT tokenizer and model for text embedding extraction
tokenizer = BertTokenizer.from_pretrained(BERT_MODEL_PATH, local_files_only=True)
bert_model = BertModel.from_pretrained(BERT_MODEL_PATH, local_files_only=True).to(
    device
)

# Define label encoding as a one-hot vector
label_to_encoding = {
    "Product_Code": [1, 0, 0, 0, 0],
    "Product_Name": [0, 1, 0, 0, 0],
    "Product_Qty": [0, 0, 1, 0, 0],
    "Unit_Cost": [0, 0, 0, 1, 0],
    "Line_Value": [0, 0, 0, 0, 1],
}


def extract_text_features_batch(texts):
    """
    Extract text embeddings in batch using BERT and GPU.
    """
    inputs = tokenizer(texts, return_tensors="pt", padding=True, truncation=True).to(
        device
    )
    outputs = bert_model(**inputs)
    embeddings = outputs.last_hidden_state.mean(dim=1)
    return embeddings.detach().cpu().numpy()  # Move back to CPU


def extract_bbox_features(bbox):
    """
    Extract bounding box features: x, y, width, height.
    """
    return np.array([bbox["x"], bbox["y"], bbox["width"], bbox["height"]])


def encode_label(label):
    """
    Convert label to one-hot encoded vector.
    """
    return label_to_encoding.get(label[0], [0, 0, 0, 0, 0])  # Default is all zeros


def encode_label_inference(label):
    """
    Convert label to one-hot encoded vector.
    """
    return label_to_encoding.get(label, [0, 0, 0, 0, 0])  # Default is all zeros


def calculate_vector_distance(bbox1, bbox2):
    """
    Calculate the vector distance between two bounding boxes.
    """
    x1, y1 = bbox1["x"], bbox1["y"]
    x2, y2 = bbox2["x"], bbox2["y"]

    # Vector distance (difference in x and y coordinates)
    distance_vector = np.array([x2 - x1, y2 - y1])

    return distance_vector


def calculate_iou(bbox1, bbox2):
    """
    Calculate the Intersection over Union (IoU) of two bounding boxes.
    Bounding boxes should be dictionaries with 'x', 'y', 'width', and 'height'.
    """
    # Coordinates of the first box
    x1_min, y1_min = bbox1["x"], bbox1["y"]
    x1_max, y1_max = x1_min + bbox1["width"], y1_min + bbox1["height"]

    # Coordinates of the second box
    x2_min, y2_min = bbox2["x"], bbox2["y"]
    x2_max, y2_max = x2_min + bbox2["width"], y2_min + bbox2["height"]

    # Calculate intersection
    inter_x_min = max(x1_min, x2_min)
    inter_y_min = max(y1_min, y2_min)
    inter_x_max = min(x1_max, x2_max)
    inter_y_max = min(y1_max, y2_max)

    # Check if there is an intersection
    inter_width = max(0, inter_x_max - inter_x_min)
    inter_height = max(0, inter_y_max - inter_y_min)
    intersection_area = inter_width * inter_height

    # Calculate the area of both bounding boxes
    bbox1_area = bbox1["width"] * bbox1["height"]
    bbox2_area = bbox2["width"] * bbox2["height"]

    # Calculate the area of the union
    union_area = bbox1_area + bbox2_area - intersection_area

    # Calculate IoU
    iou = intersection_area / union_area if union_area > 0 else 0

    return iou


def create_feature_vector_batch(pairs):
    """
    Create feature vectors for a batch of pairs of bounding boxes,
    including vector distance, IoU, and document label counts as additional features.
    """
    texts1 = [pair[4]["text"] for pair in pairs]
    texts2 = [pair[5]["text"] for pair in pairs]

    # Batch process text embeddings
    text1_features = extract_text_features_batch(texts1)
    text2_features = extract_text_features_batch(texts2)

    # Process bounding box and label features
    bbox1_features = np.array(
        [extract_bbox_features(pair[4]["bbox"]) for pair in pairs]
    )
    bbox2_features = np.array(
        [extract_bbox_features(pair[5]["bbox"]) for pair in pairs]
    )
    label1_features = np.array(
        [encode_label_inference(pair[4]["label"]) for pair in pairs]
    )  # Needs changing for inference
    label2_features = np.array(
        [encode_label_inference(pair[5]["label"]) for pair in pairs]
    )  # Needs changing for inference

    # Extract normalized nth appearance
    nth1_features = np.array(
        [pair[4].get("nth_appearance", 0) for pair in pairs]
    ).reshape(-1, 1)
    nth2_features = np.array(
        [pair[5].get("nth_appearance", 0) for pair in pairs]
    ).reshape(-1, 1)

    # Calculate vector distance between bounding boxes
    distance_features = np.array(
        [calculate_vector_distance(pair[4]["bbox"], pair[5]["bbox"]) for pair in pairs]
    )

    # Calculate IoU between bounding boxes
    iou_features = np.array(
        [calculate_iou(pair[4]["bbox"], pair[5]["bbox"]) for pair in pairs]
    ).reshape(-1, 1)

    # Add label counts from the current document (propagated with the pair)
    label_count_features = np.array(
        [list(pair[7].values()) for pair in pairs]
    )  # pair[7] holds label_count

    # Combine features (text, bounding boxes, labels, normalized nth, vector distance, IoU, and label counts)
    combined_features = np.concatenate(
        [
            text1_features,
            bbox1_features,
            label1_features,
            nth1_features,
            text2_features,
            bbox2_features,
            label2_features,
            nth2_features,
            distance_features,  # Add vector distance
            iou_features,  # Add IoU as a single feature
            label_count_features,  # Add label count feature
        ],
        axis=1,
    )

    # Return the combined features alongside the result IDs
    result_ids = [(pair[0], pair[1]) for pair in pairs]  # result_id1 and result_id2
    return combined_features, result_ids


def generate_dataset_in_batches(positive_pairs, negative_pairs, batch_size=1000):
    """
    Generate dataset of feature vectors and labels in batches.
    """
    features = []
    labels = []

    # Combine positive and negative pairs
    all_pairs = positive_pairs + negative_pairs
    np.random.shuffle(all_pairs)

    # Process in batches
    for i in tqdm(range(0, len(all_pairs), batch_size)):
        batch_pairs = all_pairs[i : i + batch_size]
        feature_batch, result_ids_batch = create_feature_vector_batch(batch_pairs)
        label_batch = [pair[6] for pair in batch_pairs]

        features.append(feature_batch)
        labels.extend(label_batch)

    return np.vstack(features), np.array(labels)


def save_dataset(features, labels, save_dir="dataset"):
    """
    Save features and labels locally using pickle.
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    with open(os.path.join(save_dir, "features.npy"), "wb") as f_feat:
        np.save(f_feat, features)

    with open(os.path.join(save_dir, "labels.npy"), "wb") as f_labels:
        np.save(f_labels, labels)

    print(f"Dataset saved to {save_dir}.")


if __name__ == "__main__":
    import time

    start_time = time.time()
    # Load data and generate pairs
    data_loader = DataLoader()
    data_loader.load_labelled_data(
        "backend/data/gnn/labelled/gnn_labelled_combined.json"
    )
    parsed_data = data_loader.get_annotation_data_training()

    # Generate positive and negative pairs
    pair_generator = PairGenerator()
    pair_generator.generate_inference_pairs(parsed_data)
    positive_pairs, negative_pairs = pair_generator.get_pairs()

    # Create dataset with feature vectors and labels in batches
    features, labels = generate_dataset_in_batches(
        positive_pairs, negative_pairs, batch_size=1000
    )

    print(f"Dataset created with {len(features)} feature vectors and labels.")

    # Save dataset
    save_dataset(features, labels, save_dir="backend/data/gnn/dataset")

    print(f"Time taken: {time.time() - start_time:.2f} seconds.")
