/* frontend/src/sphere/styles/ReviewPage.css */

/* Page Layout */
.pdf-review-page-layout {
  display: flex;
  height: 100vh; /* Full viewport height */
  overflow: hidden; /* Prevent body scroll, columns handle their own */
  width: 100%;
}

.pdf-review-left-column,
.pdf-review-right-column {
  flex: 1;
  height: 100vh;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
}

.pdf-review-right-column {
  padding-bottom: 55px;
  position: relative; /* Needed for proper modal positioning */
}

.pdf-review-left-column {
  background-color: var(--background-color);
  border-right: 1px solid var(--border-color);
}

/* Ensure left column remains scrollable when modal is open */
body.customer-lookup-modal-open .pdf-review-left-column {
  overflow-y: auto;
}

.pdf-review-left-column::-webkit-scrollbar,
.pdf-review-right-column::-webkit-scrollbar {
  width: 8px;
}

.pdf-review-left-column::-webkit-scrollbar-track,
.pdf-review-right-column::-webkit-scrollbar-track {
  background: var(--border-color-lighter);
  border-radius: 4px;
}

.pdf-review-left-column::-webkit-scrollbar-thumb,
.pdf-review-right-column::-webkit-scrollbar-thumb {
  background-color: var(--text-secondary);
  border-radius: 4px;
  border: 2px solid var(--border-color-lighter);
}

.pdf-review-left-column::-webkit-scrollbar-thumb:hover,
.pdf-review-right-column::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-color);
}

/* Images in Left Column */
.images-container-scrollable {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.image-preview-wrapper {
  width: 100%;
  background-color: var(--border-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 5px var(--shadow-color);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.image-preview-wrapper .loaded-image {
  display: block;
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 100vh;
  object-fit: contain;
}

/* Authenticated Image Placeholders */
.authenticated-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 150px;
  color: var(--text-secondary);
  font-size: 0.9rem;
  text-align: center;
  padding: 10px;
  box-sizing: border-box;
}

.authenticated-image-placeholder.loading .spinner {
  font-size: 2rem;
  margin-bottom: 10px;
}

.authenticated-image-placeholder.error svg {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--message-error-text);
}

.authenticated-image-placeholder.error span {
  font-size: 0.8rem;
}

/* Right Column Content Styling */
.pdf-review-header.section-card {
  border-bottom: none; /* Specific for header card */
}

.pdf-review-header h1 {
  margin: 0 0 10px;
  color: var(--text-color);
  font-size: 1.6rem;
}

.pdf-review-header p {
  margin: 4px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.pdf-review-page-layout .section-card {
  background-color: var(--card-background);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  box-shadow: 0 1px 4px var(--shadow-color);
  margin-bottom: 20px;
  padding: 16px;
}

/* Section headers */
.pdf-review-page-layout .section-card h2 {
  margin-top: 0;
  font-size: 1.25rem;
  color: var(--text-color);
  padding-bottom: 10px;
  margin-bottom: 16px; /* Consistent spacing below all section headings */
  position: relative; /* For the pseudo-element positioning */
}

/* Add a pseudo-element for the border that stretches full width */
.pdf-review-page-layout .section-card h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 10px;
  height: 1px;
  background-color: var(--border-color);
  z-index: 1; /* Ensure it's above other elements */
}

/* General Form Controls */
.form-control {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 0.9rem;
  box-sizing: border-box;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  min-height: 38px; /* Ensure consistent height with buttons etc. */
}

.form-control:focus {
  border-color: var(--button-primary-bg); /* Or your app's focus color */
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--button-primary-bg-rgb), 0.2); /* Example focus ring */
}

/* Candidates Table & Editable Fields */
.candidates-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.candidates-table th,
.candidates-table td {
  text-align: left;
  padding: 10px 8px;
  border-bottom: 1px solid var(--border-color-light);
  vertical-align: middle; /* Align input nicely with its label */
}

.candidates-table th {
  width: 35%; /* Adjust if labels are too cramped or too spacious */
  color: var(--text-secondary);
  font-weight: 600;
}

.candidates-table tr:last-child th,
.candidates-table tr:last-child td {
  border-bottom: none;
}

.editable-field-row th {
  white-space: nowrap; /* Prevent labels from wrapping too aggressively */
  padding-right: 15px; /* Add some space between label and input cell */
}

/* Candidates Grid Layout */
.candidates-section .candidates-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); /* Match the minmax used in addresses-form-grid */
}

.candidate-field-item {
  display: flex;
  flex-direction: column;
  padding: 10px 8px; /* Match the padding used in address-entry-label/input */
  border-bottom: 1px solid var(--border-color-light);
  border-radius: 6px;
  background-color: var(--card-background);
}

.candidate-field-item label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
  margin-bottom: 8px;
  white-space: nowrap;
}

.editable-field-wrapper {
  display: flex;
  align-items: center;
  gap: 8px; /* Space between input/select and toggle button */
}

.editable-field-wrapper .form-control {
  flex-grow: 1; /* Input/select takes available space */
  width: auto; /* Reset width if it was set to 100% by .form-control and flex-basis is preferred */
}

.editable-field-toggle-btn {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 8px; /* Adjust for icon size */
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem; /* Adjust icon size if needed */
  line-height: 1; /* Ensure icon is centered if it has extra line height */
  transition: background-color 0.2s, color 0.2s, border-color 0.2s;
  flex-shrink: 0; /* Prevent button from shrinking */
  height: 38px; /* Match form-control height */
  width: 38px; /* Make it square-ish */
  box-sizing: border-box;
}

.editable-field-toggle-btn:hover {
  background-color: var(--border-color-lighter); /* Subtle hover */
  color: var(--text-color);
  border-color: var(--border-color-dark); /* Or a slightly darker border */
}

.editable-field-toggle-btn:focus,
.editable-field-toggle-btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--button-primary-bg-rgb), 0.3); /* Focus ring */
  border-color: var(--button-primary-bg);
}

/* Style for disabled options in select, e.g., "Custom" hint */
.editable-field-wrapper select option[disabled] {
  color: var(--text-disabled, #999); /* Fallback if --text-disabled is not defined */
}

/* Addresses Grid & Blocks */
.addresses-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); /* Adjusted minmax for better spacing */
  gap: 24px; /* Gap between Delivery and Invoice blocks */
}

.address-form-block {
  display: flex;
  flex-direction: column;
}

.address-form-block h3 {
  font-size: 1.05rem;
  color: var(--text-secondary);
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-lighter);
}

/* New styles for table-like layout within each address block */
.address-fields-table-like {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.address-entry-row {
  display: flex;
  align-items: stretch; /* Make cells same height */
  border-bottom: 1px solid var(--border-color-light);
}

.address-entry-row:last-child {
  border-bottom: none;
}

.address-entry-label,
.address-entry-input {
  padding: 10px 8px; /* Same padding as .candidates-table cells */
  display: flex;
  align-items: center;
}

.address-entry-label {
  width: 15%;
  min-width: 120px; /* Ensure label has some minimum space */
  color: var(--text-secondary);
  font-weight: 600;
  white-space: nowrap;
  padding-right: 5px; /* Space between label and input cell, matches .editable-field-row th */
  box-sizing: border-box;
}
.address-entry-label label {
    width: 100%; /* Make label take full width of its cell */
    font-size: 0.9rem; /* Match candidate label font size */
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Required field styles */
.address-entry-label.required-field label {
  font-weight: 700; /* Slightly bolder for required fields */
}

.required-asterisk {
  color: var(--message-error-text);
  margin-left: 2px;
}

.required-input.invalid {
  border-color: var(--message-error-text);
  background-color: rgba(var(--message-error-bg-rgb), 0.1);
}

.address-entry-input {
  flex-grow: 1;
  width: 65%; /* To complement label's width */
  box-sizing: border-box;
}

/* Ensure EditableAddressField (which is now just the input part effectively)
   takes full width of its container cell and its input behaves like other form-controls */
.address-entry-input .editable-address-field {
  width: 100%;
  padding: 0;
  margin: 0;
}
/* The .editable-address-field.no-internal-label might not need specific styles here
   if the above covers it, but can be used for fine-tuning. */

.address-entry-input .editable-address-field .address-input-interactive-wrapper {
  height: 100%;
}



.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px; /* Space between label and input */
}

.form-group label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Products Section Header */
.products-header,
.addresses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.find-customer-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.products-header h2, .addresses-header h2 {
    margin: 0;
    padding: 0;
    padding-bottom: 10px;
    border-bottom: none;
    font-size: 1.25rem;
    color: var(--text-color);
    width: auto;
    flex: 1; /* Take up available space */
    position: relative; /* For the pseudo-element positioning */
}

/* Override the general h2::after for headers with buttons */
.products-header h2::after, .addresses-header h2::after {
    content: '';
    position: top;
    bottom: 0;
    left: 0;
    right: 10px;
    height: 1px;
    background-color: var(--border-color);
}

/* Products Table */
.table-container {
  overflow-x: auto;
  border: 1px solid var(--border-color-light);
  border-radius: 6px;
  background-color: var(--card-background);
}

.products-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
  min-width: 550px;
  table-layout: fixed;
}

.products-table th,
.products-table td {
  text-align: left;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  transition: background-color 0.15s ease-in-out;
}

.products-table th {
  background-color: var(--table-header-bg);
  position: sticky;
  top: 0;
  z-index: 10;
  font-weight: 600;
  color: var(--text-color);
  padding: 12px 10px;
  border-bottom: 1px solid var(--border-color);
}

.products-table td {
  white-space: normal;
  color: var(--text-color);
  padding: 0;
  border-bottom: none;
  vertical-align: middle;
  background-color: transparent;
}

.products-table tbody tr:hover td {
  background-color: var(--table-row-hover);
}

.products-table th:last-child,
.products-table td.product-action-cell {
  text-align: center;
  vertical-align: middle;
}

.product-action-cell {
  padding: 0 4px; /* Keep cell padding for spacing */
  width: 60px; /* Fixed width for consistent column sizing */
  min-width: 60px; /* Ensure minimum width */
  max-width: 60px; /* Prevent expansion */
}

.product-actions-container {
  display: flex;
  justify-content: center;
  gap: 4px; /* Reduced from 8px to 4px */
}

.product-check-btn {
  color: var(--button-primary-bg);
}

.product-check-btn:hover {
  color: var(--button-primary-hover-bg);
}

.product-check-btn.verified {
  color: var(--message-success-text);
}

/* Change color to error on hover for unverification */
.product-check-btn.verified:hover {
  color: var(--message-error-text);
}

/* Disabled delete button */
.product-delete-btn:disabled {
  color: var(--text-secondary);
  opacity: 0.5;
  cursor: not-allowed;
}

/* Hide delete button for verified products */
.verified-product-row .product-delete-btn {
  visibility: hidden;
  opacity: 0;
  pointer-events: none; /* Prevent interaction */
}

/* Read-only input styling */
.product-cell-input.read-only {
  background-color: var(--input-disabled-bg);
  cursor: not-allowed;
  border-color: transparent;
}

/* Styles for the delete button in the product row */
.product-delete-btn {
  color: var(--message-error-text);
  /* Inherits from .icon-action-button for base style, size, transitions, border-radius (4px) */
  /* border-radius: 50%; */ /* Removed for consistency with .icon-action-button's 4px radius */

  /* Initial state: hidden and scaled down */
  opacity: 0;
  transform: scale(0.8);
}

/* Reveal button when table row is hovered, if not disabled */
.products-table tbody tr:hover .product-delete-btn:not(:disabled):not(.verified-product-row .product-delete-btn) {
  opacity: 0.7;
  transform: scale(1);
}

/* On button's own hover/focus (when visible), make fully opaque */
/* Color/background changes come from .icon-action-button styles */
.product-delete-btn:hover:not(:disabled),
.product-delete-btn:focus:not(:disabled),
.product-delete-btn:focus-visible:not(:disabled) {
  opacity: 1;
}

.products-table-empty-message {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
  font-style: italic;
}

.products-table-empty-message.validation-error {
  color: var(--message-error-text);
  font-weight: 600;
  font-style: normal;
  padding-left: 5px;
}

.products-table tbody tr {
  border-bottom: 1px solid var(--border-color-light);
}
.products-table tbody tr:last-child {
  border-bottom: none;
}

/* Highlight unverified products when validation fails */
.products-table tbody tr.unverified-product-row {
  animation: pulse-highlight 2s ease-in-out infinite;
  border-left: 3px solid var(--message-error-text);
  height: 40px;
}

@keyframes pulse-highlight {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(var(--message-error-bg-rgb), 0.5);
  }
}

.product-cell-input.form-control {
  width: 100%;
  height: 100%;
  border: 1px solid transparent;
  border-radius: 0;
  padding: 12px 10px;
  box-sizing: border-box;
  background-color: transparent;
  font-size: 0.9rem;
  color: var(--text-color);
  transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}

.product-cell-input.form-control:focus {
  border: 1px solid var(--button-primary-bg);
  background-color: var(--card-background);
  outline: none;
  box-shadow: none;
  position: relative;
  z-index: 1;
}

/* Loading & Error States (Full Page) */
.pdf-review-loading,
.pdf-review-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  height: 100vh;
  box-sizing: border-box;
  color: var(--text-secondary);
  background-color: var(--background-color);
}

.pdf-review-loading p,
.pdf-review-error p {
  margin-top: 15px;
  font-size: 1.1rem;
}

.spinner {
  animation: spin 1s linear infinite;
  font-size: 2.8rem;
  color: var(--button-primary-bg);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Submit Button Styles */
.submit-section {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  margin-bottom: 20px; /* Match the margin-bottom of other section cards */
}

.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-sm);
    padding: var(--ds-button-padding-y) var(--ds-button-padding-x);
    border-radius: var(--ds-button-radius);
    font-size: var(--ds-font-size-xs);
    font-weight: var(--ds-font-weight-medium);
    cursor: pointer;
    transition: all var(--ds-transition-normal);
    text-decoration: none;
    border: var(--ds-border-width) solid transparent;
    background-color: var(--ds-background-light);
    color: var(--ds-text);
    position: relative;
    overflow: hidden;
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--ds-gradient-shine);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.action-button:hover {
    background-color: var(--ds-background-hover);
    border-color: var(--ds-border-light);
}

.action-button:hover::before {
    transform: translateX(100%);
    transition: transform var(--ds-transition-shine);
}

.action-button {
    background-color: var(--ds-primary);
    color: white;
}

.action-button:hover {
    background-color: var(--ds-primary-dark);
}