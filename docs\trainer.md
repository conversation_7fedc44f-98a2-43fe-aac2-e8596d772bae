# Training Script for LayoutLMv3

This script trains a LayoutLMv3 model for token classification on annotated data. The training data is loaded from JSON files, processed, and split into training and testing datasets. The model is then trained and evaluated.

## Dependencies

- `json`
- `torch`
- `torch.utils.data`
- `transformers`
- `datasets`
- `PIL`
- `sklearn.metrics`
- `warnings`

## Suppress Warnings

Certain warnings from the `transformers` module are suppressed to avoid cluttering the output.

## Label Mapping

The script uses a predefined label mapping to convert textual labels into numerical values. The mapping is defined as follows:

```python
label_mapping = {
    "PO_Number": 1,
    "Contact_Name": 2,
    "Contact_Phone": 3,
    "Contact_Email": 4,
    "Delivery_Address": 5,
    "Invoice_Address": 6,
    "Product_Code": 7,
    "Product_Qty": 8,
    "Invoice_Email": 9,
    "Product_Name": 0,
    "Unit_Cost": 10,
    "Line_Value": 11,
    "Total_Value": 12,
    "Ignore_Text": 13,
    "Order_Date": 14,
    "Invoice_Phone": 15,
}
```

## Custom Dataset Class

### `CustomDataset(Dataset)`

This class inherits from `torch.utils.data.Dataset` and is used to handle the annotated data.

- **__init__(self, data, processor)**: Initialises the dataset with data and processor.
- **__len__(self)**: Returns the length of the dataset.
- **__getitem__(self, idx)**: Retrieves an item by index, processes the image and annotations, and returns the encoded inputs.

## Custom Metrics Function

### `compute_metrics(p)`

This function computes evaluation metrics for the model.

- **Parameters**:
  - `p`: Predictions and labels.
- **Returns**:
  - `dict`: Dictionary containing precision, recall, F1 score, and accuracy.

## Main Script Execution

The main block of the script performs the following steps:

1. **Load Data**: Loads the data from a specified JSON file.
2. **Initialise Processor**: Initialises the `LayoutLMv3Processor` with a pretrained model.
3. **Create Dataset**: Creates an instance of the `CustomDataset` class with the loaded data and processor.
4. **Split Dataset**: Splits the dataset into training and testing sets.
5. **Define Model**: Defines the `LayoutLMv3ForTokenClassification` model for token classification.
6. **Define Training Arguments**: Specifies training arguments such as batch size, learning rate, logging steps, evaluation steps, etc.
7. **Initialise Trainer**: Initialises the `Trainer` with the model, training arguments, datasets, and metrics.
8. **Train Model**: Trains the model using the `trainer.train()` method.
9. **Save Model and Processor**: Saves the trained model and processor.
10. **Evaluate Model**: Evaluates the model and prints the evaluation results.

## Usage

Run the script using Python:

```bash
python trainer.py
```

## Important Parameters

### Training Arguments

- `num_train_epochs`: Number of training epochs.
- `per_device_train_batch_size`: Training batch size per device.
- `per_device_eval_batch_size`: Evaluation batch size per device.
- `learning_rate`: Learning rate for training.
- `warmup_steps`: Number of warmup steps.
- `weight_decay`: Weight decay for optimisation.
- `logging_steps`: Steps interval for logging.
- `eval_steps`: Steps interval for evaluation.
- `save_steps`: Steps interval for saving the model.
- `load_best_model_at_end`: Whether to load the best model at the end of training.
- `logging_strategy`: Strategy for logging.
- `logging_first_step`: Whether to log the first step.
- `save_total_limit`: Maximum number of saved models.

### Early Stopping

The `Trainer` is configured with early stopping to prevent overfitting. The `EarlyStoppingCallback` is used with a patience of 2.

## Logging

The script logs information about the training process, including the progress and any warnings encountered. 
