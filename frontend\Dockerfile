# Stage 1: Build the React application
# Use a recommended tag with fewer known vulnerabilities and fix casing
FROM node:22-alpine AS builder
# Set the working directory
WORKDIR /app
# Copy package.json and package-lock.json (or yarn.lock)
COPY package*.json ./
# Install dependencies securely and efficiently
RUN npm ci --only=production
# Copy the rest of the application code
COPY . .
# Set API URL during build (adjust if needed, or use runtime env vars)
ARG REACT_APP_SUPABASE_URL
ENV REACT_APP_SUPABASE_URL=${REACT_APP_SUPABASE_URL}
ARG REACT_APP_SUPABASE_ANON_KEY
ENV REACT_APP_SUPABASE_ANON_KEY=${REACT_APP_SUPABASE_ANON_KEY}
# Build the application
RUN npm run build

# Stage 2: Serve the application with Nginx
FROM nginx:stable-alpine
# Copy the build output from the builder stage
COPY --from=builder /app/build /usr/share/nginx/html
# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf
# Copy your custom Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf
# Expose port 9000
EXPOSE 9000
# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
