# PDF to JPEG Conversion Script

This script converts PDF documents into JPEG images using `PyMuPDF` for PDF manipulation and `Pillow` for image processing. The images are saved in a structured manner in the specified output folder.

## Dependencies

- `os`
- `logging`
- `fitz` (PyMuPDF)
- `PIL` (Pillow)
- `numpy`
- `concurrent.futures`

## Class: PDFConverter

### __init__(self, input_folder, output_folder)
Initializes the `PDFConverter` class with the paths to the input and output folders.

- **Parameters**:
  - `input_folder` (str): Path to the folder containing PDF files.
  - `output_folder` (str): Path to the folder where images will be saved.

### convert_pdf_to_images(self, pdf_file_path, order_index)
Converts a single PDF file into individual images, saving each page as a JPEG in the output folder.

- **Parameters**:
  - `pdf_file_path` (str): Path to the PDF file to be converted.
  - `order_index` (int): Index used to prefix the image filenames for order tracking.

- **Returns**:
  - `saved_image_paths` (list): List of paths to the saved images.

### process_all_pdfs(self)
Processes all PDF files in the input folder, converting each to individual images.

- **Usage**:
  This method uses `ProcessPoolExecutor` for parallel processing of multiple PDF files.

## Function: count_files(directory)
Counts the total number of files in a directory.

- **Parameters**:
  - `directory` (str): Path to the directory to be scanned.

- **Returns**:
  - `total_files` (int): Total number of files in the directory.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the input and output folder paths.
2. Creates the output folder if it does not exist.
3. Instantiates the `PDFConverter` class with the specified folders.
4. Processes all PDFs in the input folder to convert them to images.
5. Counts and logs the total number of files in the output folder.

## Usage

Run the script using Python:

```bash
python pdf_to_jpg.py
```


## Logging

The script uses the `logging` module to log information about the conversion process, such as the number of files processed and any errors encountered.

## Error Handling

Errors encountered during the PDF to image conversion process are logged using the `logging` module, allowing for easier debugging and tracking of issues.
