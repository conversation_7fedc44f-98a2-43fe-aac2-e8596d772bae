/* frontend/src/sphere/styles/ZoomableImage.css */

.zoomable-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: default;
  border-radius: 8px;
  /* Isolate the zoom context to prevent affecting other elements */
  isolation: isolate;
  /* Ensure hardware acceleration for smoother zooming */
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  touch-action: none; /* Prevent browser handling of touch events */
}

.zoomable-image-container:hover {
  cursor: default;
}

/* Cursor styles for different states */
.zoomable-image-container.ctrl-pressed:hover {
  cursor: zoom-in;
}

.zoomable-image-container.can-drag:hover {
  cursor: grab;
}

.zoomable-image-container.dragging {
  cursor: grabbing !important;
}

.zoomable-image-container.ctrl-pressed:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid var(--button-primary-bg, #4a90e2);
  border-radius: 8px;
  opacity: 0.5;
  pointer-events: none;
  transition: opacity 0.2s ease;
  animation: pulse 1.5s infinite;
  z-index: 1;
}

/* Add a subtle highlight when draggable */
.zoomable-image-container.can-drag:not(.ctrl-pressed):hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid var(--button-primary-bg, #4a90e2);
  border-radius: 8px;
  opacity: 0.3;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1;
}

.zoomable-image-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  will-change: transform; /* Optimize for performance */
  /* Ensure hardware acceleration */
  transform: translateZ(0);
  backface-visibility: hidden;
  z-index: 0;
  user-select: none; /* Prevent text selection during drag */
}

/* Zoom indicator styling */
.zoom-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  pointer-events: none; /* Don't interfere with mouse events */
  z-index: 100; /* Ensure it's above other elements */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-10px);
}

.zoom-indicator.visible {
  opacity: 0.7;
  transform: translateY(0);
}

.zoom-indicator.visible.active {
  opacity: 1;
  animation: fadeInScale 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.zoom-indicator svg {
  font-size: 1rem;
  color: var(--button-primary-bg, #4a90e2);
}

.zoom-text {
  display: none;
}

.zoom-indicator.visible .zoom-text {
  display: inline;
  white-space: nowrap;
}

.zoom-tip {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-left: 4px;
  font-style: italic;
  white-space: nowrap;
}

.zoom-level {
  font-weight: bold;
  margin-left: 6px;
  color: var(--button-primary-bg, #4a90e2);
  font-size: 0.9rem;
}

/* Add a subtle border when zoomed */
.zoomable-image-wrapper.zoomed {
  outline-offset: -2px;
  border-radius: 2px;
  /* Add a subtle box shadow for depth */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Ensure the loaded image maintains its container */
.zoomable-image-wrapper .loaded-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Reset zoom button */
.reset-zoom-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(10px);
  backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Show the reset button when hovering over the container */
.zoomable-image-container:hover .reset-zoom-button {
  opacity: 0.85;
  transform: translateY(0);
}

.reset-zoom-button:hover {
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 0 1px var(--button-primary-bg, #4a90e2);
  background-color: rgba(0, 0, 0, 0.85);
}

.reset-zoom-button:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.reset-zoom-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--button-primary-bg, #4a90e2);
}

.reset-zoom-button svg {
  font-size: 1.1rem;
  color: var(--button-primary-bg, #4a90e2);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Drag indicator styling */
.drag-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  pointer-events: none; /* Don't interfere with mouse events */
  z-index: 100; /* Ensure it's above other elements */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-10px);
}

.drag-indicator.visible {
  opacity: 0.7;
  transform: translateY(0);
}

.drag-indicator.visible.active {
  opacity: 1;
  animation: fadeInScale 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.drag-indicator svg {
  font-size: 1rem;
  color: var(--button-primary-bg, #4a90e2);
}

.drag-text {
  display: inline;
  white-space: nowrap;
}

/* Pulse animation for the border */
@keyframes pulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 0.3;
  }
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
