# Visualise Annotations Script

This script visualises annotations on an image by drawing bounding boxes and labels. The annotations are provided in JSON format, and the script uses PIL to render the visualisation.

## Dependencies

- `json`
- `PIL` (Pillow)

## Class: AnnotationVisualizer

### __init__(self, json_data)
Initialises the `AnnotationVisualizer` class with JSON data containing annotations.

- **Parameters**:
  - `json_data` (str or dict): JSON data containing the annotations. This can be a JSON string or a dictionary.

### draw_annotations(self, output_path="annotated_image.jpg")
Draws annotations on the image and saves the annotated image to the specified output path.

- **Parameters**:
  - `output_path` (str): Path to save the annotated image. Default is "annotated_image.jpg".

### visualize(self)
Draws annotations on the image and displays the annotated image.

## Label to Color Mapping

The script uses a predefined mapping of labels to colors for visualisation. The mapping is defined as follows:

```python
label_to_color = {
    "PO_Number": "blue",
    "Contact_Name": "green",
    "Contact_Phone": "orange",
    "Contact_Email": "purple",
    "Delivery_Address": "yellow",
    "Invoice_Address": "pink",
    "Product_Code": "cyan",
    "Product_Qty": "magenta",
    "Invoice_Email": "lime",
    "Product_Name": "brown",
    "Unit_Cost": "red",
    "Line_Value": "navy",
    "Total_Value": "gold",
    "Ignore_Text": "grey",
    "Order_Date": "olive",
    "Invoice_Phone": "teal",
}
```

## Methods

### __init__(self, json_data)
Initialises the visualiser with the provided JSON data. It parses the JSON data to extract image details and annotations.

- **Parameters**:
  - `json_data` (str or dict): The JSON data containing annotations. It can be a JSON string or a dictionary.

### draw_annotations(self, output_path="annotated_image.jpg")
Draws annotations on the image, including bounding boxes and labels, and saves the annotated image.

- **Parameters**:
  - `output_path` (str): The file path to save the annotated image. Default is "annotated_image.jpg".

### visualize(self)
Calls the `draw_annotations` method to draw and display the annotations on the image.

## Usage

Run the script using Python:

```python
visualizer = AnnotationVisualizer(json_data)
visualizer.visualize()
```
