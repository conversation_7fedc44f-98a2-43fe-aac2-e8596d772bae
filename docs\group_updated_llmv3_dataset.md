# Group Updated LLMv3 Dataset Script

This script combines JSON files from clustered image data into single JSON files for each cluster. It skips those that are already labelled. The combined files are saved in an output directory for further processing or labelling.

## Dependencies

- `os`
- `json`
- `joblib`
- `logging`

## Class: ClusterJSO<PERSON>ombiner

### __init__(self, cluster_file, input_folder_path, labelled_folder_path, output_folder_path)
Initialises the `ClusterJSONCombiner` class with paths to the cluster file, input folder, labelled folder, and output folder.

- **Parameters**:
  - `cluster_file` (str): Path to the file containing cluster labels.
  - `input_folder_path` (str): Path to the folder containing input JSON files.
  - `labelled_folder_path` (str): Path to the folder containing labelled JSON files.
  - `output_folder_path` (str): Path to the folder where combined JSON files will be saved.

### load_cluster_labels(self)
Loads cluster labels from the specified file.

- **Returns**:
  - `cluster_labels` (dict): Dictionary containing cluster labels.

### get_cluster_n_pdfs(self, cluster_n)
Retrieves PDF paths that belong to a specific cluster.

- **Parameters**:
  - `cluster_n` (int): The cluster number to retrieve PDFs for.

- **Returns**:
  - `pdf_paths` (list): List of PDF paths belonging to the specified cluster.

### replace_paths(self, file_paths)
Replaces file paths to point to JSON files in the input folder.

- **Parameters**:
  - `file_paths` (list): List of file paths to be replaced.

- **Returns**:
  - `replaced_paths` (list): List of replaced file paths.

### load_json(self, json_file)
Loads a JSON file.

- **Parameters**:
  - `json_file` (str): Path to the JSON file.

- **Returns**:
  - `data` (dict): JSON data.

### combine_json_files(self, file_paths)
Combines JSON data from multiple files.

- **Parameters**:
  - `file_paths` (list): List of JSON file paths to be combined.

- **Returns**:
  - `combined_data` (list): Combined JSON data from all files.

### save_json(self, data, output_file_path)
Saves combined JSON data to a specified file.

- **Parameters**:
  - `data` (list): Combined JSON data to be saved.
  - `output_file_path` (str): Path to the output JSON file.

### combine_cluster_files(self, cluster_n)
Combines JSON files for a specific cluster and saves the result.

- **Parameters**:
  - `cluster_n` (int): The cluster number for which to combine JSON files.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the paths for the cluster file, input folder, labelled folder, and output folder.
2. Initialises the `ClusterJSONCombiner` class with the specified paths.
3. Combines JSON files for each cluster (in this case, for clusters 0 to 19).

## Usage

Run the script using Python:

```bash
python group_updated_llmv3_dataset.py
```

## Logging

The script uses the `logging` module to log information about the JSON combining process, including progress and any errors encountered.

## Error Handling

Errors encountered during the JSON combining process are logged using the `logging` module, allowing for easier debugging and tracking of issues.
