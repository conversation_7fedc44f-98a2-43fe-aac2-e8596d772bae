# backend/app/services/sql_server/sql_server_queries.py

from sqlalchemy import text
from .sql_server_conn import get_engine


def find_customers_by_contact_info(phone_numbers=[], emails=[]):
    """
    Query the SQL Server to find customers matching the given phone numbers or emails.
    Returns a list of customer records.
    """
    engine = get_engine()

    try:
        with engine.connect() as connection:
            # Base query
            query = """
                SELECT
                    AddressId as address_id,
                    [3EX Code] AS AddressCode,
                    [Contact],
                    [Company],
                    [Address 1],
                    [Address 2],
                    [Address 3],
                    [City],
                    [County],
                    [PostCode],
                    [Country],
                    [Email],
                    [HomeTel],
                    [WorkTel],
                    [MobileTel]
                FROM [dbo].[COSY_SPHERE_Customer_Search]
                WHERE 1=1
            """
            params = {}

            # Conditions storage
            combined_conditions = []

            # Add phone number conditions
            if phone_numbers:
                phone_conditions = []
                for idx, phone in enumerate(phone_numbers):
                    key = f"phone{idx}"
                    phone_conditions.append(
                        f"([HomeTel] = :{key} OR [WorkTel] = :{key} OR [MobileTel] = :{key})"
                    )
                    params[key] = phone
                phone_clause = " OR ".join(phone_conditions)
                combined_conditions.append(f"({phone_clause})")

            # Add email conditions
            if emails:
                email_conditions = []
                for idx, email in enumerate(emails):
                    key = f"email{idx}"
                    email_conditions.append(f"[Email] = :{key}")
                    params[key] = email
                email_clause = " OR ".join(email_conditions)
                combined_conditions.append(f"({email_clause})")

            # Combine conditions with OR
            if combined_conditions:
                query += f" AND ({' OR '.join(combined_conditions)})"

            # Prepare the query for execution using text() function
            query = text(query)

            # Execute the query
            result = connection.execute(query, params)

            # Fetch the results and explicitly map columns to row values
            customers = [dict(zip(result.keys(), row)) for row in result.fetchall()]
            return customers

    finally:
        # Ensure the engine is disposed of after use
        engine.dispose()


def find_product_details(product_code: str, address_id: int):
    """
    Query the SQL Server to find the product matching the given product code.
    Returns a product record or None if not found.

    Args:
        product_code (str): The product code to look up
        address_id (int): The address id to look up
    """
    engine = get_engine()

    try:
        with engine.connect() as connection:
            # Base query with a parameter placeholder
            query = """
                -- Input Parameters
                DECLARE @InputProductCode NVARCHAR(100) = :product_code;
                DECLARE @InputAddressId INT = :address_id;
                DECLARE @InputQuantity DECIMAL(18, 2) = 1;

                -- CTE to get ProductId and StandardPrice
                WITH ProductInfo AS (
                    SELECT 
                        P.Id AS ProductId,
                        P.Code AS ProductCode,
                        REPLACE(REPLACE(P.Description, ',', ' '), '"', '') AS ProductName, 
                        CASE WHEN P.DiscontinueStatus = 0 THEN 'Live' WHEN P.DiscontinueStatus = 1 THEN 'Discontinued' WHEN P.DiscontinueStatus = 2 THEN 'Run Down Stock' END AS Status,
                        PP.VatExclusivePrice AS StandardPrice
                    FROM Product P
                    LEFT JOIN ProductPriceMatrix PP 
                        ON PP.ProductId = P.Id
                        AND PP.CategoryIdForHorizontalMatrix = 527
                    WHERE P.Code = @InputProductCode
                    AND PP.Id IS NOT NULL
                ),
                -- CTE for Customer Context (Default Matrix, Discount Category Id, and Address to use)
                CustomerContext AS (
                    SELECT
                        COALESCE(Parent.Id, A.Id) AS AddressIdToUse, -- Use Parent if exists, else original
                        COALESCE(ParentAA.CategoryIdForDefaultHorizontalMatrix, AA.CategoryIdForDefaultHorizontalMatrix) AS DefaultHorizontalMatrixId,
                        COALESCE(ParentDiscountLink.CategoryId, DiscountLink.CategoryId) AS DiscountCategoryId
                    FROM Address A
                    LEFT JOIN AccountAgreement ON AccountAgreement.AddressId = A.Id
                    LEFT JOIN Address AS Parent ON Parent.Id = AccountAgreement.AddressIdForParentAddress
                    LEFT JOIN AccountAgreement AA ON AA.AddressId = A.Id -- For original address's matrix
                    LEFT JOIN AccountAgreement ParentAA ON ParentAA.AddressId = Parent.Id -- For parent's matrix
                    LEFT JOIN AddressCategory AS DiscountLink ON DiscountLink.AddressId = A.Id AND DiscountLink.CategoryUsageId = 84
                    LEFT JOIN AddressCategory AS ParentDiscountLink ON ParentDiscountLink.AddressId = Parent.Id AND ParentDiscountLink.CategoryUsageId = 84
                    WHERE A.Id = @InputAddressId
                ),
                -- CTE for PriceMatrix
                PriceAfterHorizontalMatrix AS (
                    SELECT
                        PI.ProductId,
                        COALESCE(PPM_Customer.VatExclusivePrice, PI.StandardPrice) AS PriceBeforeVariance
                    FROM ProductInfo PI
                    INNER JOIN CustomerContext CC ON 1=1
                    LEFT JOIN ProductPriceMatrix PPM_Customer
                        ON PPM_Customer.ProductId = PI.ProductId
                        AND PPM_Customer.CategoryIdForHorizontalMatrix = CC.DefaultHorizontalMatrixId
                        AND CC.DefaultHorizontalMatrixId IS NOT NULL
                        AND CC.DefaultHorizontalMatrixId != 527 -- Exclude standard matrix (527)
                ),
                -- CTE for PriceVariance
                AllPotentialVarianceRules AS (
                    SELECT
                        VL.Id AS VarianceLineId,
                        VLPA.AddressId AS TriggerAddressId,
                        VLPGT.ProductGroupId AS TriggerProductGroupId,
                        VLPGT.ProductGroupUsageId AS TriggerProductGroupUsageId,
                        VLPACT.CategoryId AS TriggerCategoryId,
                        P_Trigger.Id AS TriggerProductId,
                        -- CategoryIdForCurrency,
                        VL.PricingMethod,
                        VLLevelBreak.Id AS LevelBreakId,
                        VLLevelBreak.QuantityLevelBreak,
                        VLLevelBreak.FixedPrice,
                        VLLevelBreak.Percentage
                        -- PricingMethod -- 0=Fixed Price, 1=Discount Percentage, 2=Discount Value, 3=Surchard Percentage, 4= Surcharge Value
                    FROM ProductPriceVarianceLine VL
                    -- Discount and price figures
                    LEFT JOIN ProductPriceVarianceLineLevelBreak VLLevelBreak
                        ON VLLevelBreak.ProductPriceVarianceLineId = VL.Id
                    -- Get primary address for price variance (if using address)
                    LEFT JOIN ProductPriceVarianceLinePrimaryAddressTrigger VLPA
                        ON VLPA.ProductPriceVarianceLineId = VL.Id
                    -- Get product group and usage (if using product group and usage)
                    LEFT JOIN ProductPriceVarianceLineProductGroupTrigger VLPGT
                        ON VLPGT.ProductPriceVarianceLineId = VL.Id
                    -- Get product id (if exact product)
                    LEFT JOIN ProductPriceVarianceLineProductTrigger VLPT
                        ON VLPT.ProductPriceVarianceLineId = VL.Id
                    LEFT JOIN Product P_Trigger
                        ON P_Trigger.Id = VLPT.ProductId
                    -- Get category id (if using category)
                    LEFT JOIN ProductPriceVarianceLinePrimaryAddressCategoryTrigger VLPACT
                        ON VLPACT.ProductPriceVarianceLineId = VL.Id
                    -- Ensure active
                    WHERE VL.FinishDate >= GETDATE() AND VL.StartDate <= GETDATE()
                ),
                -- Matched and ranked variance rules
                MatchedAndRankedVarianceRules AS (
                    SELECT
                        APVR.VarianceLineId,
                        APVR.PricingMethod,
                        APVR.LevelBreakId,
                        APVR.QuantityLevelBreak,
                        APVR.FixedPrice AS LevelBreakFixedPrice,
                        APVR.Percentage AS LevelBreakPercentage,
                        PAHM.PriceBeforeVariance,
                        PI.ProductId AS InputProductId,
                        CC.AddressIdToUse,
                        CC.DiscountCategoryId AS InputCustomerDiscountCategoryId,

                        -- Determine the RuleTypePriority based on the new explicit order
                        CASE
                            -- Priority 1: "True" Product-Only (Option 3)
                            WHEN APVR.TriggerProductId = PI.ProductId
                                AND APVR.TriggerAddressId IS NULL
                                AND APVR.TriggerCategoryId IS NULL
                            THEN 1

                            -- Priority 2.1: Address + Product (Option 1a)
                            WHEN APVR.TriggerAddressId = CC.AddressIdToUse
                                AND APVR.TriggerProductId = PI.ProductId
                            THEN 2

                            -- Priority 2.2: Address + Generic Product (Option 1b)
                            WHEN APVR.TriggerAddressId = CC.AddressIdToUse
                                AND APVR.TriggerProductId IS NULL
                            THEN 3

                            -- Priority 3.1: Category + Product Group (must match) + Product (Option 2a)
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId
                                AND APVR.TriggerProductGroupId IS NOT NULL -- Rule specifies a product group
                                AND EXISTS ( -- And the input product is in that group
                                    SELECT 1 FROM ProductGroupLink PGL
                                    WHERE PGL.ProductId = PI.ProductId
                                    AND PGL.ProductGroupId = APVR.TriggerProductGroupId
                                    AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)
                                )
                                AND APVR.TriggerProductId = PI.ProductId
                            THEN 4

                            -- Priority 3.2: Category + Product Group (must match) + Generic Product (Option 2a)
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId
                                AND APVR.TriggerProductGroupId IS NOT NULL -- Rule specifies a product group
                                AND EXISTS ( -- And the input product is in that group
                                    SELECT 1 FROM ProductGroupLink PGL
                                    WHERE PGL.ProductId = PI.ProductId
                                    AND PGL.ProductGroupId = APVR.TriggerProductGroupId
                                    AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)
                                )
                                AND APVR.TriggerProductId IS NULL
                            THEN 5

                            -- Priority 3.3: Category + Product (No Product Group defined on the Rule)
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId
                                AND APVR.TriggerProductGroupId IS NULL -- Rule does NOT specify a product group
                                AND APVR.TriggerProductId = PI.ProductId
                            THEN 6

                            -- Priority 3.4: Category + Generic Product (No Product Group defined on the Rule)
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId
                                AND APVR.TriggerProductGroupId IS NULL -- Rule does NOT specify a product group
                                AND APVR.TriggerProductId IS NULL
                            THEN 7

                            ELSE 99 -- Does not fit any defined high-priority pattern
                        END AS RuleTypePriority,

                        ROW_NUMBER() OVER (
                            PARTITION BY PI.ProductId -- Should be one product, but good practice
                            ORDER BY
                                -- Primary Sort: The new RuleTypePriority
                                CASE
                                    WHEN APVR.TriggerProductId = PI.ProductId AND APVR.TriggerAddressId IS NULL AND APVR.TriggerCategoryId IS NULL AND APVR.TriggerProductGroupId IS NULL THEN 1
                                    WHEN APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId = PI.ProductId THEN 2
                                    WHEN APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId IS NULL THEN 3
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PI.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId = PI.ProductId THEN 4
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PI.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId IS NULL THEN 5
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId = PI.ProductId THEN 6
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId IS NULL THEN 7
                                    ELSE 99
                                END ASC,

                                -- Secondary Sort: Fixed price overrules other methods within the same RuleTypePriority
                                CASE WHEN APVR.PricingMethod = 0 THEN 0 ELSE 1 END ASC,

                                -- Tertiary Sort: Higher applicable quantity break wins
                                ISNULL(APVR.QuantityLevelBreak, -1) DESC,

                                -- Tie-breakers
                                APVR.VarianceLineId ASC,
                                ISNULL(APVR.LevelBreakId, -1) ASC
                        ) AS OverallRank

                    FROM AllPotentialVarianceRules APVR
                    INNER JOIN ProductInfo PI ON 1=1
                    INNER JOIN CustomerContext CC ON 1=1
                    INNER JOIN PriceAfterHorizontalMatrix PAHM ON PAHM.ProductId = PI.ProductId
                    WHERE
                        -- The rule must qualify for one of the defined patterns (RuleTypePriority 1 through 7)
                        (
                            (APVR.TriggerProductId = PI.ProductId AND APVR.TriggerAddressId IS NULL AND APVR.TriggerCategoryId IS NULL AND APVR.TriggerProductGroupId IS NULL) OR -- P1
                            (APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId = PI.ProductId) OR -- P2.1
                            (APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId IS NULL) OR -- P2.2
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PI.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId = PI.ProductId) OR -- P3.1
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PI.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId IS NULL) OR -- P3.2
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId = PI.ProductId) OR -- P3.3
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId IS NULL) -- P3.4
                        )
                        -- Quantity break logic (remains the same)
                        AND (APVR.LevelBreakId IS NOT NULL OR NOT EXISTS (SELECT 1 FROM ProductPriceVarianceLineLevelBreak sub WHERE sub.ProductPriceVarianceLineId = APVR.VarianceLineId))
                        AND (@InputQuantity >= APVR.QuantityLevelBreak OR APVR.QuantityLevelBreak IS NULL)
                )
                -- Final Selection and Price Calculation
                SELECT
                    PI.ProductId,
                    PI.ProductCode,
                    PI.ProductName,
                    (SELECT StandardPrice FROM ProductInfo) AS BaseStandardPrice,
                    PAHM.PriceBeforeVariance,
                    MVR.VarianceLineId AS AppliedVarianceLineId,
                    MVR.PricingMethod AS AppliedPricingMethod,
                    MVR.LevelBreakFixedPrice AS AppliedRuleFixedPriceOrValue,
                    MVR.LevelBreakPercentage AS AppliedRulePercentage,
                    MVR.QuantityLevelBreak AS AppliedQuantityLevelBreak,
                    MVR.RuleTypePriority AS AppliedRuleTypePriority, -- Using the new priority field
                    MVR.OverallRank AS AppliedOverallRank,
                    @InputQuantity AS RequestedQuantity,
                    CAST(
                        CASE
                            WHEN MVR.VarianceLineId IS NULL THEN PAHM.PriceBeforeVariance
                            WHEN MVR.PricingMethod = 0 THEN MVR.LevelBreakFixedPrice
                            WHEN MVR.PricingMethod = 1 THEN PAHM.PriceBeforeVariance * (1 - (MVR.LevelBreakPercentage / 100.0))
                            WHEN MVR.PricingMethod = 2 THEN PAHM.PriceBeforeVariance - MVR.LevelBreakFixedPrice
                            WHEN MVR.PricingMethod = 3 THEN PAHM.PriceBeforeVariance * (1 + (MVR.LevelBreakPercentage / 100.0))
                            WHEN MVR.PricingMethod = 4 THEN PAHM.PriceBeforeVariance + MVR.LevelBreakFixedPrice
                            ELSE PAHM.PriceBeforeVariance
                        END
                    AS DECIMAL(18,4)) AS Price
                FROM ProductInfo PI
                INNER JOIN PriceAfterHorizontalMatrix PAHM ON PI.ProductId = PAHM.ProductId
                LEFT JOIN MatchedAndRankedVarianceRules MVR
                    ON PI.ProductId = MVR.InputProductId AND MVR.OverallRank = 1;
            """

            # Prepare the query for execution using text() function
            query = text(query)

            # Execute the query with parameters
            result = connection.execute(
                query,
                {
                    "product_code": product_code,
                    "address_id": address_id,
                },
            )

            # Fetch the result
            row = result.fetchone()

            if row:
                product = dict(zip(result.keys(), row))
                return product
            else:
                return None

    finally:
        # Ensure the engine is disposed of after use
        engine.dispose()


def find_products_by_name(
    product_name: str,
    address_id: int,
    product_cost: float = None,
    quantity: int = 1,
):
    """
    Searches for products where the description contains any word from the product_name.
    For each found product, calculates the customer-specific price using the provided address_id and quantity.
    If product_cost is provided, filters results to products whose calculated FinalPrice
    is within a 20% range of the product_cost.
    Returns a list of unique product records with their calculated prices.

    Args:
        product_name (str): The product name string to search for.
        address_id (int): The address_id for customer-specific pricing.
        product_cost (float, optional): The product cost to filter results by.
        quantity (int, optional): The quantity for pricing, defaults to 1.
    """
    engine = get_engine()

    try:
        with engine.connect() as connection:
            # Step 1: Convert product_name to lowercase and split into words
            words = (
                product_name.lower().split()
            )  # Example: "My product name" -> ["my", "product", "name"]

            # Filter out any strings ending with "Pk" or "Pk."
            words = [
                word
                for word in words
                if not word.lower().endswith(("pk", "pk.", "pk)", "pk)."))
            ]

            # Build the LIKE conditions for the product description search
            # Create a series of "P.Description LIKE '%word1%' OR P.Description LIKE '%word2%' OR ..."
            description_filters = " OR ".join(
                [
                    f"REPLACE(REPLACE(P.Description, ',', ' '), '\"', '') LIKE :word{i}"
                    for i in range(len(words))
                ]
            )

            params = {
                "address_id": address_id,
                "quantity": quantity,
            }
            for i, word in enumerate(words):
                params[f"word{i}"] = f"%{word}%"

            # The full pricing logic, now starting with a product search
            # and applying pricing to all found products.
            sql_query = f"""
                -- Input Parameters
                DECLARE @InputAddressId INT = :address_id;
                DECLARE @InputQuantity DECIMAL(18, 2) = :quantity;

                -- CTE 1: Find products matching the name search criteria
                WITH SearchedProducts AS (
                    SELECT
                        P.Id AS ProductId,
                        P.Code AS ProductCode,
                        REPLACE(REPLACE(P.Description, ',', ' '), '"', '') AS ProductName,
                        CASE
                            WHEN P.DiscontinueStatus = 0 THEN 'Live'
                            WHEN P.DiscontinueStatus = 1 THEN 'Discontinued'
                            WHEN P.DiscontinueStatus = 2 THEN 'Run Down Stock'
                        END AS Status,
                        PP_Standard.VatExclusivePrice AS StandardPrice -- Base standard price
                    FROM Product P
                    -- Join for standard price (matrix 527)
                    LEFT JOIN ProductPriceMatrix PP_Standard
                        ON PP_Standard.ProductId = P.Id AND PP_Standard.CategoryIdForHorizontalMatrix = 527
                    WHERE {description_filters} -- Dynamic LIKE conditions
                    AND PP_Standard.Id IS NOT NULL
                ),
                -- CTE 2: Customer Context (same as before, but will apply to all searched products)
                CustomerContext AS (
                    SELECT
                        COALESCE(Parent.Id, A.Id) AS AddressIdToUse,
                        COALESCE(ParentAA.CategoryIdForDefaultHorizontalMatrix, AA.CategoryIdForDefaultHorizontalMatrix) AS DefaultHorizontalMatrixId,
                        COALESCE(ParentDiscountLink.CategoryId, DiscountLink.CategoryId) AS DiscountCategoryId
                    FROM Address A
                    LEFT JOIN AccountAgreement ON AccountAgreement.AddressId = A.Id
                    LEFT JOIN Address AS Parent ON Parent.Id = AccountAgreement.AddressIdForParentAddress
                    LEFT JOIN AccountAgreement AA ON AA.AddressId = A.Id
                    LEFT JOIN AccountAgreement ParentAA ON ParentAA.AddressId = Parent.Id
                    LEFT JOIN AddressCategory AS DiscountLink ON DiscountLink.AddressId = A.Id AND DiscountLink.CategoryUsageId = 84
                    LEFT JOIN AddressCategory AS ParentDiscountLink ON ParentDiscountLink.AddressId = Parent.Id AND ParentDiscountLink.CategoryUsageId = 84
                    WHERE A.Id = @InputAddressId
                ),
                -- CTE 3: Price After Customer's Horizontal Matrix (for each searched product)
                PriceAfterHorizontalMatrix AS (
                    SELECT
                        SP.ProductId,
                        SP.ProductCode,
                        SP.ProductName,
                        SP.Status,
                        SP.StandardPrice AS BaseStandardPrice,
                        COALESCE(PPM_Customer.VatExclusivePrice, SP.StandardPrice) AS PriceBeforeVariance
                    FROM SearchedProducts SP
                    CROSS JOIN CustomerContext CC -- Apply customer context to all searched products
                    LEFT JOIN ProductPriceMatrix PPM_Customer
                        ON PPM_Customer.ProductId = SP.ProductId
                        AND PPM_Customer.CategoryIdForHorizontalMatrix = CC.DefaultHorizontalMatrixId
                        AND CC.DefaultHorizontalMatrixId IS NOT NULL
                        AND CC.DefaultHorizontalMatrixId != 527
                ),
                -- CTE 4: All Potential Variance Rules (same as before)
                AllPotentialVarianceRules AS (
                    SELECT
                        VL.Id AS VarianceLineId, VLPA.AddressId AS TriggerAddressId,
                        VLPGT.ProductGroupId AS TriggerProductGroupId, VLPGT.ProductGroupUsageId AS TriggerProductGroupUsageId,
                        VLPACT.CategoryId AS TriggerCategoryId, P_Trigger.Id AS TriggerProductId,
                        VL.PricingMethod, VLLevelBreak.Id AS LevelBreakId,
                        VLLevelBreak.QuantityLevelBreak, VLLevelBreak.FixedPrice, VLLevelBreak.Percentage
                    FROM ProductPriceVarianceLine VL
                    LEFT JOIN ProductPriceVarianceLineLevelBreak VLLevelBreak ON VLLevelBreak.ProductPriceVarianceLineId = VL.Id
                    LEFT JOIN ProductPriceVarianceLinePrimaryAddressTrigger VLPA ON VLPA.ProductPriceVarianceLineId = VL.Id
                    LEFT JOIN ProductPriceVarianceLineProductGroupTrigger VLPGT ON VLPGT.ProductPriceVarianceLineId = VL.Id
                    LEFT JOIN ProductPriceVarianceLineProductTrigger VLPT ON VLPT.ProductPriceVarianceLineId = VL.Id
                    LEFT JOIN Product P_Trigger ON P_Trigger.Id = VLPT.ProductId
                    LEFT JOIN ProductPriceVarianceLinePrimaryAddressCategoryTrigger VLPACT ON VLPACT.ProductPriceVarianceLineId = VL.Id
                    WHERE VL.FinishDate >= GETDATE() AND VL.StartDate <= GETDATE()
                ),
                -- CTE 5: Matched and Ranked Variance Rules (applied per product)
                MatchedAndRankedVarianceRules AS (
                    SELECT
                        PAHM.ProductId, -- Keep ProductId to join back
                        APVR.VarianceLineId, APVR.PricingMethod, APVR.LevelBreakId, APVR.QuantityLevelBreak,
                        APVR.FixedPrice AS LevelBreakFixedPrice, APVR.Percentage AS LevelBreakPercentage,
                        PAHM.PriceBeforeVariance, CC.AddressIdToUse, CC.DiscountCategoryId AS InputCustomerDiscountCategoryId,
                        CASE
                            WHEN APVR.TriggerProductId = PAHM.ProductId AND APVR.TriggerAddressId IS NULL AND APVR.TriggerCategoryId IS NULL AND APVR.TriggerProductGroupId IS NULL THEN 1
                            WHEN APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId = PAHM.ProductId THEN 2
                            WHEN APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId IS NULL THEN 3
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PAHM.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId = PAHM.ProductId THEN 4
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PAHM.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId IS NULL THEN 5
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId = PAHM.ProductId THEN 6
                            WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId IS NULL THEN 7
                            ELSE 99
                        END AS RuleTypePriority,
                        ROW_NUMBER() OVER (
                            PARTITION BY PAHM.ProductId -- Rank rules FOR EACH product found
                            ORDER BY
                                CASE
                                    WHEN APVR.TriggerProductId = PAHM.ProductId AND APVR.TriggerAddressId IS NULL AND APVR.TriggerCategoryId IS NULL AND APVR.TriggerProductGroupId IS NULL THEN 1
                                    WHEN APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId = PAHM.ProductId THEN 2
                                    WHEN APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId IS NULL THEN 3
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PAHM.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId = PAHM.ProductId THEN 4
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PAHM.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId IS NULL THEN 5
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId = PAHM.ProductId THEN 6
                                    WHEN APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId IS NULL THEN 7
                                    ELSE 99
                                END ASC,
                                CASE WHEN APVR.PricingMethod = 0 THEN 0 ELSE 1 END ASC,
                                ISNULL(APVR.QuantityLevelBreak, -1) DESC,
                                APVR.VarianceLineId ASC, ISNULL(APVR.LevelBreakId, -1) ASC
                        ) AS OverallRank
                    FROM PriceAfterHorizontalMatrix PAHM
                    CROSS JOIN CustomerContext CC -- Ensure CC values are available for every product row
                    LEFT JOIN AllPotentialVarianceRules APVR -- Check all rules against each product
                        ON -- The WHERE clause below will filter applicable rules
                        (
                            (APVR.TriggerProductId = PAHM.ProductId AND APVR.TriggerAddressId IS NULL AND APVR.TriggerCategoryId IS NULL AND APVR.TriggerProductGroupId IS NULL) OR
                            (APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId = PAHM.ProductId) OR
                            (APVR.TriggerAddressId = CC.AddressIdToUse AND APVR.TriggerProductId IS NULL) OR
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PAHM.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId = PAHM.ProductId) OR
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NOT NULL AND EXISTS (SELECT 1 FROM ProductGroupLink PGL WHERE PGL.ProductId = PAHM.ProductId AND PGL.ProductGroupId = APVR.TriggerProductGroupId AND (PGL.ProductGroupUsageId = APVR.TriggerProductGroupUsageId OR APVR.TriggerProductGroupUsageId IS NULL)) AND APVR.TriggerProductId IS NULL) OR
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId = PAHM.ProductId) OR
                            (APVR.TriggerCategoryId = CC.DiscountCategoryId AND APVR.TriggerProductGroupId IS NULL AND APVR.TriggerProductId IS NULL)
                        )
                        AND (APVR.LevelBreakId IS NOT NULL OR NOT EXISTS (SELECT 1 FROM ProductPriceVarianceLineLevelBreak sub WHERE sub.ProductPriceVarianceLineId = APVR.VarianceLineId))
                        AND (@InputQuantity >= APVR.QuantityLevelBreak OR APVR.QuantityLevelBreak IS NULL)
                )
                -- Final Selection and Price Calculation for all searched products
                SELECT
                    PAHM.ProductId,
                    PAHM.ProductCode,
                    PAHM.ProductName,
                    PAHM.Status,
                    PAHM.BaseStandardPrice,
                    PAHM.PriceBeforeVariance,
                    MVR.VarianceLineId AS AppliedVarianceLineId,
                    MVR.PricingMethod AS AppliedPricingMethod,
                    MVR.LevelBreakFixedPrice AS AppliedRuleFixedPriceOrValue,
                    MVR.LevelBreakPercentage AS AppliedRulePercentage,
                    MVR.QuantityLevelBreak AS AppliedQuantityLevelBreak,
                    MVR.RuleTypePriority AS AppliedRuleTypePriority,
                    @InputQuantity AS RequestedQuantity,
                    CAST(
                        CASE
                            WHEN MVR.VarianceLineId IS NULL THEN PAHM.PriceBeforeVariance
                            WHEN MVR.PricingMethod = 0 THEN MVR.LevelBreakFixedPrice
                            WHEN MVR.PricingMethod = 1 THEN PAHM.PriceBeforeVariance * (1 - (MVR.LevelBreakPercentage / 100.0))
                            WHEN MVR.PricingMethod = 2 THEN PAHM.PriceBeforeVariance - MVR.LevelBreakFixedPrice
                            WHEN MVR.PricingMethod = 3 THEN PAHM.PriceBeforeVariance * (1 + (MVR.LevelBreakPercentage / 100.0))
                            WHEN MVR.PricingMethod = 4 THEN PAHM.PriceBeforeVariance + MVR.LevelBreakFixedPrice
                            ELSE PAHM.PriceBeforeVariance
                        END
                    AS DECIMAL(18,4)) AS Price
                FROM PriceAfterHorizontalMatrix PAHM
                LEFT JOIN MatchedAndRankedVarianceRules MVR
                    ON PAHM.ProductId = MVR.ProductId AND MVR.OverallRank = 1
            """

            prepared_query = text(sql_query)
            result = connection.execute(prepared_query, params)
            rows = result.fetchall()

            unique_products_dict = {}
            for row in rows:
                product_details = dict(zip(result.keys(), row))
                # Apply product_cost_filter
                if product_cost is not None:
                    try:
                        cost_filter = float(product_cost)
                        lower_bound = cost_filter * 0.8
                        upper_bound = cost_filter * 1.2
                        if not (lower_bound <= product_details["Price"] <= upper_bound):
                            continue
                    except (ValueError, TypeError):
                        # Invalid product_cost or Price
                        pass
                # Use ProductCode for uniqueness is multiple search words match the same
                unique_products_dict[product_details["ProductCode"]] = product_details

            return list(unique_products_dict.values())

    finally:
        # Ensure the engine is disposed of after use
        engine.dispose()
