# app/pdfs/routes.py

import logging
from flask import request, Blueprint, jsonify, send_from_directory, current_app, url_for
from ..auth.jwt_auth import requires_auth, get_current_user
from ..extensions import db
from ..models_db.pdf import PDF, StatusEnum
from ..models_db.image import Image
from ..models_db.order import Order, OrderLine
from .utils.review_processing import (
    extract_candidates,
    extract_addresses,
    extract_products,
)
from marshmallow import ValidationError
from ..schemas import OrderSchema
import uuid
import os
from sqlalchemy.orm import joinedload
import json
from ..services.doc_processing.view_results import process_pdf
from datetime import datetime
import re

pdfs_bp = Blueprint("/pdfs", __name__)
logger = logging.getLogger(__name__)


@pdfs_bp.route("/", methods=["GET"])
@requires_auth
def get_pdfs_list():
    try:
        pdfs = PDF.query.all()
        pdf_list = [
            {
                "id": pdf.id,
                "origin_name": pdf.origin_name,
                "filename": pdf.filename,
                "storage_path": pdf.storage_path,
                "uploaded_at": (
                    pdf.uploaded_at.isoformat() if pdf.uploaded_at else "N/A"
                ),
                "status": pdf.status.value,
                "completed_at": (
                    pdf.completed_at.isoformat() if pdf.completed_at else "N/A"
                ),
            }
            for pdf in pdfs
        ]
        return jsonify(pdf_list), 200
    except Exception as e:
        logger.error(f"Error retrieving PDFs: {e}")
        return jsonify({"error": "Internal server error"}), 500


@pdfs_bp.route("/upload", methods=["POST"])
@requires_auth
def upload_file():
    current_user = get_current_user()
    user_id = current_user.id if current_user else None

    if "file" not in request.files:
        logger.error("No file part in the request")
        return jsonify({"error": "No file part"}), 400

    file = request.files["file"]

    if file.filename == "":
        logger.error("No selected file")
        return jsonify({"error": "No selected file"}), 400

    if file:
        original_filename = file.filename
        filename = f"{uuid.uuid4()}_{original_filename}"
        file_path = os.path.join(current_app.config["UPLOAD_FOLDER"], filename)
        file.save(file_path)
        logger.info(f"File saved to {file_path}")

        try:
            # Create a new PDF entry including the user_id
            new_pdf = PDF(
                origin_name=original_filename,
                filename=filename,
                storage_path=file_path,
                uploaded_by=user_id,
            )
            db.session.add(new_pdf)
            db.session.commit()

            output_file_paths = process_pdf(file_path, new_pdf.id, db.session)

            output_file_urls = [
                f"/output/{os.path.basename(path)}" for path in output_file_paths
            ]
            logger.info(f"File processed successfully: {output_file_urls}")
            return (
                jsonify(
                    {
                        "message": "File processed successfully",
                        "output_files": output_file_urls,
                    }
                ),
                200,
            )

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error processing file: {e}")
            return jsonify({"error": str(e)}), 500


@pdfs_bp.route("/approve/<int:pdf_id>", methods=["POST"])
@requires_auth
def approve_pdf(pdf_id):
    try:
        pdf = PDF.query.filter_by(id=pdf_id).first()

        if not pdf:
            return jsonify({"error": "PDF not found"}), 404

        current_user = get_current_user()
        user_id = current_user.id if current_user else None

        pdf.status = StatusEnum.approved
        pdf.approved_by = user_id
        pdf.completed_at = datetime.utcnow()
        pdf.comments = request.json.get("comments", "")

        db.session.commit()
        return jsonify({"message": "PDF approved successfully"}), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error approving PDF: {e}")
        return jsonify({"error": str(e)}), 500


@pdfs_bp.route("/skip/<int:pdf_id>", methods=["POST"])
@requires_auth
def skip_pdf(pdf_id):
    current_user = get_current_user()
    user_id = current_user.id if current_user else None

    try:
        data = request.json
        reason = data.get("reason")
        comments = data.get("comments", "")

        pdf = PDF.query.filter_by(id=pdf_id).first()
        if not pdf:
            return jsonify({"error": "PDF not found"}), 404

        pdf.status = StatusEnum.skipped
        pdf.completed_by = user_id
        pdf.completed_at = datetime.utcnow()
        pdf.skip_reason = reason
        pdf.skip_comments = comments
        db.session.commit()

        return jsonify({"message": "PDF skipped successfully"}), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error skipping PDF: {e}")
        return jsonify({"error": str(e)}), 500


@pdfs_bp.route("/pdf/<int:pdf_id>/review-data", methods=["GET"])
@requires_auth
def get_pdf_review_data(pdf_id: int):
    """
    Returns a structure optimised for the React reviewer UI.

    {
      "pdf":     { id, origin_name, uploaded_at },
      "images":  [ { id, url } … ],
      "candidates": { po_numbers, order_dates, … },
      "addresses":  { delivery: [line, …], invoice: [line, …] },
      "products":   [ { Product_Code, … } … ]
    }
    """
    try:
        pdf: PDF | None = (
            PDF.query.options(joinedload(PDF.annotations)).filter_by(id=pdf_id).first()
        )
        if pdf is None:
            return jsonify({"error": "PDF not found"}), 404

        # Load annotation JSON blobs
        images_annotations: list[dict] = []
        for annotation in pdf.annotations:
            with open(annotation.storage_path, "r") as json_file:
                annotation_data = json.load(json_file)
                images_annotations.append(annotation_data)

        # Build the tidy payload
        pdf_meta = {
            "id": pdf.id,
            "origin_name": pdf.origin_name,
            "uploaded_at": pdf.uploaded_at.isoformat(),
        }

        images = [
            {
                "id": img.id,
                "url": url_for("/pdfs.view_image", image_id=img.id, _external=True),
            }
            for img in pdf.images
        ]

        candidates = extract_candidates(images_annotations)
        addresses = extract_addresses(images_annotations)
        products = extract_products(images_annotations)

        return (
            jsonify(
                {
                    "pdf": pdf_meta,
                    "images": images,
                    "candidates": candidates,
                    "addresses": addresses,
                    "products": products,
                }
            ),
            200,
        )

    except Exception as exc:
        logger.exception("get_pdf_review_data failed")
        logger.error(f"Error in get_pdf_review_data: {exc}")
        return jsonify({"error": "Internal server error"}), 500


@pdfs_bp.route("/pdf/<int:pdf_id>", methods=["GET"])
@requires_auth
def get_pdf_details(pdf_id):
    try:
        logger.info(f"Fetching details for PDF ID {pdf_id}")
        pdf = (
            PDF.query.options(joinedload(PDF.annotations)).filter_by(id=pdf_id).first()
        )

        if not pdf:
            return jsonify({"error": "PDF not found"}), 404

        # Gather all annotations
        annotations = []
        for annotation in pdf.annotations:
            with open(annotation.storage_path, "r") as json_file:
                annotation_data = json.load(json_file)
                annotations.append(annotation_data)

        # Collect image details
        images = [
            {
                "id": image.id,
                "filename": image.filename,
                "storage_path": image.storage_path,
                "processed_at": image.processed_at.isoformat(),
            }
            for image in pdf.images
        ]

        response_data = {
            "id": pdf.id,
            "filename": pdf.filename,
            "origin_name": pdf.origin_name,
            "storage_path": pdf.storage_path,
            "uploaded_at": pdf.uploaded_at.isoformat(),
            "images": images,
            "annotations": annotations,
        }

        return jsonify(response_data), 200
    except Exception as e:
        logger.error(f"Error in get_pdf_details: {str(e)}")
        return jsonify({"error": str(e)}), 500


@pdfs_bp.route("/view_image/<int:image_id>", methods=["GET"])
@requires_auth
def view_image(image_id):
    try:
        image = Image.query.filter_by(id=image_id).first()
        if not image:
            return "Image not found", 404

        directory = os.path.dirname(image.storage_path)
        filename = os.path.basename(image.storage_path)
        return send_from_directory(directory, filename)
    except Exception as e:
        logger.error(f"Error in view_image: {e}")
        return jsonify({"error": "Internal server error"}), 500


@pdfs_bp.route("/pending_jobs", methods=["GET"])
@requires_auth
def get_pending_jobs():
    try:
        pending_pdfs = PDF.query.filter(
            PDF.status.in_([StatusEnum.unprocessed, StatusEnum.under_review])
        ).all()

        pending_jobs = [
            {
                "id": pdf.id,
                "origin_name": pdf.origin_name,
                "uploaded_by": pdf.uploaded_by,
                "uploaded_at": pdf.uploaded_at.isoformat(),
                "status": pdf.status.value,
                "comments": pdf.comments,
            }
            for pdf in pending_pdfs
        ]

        return jsonify(pending_jobs), 200
    except Exception as e:
        logger.error(f"Error retrieving pending jobs: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@pdfs_bp.route("/completed_pdfs", methods=["GET"])
@requires_auth
def get_completed_pdfs():
    try:
        completed_pdfs = PDF.query.filter(
            PDF.status.in_([StatusEnum.approved, StatusEnum.skipped])
        ).all()

        completed_jobs = [
            {
                "id": pdf.id,
                "origin_name": pdf.origin_name,
                "uploaded_by": pdf.uploaded_by,
                "uploaded_at": pdf.uploaded_at.isoformat(),
                "status": pdf.status.value,
                "comments": pdf.comments,
                "skip_reason": pdf.skip_reason,
                "skip_comments": pdf.skip_comments,
            }
            for pdf in completed_pdfs
        ]

        return jsonify(completed_jobs), 200
    except Exception as e:
        logger.error(f"Error retrieving completed jobs: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@pdfs_bp.route("/submit_order/<int:pdf_id>", methods=["POST"])
@requires_auth
def submit_order(pdf_id):
    try:
        # Get user id
        current_user = get_current_user()
        user_id = current_user.id if current_user else None

        # Parse JSON payload
        data = request.get_json()
        if not data:
            logger.error("No JSON payload provided")
            return jsonify({"error": "No JSON payload provided"}), 400

        # Validate and deserialise the input using Marshmallow
        order_schema = OrderSchema()
        try:
            validated_data = order_schema.load(data)  # Returns dict of clean data
        except ValidationError as ve:
            logger.error(f"Validation error: {ve}")
            return jsonify({"error": "Validation error", "details": ve.messages}), 400

        # Extract top-level fields from validated_data
        po_number = validated_data.get("po_number")
        # contact_name = validated_data.get("contact_name")
        contact_phone = validated_data.get("contact_phone")
        contact_email = validated_data.get("contact_email")
        # invoice_email = validated_data.get("invoice_email")
        # invoice_phone = validated_data.get("invoice_phone")
        # total_value = validated_data.get("total_value") or 0.0

        # Get addresses
        addresses = validated_data.get("addresses", {})
        delivery_address = addresses.get("delivery", {})
        # invoice_address = addresses.get("invoice", {})

        # --- Begin Database Operations ---
        # Create a new Order record.
        try:
            order = Order(
                pdf_id=pdf_id,
                po_number=po_number,
                # contact_name=contact_name,
                contact_phone=contact_phone,
                contact_email=contact_email,
                delivery_organisation_name=delivery_address.get(
                    "delivery_organisation_name"
                ),
                delivery_house=delivery_address.get("delivery_house"),
                delivery_street=delivery_address.get("delivery_street"),
                delivery_locality=delivery_address.get("delivery_locality"),
                delivery_town=delivery_address.get("delivery_town"),
                delivery_county=delivery_address.get("delivery_county"),
                delivery_postcode=delivery_address.get("delivery_postcode"),
                delivery_country=delivery_address.get("delivery_country"),
                # invoice_organisation_name=invoice_address.get(
                #     "invoice_organisation_name"
                # ),
                # invoice_house=invoice_address.get("invoice_house"),
                # invoice_street=invoice_address.get("invoice_street"),
                # invoice_locality=invoice_address.get("invoice_locality"),
                # invoice_town=invoice_address.get("invoice_town"),
                # invoice_county=invoice_address.get("invoice_county"),
                # invoice_postcode=invoice_address.get("invoice_postcode"),
                # invoice_country=invoice_address.get("invoice_country"),
                # invoice_email=invoice_email,
                # invoice_phone=invoice_phone,
                # total_value=total_value,
            )
            db.session.add(order)
            db.session.flush()  # Flush so that order.id is generated

            # Extract products data
            products_data = validated_data.get("products", [])

            # Create OrderLine records for each product
            for prod in products_data:
                order_line = OrderLine(
                    order_id=order.id,
                    product_code=prod.get("Product_Code"),
                    product_name=prod.get("Product_Name"),
                    product_qty=prod.get("Product_Qty"),
                    unit_cost=prod.get("Unit_Cost"),
                    # line_value=prod.get("Line_Value"),
                )
                db.session.add(order_line)

            # Commit the order and order lines.
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error in database operations: {e}")
            return jsonify({"error": "Internal server error"}), 500

        # Begin order import
        pass

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error processing purchase order for PDF ID {pdf_id}: {e}")
        return jsonify({"error": str(e)}), 500
