# Convert Labelled Data to Training Data Script

This script converts labelled JSON files into a format suitable for training machine learning models. The converted files are saved in a specified output directory.

## Dependencies

- `json`
- `os`

## Functions

### convert_bounding_box(x, y, width, height, original_width, original_height, new_width, new_height)
Converts the given bounding box coordinates to normalised values.

- **Parameters**:
  - `x` (float): The x-coordinate of the top-left corner of the bounding box in percentages.
  - `y` (float): The y-coordinate of the top-left corner of the bounding box in percentages.
  - `width` (float): The width of the bounding box in percentages.
  - `height` (float): The height of the bounding box in percentages.
  - `original_width` (int): The original width of the image.
  - `original_height` (int): The original height of the image.
  - `new_width` (int): The new width of the image after resizing.
  - `new_height` (int): The new height of the image after resizing.

- **Returns**:
  - `bbox` (list): A list of four coordinates `[x1, y1, x2, y2]` normalised to [0, 1].

### process_json_file(input_file_path, output_file_path)
Processes a single JSON file and converts its contents into the training format.

- **Parameters**:
  - `input_file_path` (str): Path to the input JSON file.
  - `output_file_path` (str): Path to the output JSON file.

### process_all_files(input_folder, output_folder)
Processes all JSON files in the input folder and saves the converted files in the output folder.

- **Parameters**:
  - `input_folder` (str): Path to the folder containing input JSON files.
  - `output_folder` (str): Path to the folder where output JSON files will be saved.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the paths for the input and output folders.
2. Calls the `process_all_files` function to process all JSON files in the input folder and save them in the output folder.

## Usage

Run the script using Python:

```bash
python labelled_to_training.py
```

## Label Mapping

The script uses a predefined label mapping to convert textual labels into numerical values. The mapping is defined as follows:

```python
label_mapping = {
    "PO_Number": 1,
    "Contact_Name": 2,
    "Contact_Phone": 3,
    "Contact_Email": 4,
    "Delivery_Address": 5,
    "Invoice_Address": 6,
    "Product_Code": 7,
    "Product_Qty": 8,
    "Invoice_Email": 9,
    "Product_Name": 0,
    "Unit_Cost": 10,
    "Line_Value": 11,
    "Total_Value": 12,
    "Ignore_Text": 13,
    "Order_Date": 14,
    "Invoice_Phone": 15,
}
```

## Error Handling

The script includes error handling for loading JSON files and ensures that directories are created if they do not already exist. Any unknown labels encountered during the processing are collected and printed.
