# backend/app/services/doc_processing/create_dataset.py

import logging
import os
from pathlib import Path
from uuid import uuid4
import numpy as np
from paddleocr import PaddleOCR

logger = logging.getLogger(__name__)


class OCRProcessor:
    def __init__(self, use_gpu=True):
        base = Path(os.getenv("PADDLEOCR_HOME"))
        det_dir = base / "det/en_PP-OCRv3_det_infer"
        rec_dir = base / "rec/en_PP-OCRv4_rec_infer"
        cls_dir = base / "cls/ch_ppocr_mobile_v2.0_cls_infer"

        try:
            logger.info(
                f"Setting up PaddleOCR with det dir {det_dir}, rec dir {rec_dir}, cls dir {cls_dir}"
            )
            self.ocr = PaddleOCR(
                det_model_dir=str(det_dir),
                rec_model_dir=str(rec_dir),
                cls_model_dir=str(cls_dir),
                use_angle_cls=False,
                lang="en",
                rec=False,
                use_gpu=use_gpu,
            )
            logger.info("PaddleOCR initialized with use_gpu=%s", use_gpu)
        except Exception as e:
            logger.error("Error initializing PaddleOCR: %s", e)
            raise

    @staticmethod
    def create_image_url(filename):
        try:
            url = f"http://localhost:8080/{filename}"
            return url
        except Exception as e:
            logger.error(f"Error creating image URL: {e}")
            raise

    def process_single_image(self, image=None):
        try:
            output_json = {}
            annotation_result = []

            img = image
            output_json["data"] = {"ocr": "image_object"}

            img_np = np.asarray(img)
            image_height, image_width = img_np.shape[:2]

            logger.info("Processing image: %s", "image_object")

            result = self.ocr.ocr(img_np, cls=False)

            for output in result:
                for item in output:
                    co_ord = item[0]
                    text = item[1][0]
                    score = item[1][1]

                    bbox = {
                        "x": 100 * co_ord[0][0] / image_width,
                        "y": 100 * co_ord[0][1] / image_height,
                        "width": 100 * (co_ord[2][0] - co_ord[0][0]) / image_width,
                        "height": 100 * (co_ord[2][1] - co_ord[0][1]) / image_height,
                        "rotation": 0,
                    }
                    region_id = str(uuid4())[:10]
                    bbox_result = {
                        "id": region_id,
                        "from_name": "bbox",
                        "to_name": "image",
                        "type": "rectangle",
                        "value": bbox,
                    }
                    transcription_result = {
                        "id": region_id,
                        "from_name": "transcription",
                        "to_name": "image",
                        "type": "textarea",
                        "value": dict(text=[text], **bbox),
                        "score": score,
                    }
                    annotation_result.extend([bbox_result, transcription_result])

            output_json["predictions"] = [{"result": annotation_result, "score": 0.97}]
            logger.info("Image processed successfully.")
            return output_json
        except Exception as e:
            logger.error("Error processing image: %s", e)
            return None
