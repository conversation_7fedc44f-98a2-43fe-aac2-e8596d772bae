// frontend/src/sphere/pages/ReviewPage.jsx

import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaPlus, FaRegPlayCircle } from 'react-icons/fa';

import ZoomableImage from "../components/ZoomableImage";
import { EditableField } from "../components/EditableMainField";
import EditableAddressField from "../components/EditableAddressField";
import EditableProductCell from "../components/EditableProductCell";
import ProductVerificationModal from "../components/ProductVerificationModal";
import DeleteConfirmationModal from "../components/DeleteConfirmationModal";
import CustomerLookupModal from "../components/CustomerLookupModal";
import CustomerInformation from "../components/CustomerInformation";
import OrderSummaryModal from "../components/OrderSummaryModal";
import {
    productTableColumns,
    addressFieldConfig,
    addressTypesConfig,
    editableFieldDefinitions,
} from "../config/reviewPageConfig";
import { useReviewForm } from "../hooks/useReviewForm";
import { useProductVerification } from "../hooks/useProductVerification";
import { useCustomerLookup } from "../hooks/useCustomerLookup";
import { skipPdfReview } from "../services/pdfService";

import '../../shared/styles/CommonActionButtons.css'
import '../styles/ReviewPage.css'
import '../styles/ProductVerificationModal.css'
import '../styles/DeleteConfirmationModal.css'
import '../styles/CustomerLookupModal.css'
import '../styles/CustomerInformation.css'
import '../styles/ZoomableImage.css'
import '../styles/OrderSummaryModal.css';

const PdfReviewPage = () => {
    const { pdfId } = useParams();
    const navigate = useNavigate();
    const {
        reviewData,
        formData,
        loading,
        error,
        updateFormData,
        updateProductField,
        deleteProductRow,
        addNewProductRow
    } = useReviewForm(pdfId);

    // Add a global event listener to prevent browser zoom when CTRL+wheel is used
    useEffect(() => {
        const preventBrowserZoom = (e) => {
            if (e.ctrlKey && e.type === 'wheel') {
                e.preventDefault();
                return false;
            }
        };

        // This function prevents the browser's zoom shortcuts
        const preventZoomShortcuts = (e) => {
            // Prevent Ctrl + Plus/Minus/0 (common zoom shortcuts)
            if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0' || e.key === '=')) {
                e.preventDefault();
                return false;
            }
        };

        // Use the capture phase to ensure our handlers run before the browser's default behavior
        document.addEventListener('wheel', preventBrowserZoom, { passive: false, capture: true });
        document.addEventListener('keydown', preventZoomShortcuts, { capture: true });

        // Add a style to prevent zooming on the page
        const style = document.createElement('style');
        style.textContent = `
            html, body {
                touch-action: pan-x pan-y; /* Prevent pinch zoom on touch devices */
            }
        `;
        document.head.appendChild(style);

        return () => {
            document.removeEventListener('wheel', preventBrowserZoom, { passive: false, capture: true });
            document.removeEventListener('keydown', preventZoomShortcuts, { capture: true });
            document.head.removeChild(style);
        };
    }, []);

    // State for delete confirmation modal
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [productToDelete, setProductToDelete] = useState(null);

    // State for order summary modal
    const [isOrderSummaryModalOpen, setIsOrderSummaryModalOpen] = useState(false);

    // State for validation errors
    const [validationErrors, setValidationErrors] = useState({});
    const [showValidationErrors, setShowValidationErrors] = useState(false);

    // State to track if product validation has failed
    const [productValidationFailed, setProductValidationFailed] = useState(false);

    // State to track if customer validation has failed
    const [customerValidationFailed, setCustomerValidationFailed] = useState(false);

    // State for skip functionality
    const [isSkipping, setIsSkipping] = useState(false);

    // Track verified products
    const [verifiedProducts, setVerifiedProducts] = useState(new Set());

    // Use the customer lookup hook first to initialize selectedCustomer
    const {
        isLookupModalOpen,
        lookupLoading,
        lookupError,
        customers,
        searchInput,
        setSearchInput,
        selectedCustomer,
        suggestedSearchValues,
        deliveryPostcode,
        handleOpenLookupModal,
        handleCloseLookupModal,
        handleSearchCustomer,
        handleSelectCustomer,
        handleUnselectCustomer
    } = useCustomerLookup({
        updateFormData,
        formData,
        editableFieldDefinitions
    });

    const {
        isVerificationModalOpen,
        verificationLoading,
        verificationError,
        verifiedProduct,
        searchResults,
        currentProductIndex,
        handleCheckProduct,
        handleSearchByName,
        handleSelectProduct,
        handleCloseVerificationModal,
        handleUnconfirmProduct
    } = useProductVerification({
        updateProductField,
        products: formData?.products || [],
        setVerifiedProducts,
        selectedCustomer
    });

    // Check if a product is verified
    const isProductVerified = (rowIndex) => {
        return verifiedProducts.has(rowIndex);
    };

    useEffect(() => {
        document.body.classList.add("no-scroll-for-review-page");
        return () => {
            document.body.classList.remove("no-scroll-for-review-page");
        };
    }, []);

    // Add class to body when customer lookup modal is open
    useEffect(() => {
        if (isLookupModalOpen) {
            document.body.classList.add("customer-lookup-modal-open");
        } else {
            document.body.classList.remove("customer-lookup-modal-open");
        }
        return () => {
            document.body.classList.remove("customer-lookup-modal-open");
        };
    }, [isLookupModalOpen]);

    // handleFormChange is now updateFormData from the hook
    const handleClearAddressField = (fieldKeyToClear) => {
        updateFormData(fieldKeyToClear, "");
        toast.success("Field cleared");
    };

    // Listen for product field changes to update verification status
    useEffect(() => {
        if (!formData?.products) return;

        // Check if any verified products have been modified
        setVerifiedProducts(prev => {
            const newVerified = new Set(prev);

            // Remove verification status for any modified products
            for (const index of prev) {
                if (index >= formData.products.length) {
                    newVerified.delete(index);
                    continue;
                }
            }

            return newVerified;
        });
    }, [formData?.products]);

    const handleDeleteProductRow = (rowIndex) => {
        // Open the delete confirmation modal and set the product to delete
        if (formData?.products && rowIndex < formData.products.length) {
            setProductToDelete(formData.products[rowIndex]);
            setIsDeleteModalOpen(true);
        }
    };

    // Function to handle confirmation from the modal
    const handleConfirmDelete = (rowIndex) => {
        deleteProductRow(rowIndex);
        toast.success("Product removed successfully");
    };

    // Function to close the delete modal
    const handleCloseDeleteModal = () => {
        setIsDeleteModalOpen(false);
        setProductToDelete(null);
    };

    // Function to validate address fields
    const validateAddressFields = useCallback(() => {
        const errors = {};
        let isValid = true;

        // Check address lines
        addressTypesConfig.forEach(addrType => {
            addressFieldConfig.forEach(fieldConfig => {
                if (fieldConfig.required) {
                    const fullKey = `${addrType.prefix}_${fieldConfig.key}`;
                    const value = formData[fullKey];

                    if (!value || value.trim() === '') {
                        errors[fullKey] = `${fieldConfig.label} is required`;
                        isValid = false;
                    }
                }
            });
        });

        setValidationErrors(errors);
        setShowValidationErrors(!isValid);

        if (!isValid) {
            toast.error('Please fill in all required address fields', {
                duration: 4000
            });
        }

        return isValid;
    }, [formData]);

    // Function to validate that all products are verified
    const validateAllProductsVerified = useCallback(() => {
        // Check if there are products
        if (!formData?.products || formData.products.length === 0) {
            setProductValidationFailed(true);
            toast.error('At least one product is required before submitting', {
                duration: 4000
            });
            return false;
        }

        // Check if all products are verified
        const allVerified = formData.products.every((_, index) => isProductVerified(index));

        // Update validation state
        setProductValidationFailed(!allVerified);

        if (!allVerified) {
            toast.error('All products must be verified before submitting', {
                duration: 4000
            });
        }

        return allVerified;
    }, [formData?.products, isProductVerified]);

    // Function to validate that a customer is selected
    const validateCustomerSelected = useCallback(() => {
        if (!selectedCustomer) {
            setCustomerValidationFailed(true);
            toast.error('Please select a customer before confirming the order', {
                duration: 4000
            });
            return false;
        }
        setCustomerValidationFailed(false);
        return true;
    }, [selectedCustomer]);

    // Add validation to form submission
    const handleSubmitForm = (e) => {
        e.preventDefault();

        // Validate that a customer is selected
        if (!validateCustomerSelected()) {
            // Scroll to the customer information section to help the user
            const customerSection = document.querySelector('.customer-information-section');
            if (customerSection) {
                customerSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
            return;
        }

        // Validate required address fields
        if (!validateAddressFields()) {
            return;
        }

        // Validate that all products are verified
        if (!validateAllProductsVerified()) {
            // Scroll to the products table to help the user see unverified products
            const productsTable = document.querySelector('.products-table-container');
            if (productsTable) {
                productsTable.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
            return;
        }

        // Reset validation states
        setProductValidationFailed(false);
        setCustomerValidationFailed(false);
        setShowValidationErrors(false);

        // Open the order summary modal
        setIsOrderSummaryModalOpen(true);
    };

    // Function to close the order summary modal
    const handleCloseOrderSummaryModal = () => {
        setIsOrderSummaryModalOpen(false);
    };

    // Function to handle skipping the PDF review
    const handleSkipReview = async () => {
        if (!pdfId) {
            toast.error('PDF ID is required to skip review');
            return;
        }

        setIsSkipping(true);

        try {
            await skipPdfReview(pdfId, 'User skipped review', '');
            toast.success('PDF review skipped successfully');

            // Navigate back to pending jobs page
            navigate('/pending-jobs');
        } catch (error) {
            console.error('Error skipping PDF review:', error);
            toast.error(error.message || 'Failed to skip PDF review');
        } finally {
            setIsSkipping(false);
        }
    };

    if (loading) {
        return (
            <div className="pdf-review-loading">
                <FaSpinner className="spinner" />
                <p>Loading PDF review data…</p>
            </div>
        );
    }

    if (error || !reviewData || !formData || !formData.products) {
        return (
            <div className="pdf-review-error">
                <p>
                    {error || "Unable to display PDF review information."}
                </p>
                {!pdfId && !error && <p>No PDF ID was provided.</p>}
            </div>
        );
    }

    const { pdf, images } = reviewData;

    const allHandledCandidateKeys = new Set();
    Object.entries(editableFieldDefinitions).forEach(([key, config]) => {
        if (config.source === "candidates") {
            allHandledCandidateKeys.add(key);
            if (config.optionsKey && config.optionsKey !== key) {
                allHandledCandidateKeys.add(config.optionsKey);
            }
        }
    });

    const editableCandidateKeysToRender = Object.keys(
        editableFieldDefinitions,
    ).filter((key) => editableFieldDefinitions[key].source === "candidates");

    return (
        <div className="pdf-review-page-layout">
            <div className="pdf-review-left-column">
                <div className="images-container-scrollable">
                    {images.map((img) => (
                        <div key={img.id} className="image-preview-wrapper">
                            <ZoomableImage
                                srcUrl={img.url}
                                alt={`Page ${img.id}`}
                            />
                        </div>
                    ))}
                </div>
            </div>

            <div className="pdf-review-right-column" autoComplete="off">
                {/* Hidden honeypot fields to confuse Chrome's autofill */}
                <div style={{ display: 'none', height: 0, overflow: 'hidden' }}>
                    <input type="text" name="address1" autoComplete="chrome-off" />
                    <input type="text" name="address2" autoComplete="chrome-off" />
                    <input type="text" name="city" autoComplete="chrome-off" />
                    <input type="text" name="state" autoComplete="chrome-off" />
                    <input type="text" name="zip" autoComplete="chrome-off" />
                    <input type="text" name="country" autoComplete="chrome-off" />
                </div>

                <header className="pdf-review-header section-card">
                    <h1>Review PDF #{pdf.id}</h1>
                    <p>
                        <strong>Filename:</strong> {pdf.origin_name}
                    </p>
                    <p>
                        <strong>Uploaded at:</strong> {pdf.uploaded_at}
                    </p>
                </header>

                {/* Customer Information Section */}
                <CustomerInformation
                    customer={selectedCustomer}
                    onFindCustomer={handleOpenLookupModal}
                    onUnselectCustomer={handleUnselectCustomer}
                    validationFailed={customerValidationFailed}
                />

                <section className="section-card candidates-section">
                    <h2>Order Details</h2>
                    <div className="candidates-form-grid">
                        {editableCandidateKeysToRender.map((key) => {
                            const config = editableFieldDefinitions[key];
                            const sourceObject =
                                config.source === "pdf"
                                    ? reviewData.pdf
                                    : reviewData.candidates;
                            const optionsSourceKey = config.optionsKey || key;
                            const optionsDataSource = sourceObject?.[optionsSourceKey];

                            return (
                                <EditableField
                                    key={key}
                                    fieldKey={key}
                                    label={config.label}
                                    currentValue={formData[key]}
                                    originalDataSource={optionsDataSource}
                                    onChange={updateFormData} // Use updateFormData from hook
                                    inputType={config.type}
                                    inputStep={config.step}
                                    useGridLayout={true} // Use the grid layout
                                />
                            );
                        })}
                    </div>
                </section>

                <section className="section-card addresses-section">
                    <div className="addresses-header">
                        <h2>Addresses</h2>
                    </div>
                    <div className="addresses-form-grid">
                        {addressTypesConfig.map((addrType) => (
                            <div
                                key={addrType.prefix}
                                className="address-form-block"
                            >
                                <h3>{addrType.title}</h3>
                                {/* Table-like structure for address fields */}
                                <div className="address-fields-table-like">
                                    {addressFieldConfig.map((fieldConfig) => {
                                        const fullKey = `${addrType.prefix}_${fieldConfig.key}`;
                                        const currentValue = formData[fullKey] || "";
                                        return (
                                            <div className="address-entry-row" key={fullKey}>
                                                <div className={`address-entry-label ${fieldConfig.required ? 'required-field' : ''}`}>
                                                    <label htmlFor={fullKey}>
                                                        {fieldConfig.label}
                                                        {fieldConfig.required && <span className="required-asterisk">*</span>}
                                                    </label>
                                                </div>
                                                <div className="address-entry-input">
                                                    <EditableAddressField
                                                        fieldKey={fullKey}
                                                        label={fieldConfig.label}
                                                        showLabel={false} // Instruct to not render internal label
                                                        value={currentValue}
                                                        onChange={updateFormData}
                                                        onClear={handleClearAddressField}
                                                        inputType={fieldConfig.type}
                                                        required={fieldConfig.required}
                                                        placeholderText={
                                                            !fieldConfig.required
                                                                ? "(Optional)"
                                                                : ""
                                                        }
                                                        error={validationErrors[fullKey]}
                                                        showValidationErrors={showValidationErrors}
                                                    />
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        ))}
                    </div>
                </section>

                <section className="section-card products-section">
                    <div className="products-header">
                        <h2>Products</h2>
                        <button
                            type="button"
                            onClick={() => {
                                // Check if a customer is selected before adding a new product
                                if (!selectedCustomer) {
                                    setCustomerValidationFailed(true);
                                    toast.error('Please select a customer before adding products', {
                                        duration: 4000
                                    });
                                    // Scroll to the customer information section
                                    const customerSection = document.querySelector('.customer-information-section');
                                    if (customerSection) {
                                        customerSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                    }
                                } else {
                                    setCustomerValidationFailed(false);
                                    addNewProductRow();
                                }
                            }}
                            className="action-button"
                            title="Add a new product row"
                            aria-label="Add a new product row"
                        >
                            <FaPlus /> Add New Product
                        </button>
                    </div>
                    <div className="table-container" autoComplete="off">
                        <table className="products-table" autoComplete="off">
                            <thead>
                                <tr>
                                    {productTableColumns.map((col) => (
                                        <th key={col.key} style={{ width: col.width }}>{col.label}</th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {formData.products.map((prod, rowIndex) => {
                                    const isVerified = isProductVerified(rowIndex);
                                    return (
                                        <tr
                                            key={rowIndex}
                                            className={
                                                isVerified
                                                    ? 'verified-product-row'
                                                    : productValidationFailed
                                                        ? 'unverified-product-row'
                                                        : ''
                                            }
                                        >
                                            {/* Consider using a unique product ID for key if available and rows can be reordered,
                                            but rowIndex is fine for simple deletion. */}
                                            {productTableColumns.map((col) => {
                                                if (col.key === "actions") {
                                                    return (
                                                        <td
                                                            key={col.key}
                                                            className="product-action-cell"
                                                        >
                                                            <div className="product-actions-container">
                                                                <button
                                                                    type="button"
                                                                    onClick={(e) => {
                                                                        if (isVerified) {
                                                                            handleUnconfirmProduct(rowIndex);
                                                                        } else {
                                                                            // Check if a customer is selected before verifying
                                                                            if (!selectedCustomer) {
                                                                                setCustomerValidationFailed(true);
                                                                                toast.error('Please select a customer before verifying products', {
                                                                                    duration: 4000
                                                                                });
                                                                                // Scroll to the customer information section
                                                                                const customerSection = document.querySelector('.customer-information-section');
                                                                                if (customerSection) {
                                                                                    customerSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                                                                }
                                                                            } else {
                                                                                setCustomerValidationFailed(false);
                                                                                handleCheckProduct(rowIndex);
                                                                            }
                                                                        }
                                                                        // Remove focus after click to prevent focus state persisting
                                                                        e.currentTarget.blur();
                                                                    }}
                                                                    className={`product-check-btn icon-action-button ${isVerified ? 'verified' : ''}`}
                                                                    title={isVerified ? "Click to unverify product" : "Verify product"}
                                                                    aria-label={`${isVerified ? 'Unverify' : 'Verify'} product ${rowIndex + 1}`}
                                                                >
                                                                    <FaCheck />
                                                                </button>
                                                                <button
                                                                    type="button"
                                                                    onClick={() => handleDeleteProductRow(rowIndex)}
                                                                    className="product-delete-btn icon-action-button"
                                                                    title="Remove product row"
                                                                    aria-label={`Remove product row ${rowIndex + 1}`}
                                                                    disabled={isVerified}
                                                                >
                                                                    <FaTrashAlt />
                                                                </button>
                                                            </div>
                                                        </td>
                                                    );
                                                }
                                                return (
                                                    <td key={col.key}>
                                                        <EditableProductCell
                                                            value={prod[col.key]}
                                                            onChange={updateProductField}
                                                            rowIndex={rowIndex}
                                                            columnKey={col.key}
                                                            readOnly={isVerified}
                                                        />
                                                    </td>
                                                );
                                            })}
                                        </tr>
                                    );
                                })}
                                {formData.products.length === 0 && (
                                    <tr className={productValidationFailed ? 'unverified-product-row' : ''}>
                                        <td
                                            colSpan={productTableColumns.length}
                                            className={`products-table-empty-message ${productValidationFailed ? 'validation-error' : ''}`}
                                        >
                                            {productValidationFailed
                                                ? 'At least one product is required'
                                                : 'No products listed.'}
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>
                </section>

                {/* Submit Section */}
                <div className="section-card submit-section">
                    <button
                        type="button"
                        className="action-button secondary"
                        onClick={handleSkipReview}
                        disabled={isSkipping}
                    >
                        {isSkipping ? (
                            <>
                                <FaSpinner className="spinner" />
                                Skipping...
                            </>
                        ) : (
                            <>
                                <FaRegPlayCircle />
                                Skip
                            </>
                        )}
                    </button>
                    <button
                        type="button"
                        className="action-button primary"
                        onClick={handleSubmitForm}
                    >
                        Confirm
                    </button>
                </div>
            </div>

            {/* Product Verification Modal */}
            <ProductVerificationModal
                isOpen={isVerificationModalOpen}
                onClose={handleCloseVerificationModal}
                loading={verificationLoading}
                product={verifiedProduct}
                searchResults={searchResults}
                originalProduct={currentProductIndex !== null ? formData.products[currentProductIndex] : null}
                onSelectProduct={handleSelectProduct}
                onSearchByName={handleSearchByName}
                error={verificationError}
            />

            {/* Delete Confirmation Modal */}
            <DeleteConfirmationModal
                isOpen={isDeleteModalOpen}
                onClose={handleCloseDeleteModal}
                onConfirm={handleConfirmDelete}
                product={productToDelete}
                rowIndex={productToDelete ? formData.products.indexOf(productToDelete) : -1}
            />

            {/* Customer Lookup Modal */}
            <CustomerLookupModal
                isOpen={isLookupModalOpen}
                onClose={handleCloseLookupModal}
                loading={lookupLoading}
                customers={customers}
                searchInput={searchInput}
                onSearch={handleSearchCustomer}
                onSelectCustomer={handleSelectCustomer}
                error={lookupError}
                setSearchInput={setSearchInput}
                suggestedSearchValues={suggestedSearchValues}
                deliveryPostcode={deliveryPostcode}
            />

            {/* Order Summary Modal */}
            <OrderSummaryModal
                isOpen={isOrderSummaryModalOpen}
                onClose={handleCloseOrderSummaryModal}
                formData={formData}
                selectedCustomer={selectedCustomer}
            />
        </div>
    );
};

export default PdfReviewPage;