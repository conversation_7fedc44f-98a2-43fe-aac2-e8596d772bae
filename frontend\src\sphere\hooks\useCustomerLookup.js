// frontend/src/sphere/hooks/useCustomerLookup.js
import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { lookupCustomer } from '../services/pdfService';

export const useCustomerLookup = ({ updateFormData, formData, editableFieldDefinitions }) => {
    const [isLookupModalOpen, setIsLookupModalOpen] = useState(false);
    const [lookupLoading, setLookupLoading] = useState(false);
    const [lookupError, setLookupError] = useState(null);
    const [customers, setCustomers] = useState(null);
    const [searchInput, setSearchInput] = useState('');
    const [selectedCustomer, setSelectedCustomer] = useState(null);
    const [suggestedSearchValues, setSuggestedSearchValues] = useState([]);
    const [deliveryPostcode, setDeliveryPostcode] = useState('');

    // Helper to determine if input is an email
    const isEmail = (input) => {
        if (!input || typeof input !== 'string') return false;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(input);
    };

    // Helper to determine if input is a phone number (basic validation)
    const isPhoneNumber = (input) => {
        if (!input || typeof input !== 'string') return false;
        // Simple check for digits, spaces, dashes, parentheses, and plus signs
        return /^[0-9\s\-\(\)\+]+$/.test(input);
    };

    // Extract email and phone values from formData based on field types
    const extractContactValues = () => {
        if (!formData || !editableFieldDefinitions) return { emails: [], phones: [] };

        const emails = [];
        const phones = [];

        // Check all editable fields for email and phone types
        Object.entries(editableFieldDefinitions).forEach(([key, config]) => {
            const value = formData[key];
            if (!value) return;

            if (config.type === 'email' && isEmail(value)) {
                emails.push(value);
            } else if (config.type === 'tel' && isPhoneNumber(value)) {
                phones.push(value);
            }
        });

        return { emails, phones };
    };

    // Open the customer lookup modal
    const handleOpenLookupModal = () => {
        setIsLookupModalOpen(true);
        setLookupError(null);
        setCustomers(null);
        setSearchInput('');

        // Extract contact values from form data
        const { emails, phones } = extractContactValues();

        // Set suggested search values
        const suggestions = [...emails, ...phones].filter(Boolean);
        setSuggestedSearchValues(suggestions);

        // Extract delivery postcode from form data
        if (formData && formData.delivery_postcode) {
            setDeliveryPostcode(formData.delivery_postcode);
        } else {
            setDeliveryPostcode('');
        }
    };

    // Close the customer lookup modal
    const handleCloseLookupModal = () => {
        setIsLookupModalOpen(false);
    };

    // Search for customers by phone or email
    const handleSearchCustomer = async () => {
        if (!searchInput.trim()) {
            toast.error('Please enter a phone number or email address');
            return;
        }

        setLookupLoading(true);
        setLookupError(null);
        setCustomers(null);

        try {
            // Determine if input is email or phone
            const emails = isEmail(searchInput) ? [searchInput] : [];
            const phones = !isEmail(searchInput) ? [searchInput] : [];

            // Call the API
            const results = await lookupCustomer(phones, emails);
            setCustomers(results);

            // Show message if no results
            if (results.length === 0) {
                toast('No customers found with the provided information', { icon: 'ℹ️' });
            }
        } catch (error) {
            console.error('Error looking up customer:', error);
            setLookupError(error.message || 'Failed to lookup customer');
            toast.error('Error looking up customer');
        } finally {
            setLookupLoading(false);
        }
    };

    // Handle customer selection
    const handleSelectCustomer = (customer) => {
        setSelectedCustomer(customer);

        // Update form data with customer information
        // Map customer fields to form fields
        updateFormData('delivery_name', customer.Company || '');
        updateFormData('delivery_addressLine1', customer['Address 1'] || '');
        updateFormData('delivery_addressLine2', customer['Address 2'] || '');
        updateFormData('delivery_addressLine3', customer['Address 3'] || '');
        updateFormData('delivery_addressLine4', customer.City || '');
        updateFormData('delivery_addressLine5', customer.County || '');
        updateFormData('delivery_postcode', customer.PostCode || '');
        updateFormData('delivery_country', customer.Country || '');

        // Store address code for reference
        updateFormData('address_code', customer.AddressCode || '');

        // Close the modal
        setIsLookupModalOpen(false);

        // Show success message
        toast.success('Customer information applied to delivery address');
    };

    // Handle unsetting a selected customer
    const handleUnselectCustomer = () => {
        setSelectedCustomer(null);

        // Clear customer-related form data
        updateFormData('address_code', '');

        // Show success message
        toast.success('Customer information removed');
    };

    // Check if address code exists in formData and set selectedCustomer accordingly
    useEffect(() => {
        if (formData && formData.address_code && !selectedCustomer) {
            // Create a minimal customer object with the ID
            setSelectedCustomer({
                AddressCode: formData.address_code,
                Company: formData.delivery_name || '',
                Contact: '',
                Email: '',
                'Address 1': formData.delivery_addressLine1 || '',
                'Address 2': formData.delivery_addressLine2 || '',
                'Address 3': formData.delivery_addressLine3 || '',
                City: formData.delivery_addressLine4 || '',
                County: formData.delivery_addressLine5 || '',
                PostCode: formData.delivery_postcode || '',
                Country: formData.delivery_country || ''
            });
        }
    }, [formData, selectedCustomer]);

    return {
        isLookupModalOpen,
        lookupLoading,
        lookupError,
        customers,
        searchInput,
        setSearchInput,
        selectedCustomer,
        suggestedSearchValues,
        deliveryPostcode,
        handleOpenLookupModal,
        handleCloseLookupModal,
        handleSearchCustomer,
        handleSelectCustomer,
        handleUnselectCustomer
    };
};
