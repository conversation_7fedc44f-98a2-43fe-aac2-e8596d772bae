// frontend/src/sphere/components/AuthenticatedImage.jsx
import React, { useState, useEffect } from 'react';
import { FaSpinner, FaExclamationTriangle } from 'react-icons/fa';
import { fetchImageAsBlob } from '../services/pdfService';

const AuthenticatedImage = ({ srcUrl, alt }) => {
    const [imageSrc, setImageSrc] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (!srcUrl) {
            setLoading(false);
            setError('No image URL provided.');
            return;
        }

        let objectUrl = null;
        const loadImage = async () => {
            setLoading(true);
            setError(null);
            setImageSrc(null); // Reset previous image if srcUrl changes
            try {
                const blob = await fetchImageAsBlob(srcUrl);
                objectUrl = URL.createObjectURL(blob);
                setImageSrc(objectUrl);
            } catch (err) {
                console.error(`Failed to load image "${alt}":`, err);
                setError(err.message || 'Failed to load image.');
            } finally {
                setLoading(false);
            }
        };

        loadImage();

        // Cleanup function to revoke the object URL
        return () => {
            if (objectUrl) {
                URL.revokeObjectURL(objectUrl);
            }
        };
    }, [srcUrl, alt]); // Re-run if srcUrl or alt changes

    if (loading) {
        return (
            <div className="authenticated-image-placeholder loading">
                <FaSpinner className="spinner" />
            </div>
        );
    }

    if (error) {
        return (
            <div
                className="authenticated-image-placeholder error"
                title={error}
            >
                <FaExclamationTriangle />
                <span>Error</span>
            </div>
        );
    }

    return imageSrc ? (
        <img src={imageSrc} alt={alt} className="loaded-image" />
    ) : null;
};

export default AuthenticatedImage;
