# frontend/nginx.conf

server {
    listen 9000;
    server_name _; # Listen for any hostname

    # Serve frontend static files from container path
    location / {
        root /usr/share/nginx/html; # Path where files are copied in Dockerfile
        index index.html;
        try_files $uri $uri/ /index.html; # Handle client-side routing
    }

    # Proxy order management requests to the backend Docker service 'ordermanagement' on port 5000
    location /om_api/ {
        proxy_pass http://ordermanagement:5000/api/; # Use Docker service name
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Proxy sphere requests to the backend Docker service 'orderprocessing' on port 5001
    location /sphere/ {
        proxy_pass http://orderprocessing:5001/api/; # Use Docker service name
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
