# Review and Visualise Clustering Script

This script provides functionality to review and visualise image clusters created by a clustering algorithm. It uses t-SNE and UMAP for visualisation.

## Dependencies

- `joblib`
- `numpy`
- `matplotlib.pyplot`
- `sklearn.manifold` (TSNE)
- `umap`
- `os`

## Functions

### review_clusters(cluster_file)
Loads and prints a summary of the clusters from a specified file.

- **Parameters**:
  - `cluster_file` (str): Path to the file containing the saved clusters.

### visualize_clusters_tsne(features, labels)
Visualises the clusters using t-SNE.

- **Parameters**:
  - `features` (numpy.ndarray): Array of feature vectors.
  - `labels` (numpy.ndarray): Array of cluster labels corresponding to the feature vectors.

### visualize_clusters_umap(features, labels)
Visualises the clusters using UMAP.

- **Parameters**:
  - `features` (numpy.ndarray): Array of feature vectors.
  - `labels` (numpy.ndarray): Array of cluster labels corresponding to the feature vectors.

### load_features(feature_file)
Loads feature vectors and corresponding image paths from a specified file.

- **Parameters**:
  - `feature_file` (str): Path to the file containing the saved features.

- **Returns**:
  - `features` (numpy.ndarray): Array of feature vectors.
  - `image_paths` (list): List of image paths corresponding to the feature vectors.

### load_cluster_labels(cluster_file, image_paths)
Loads cluster labels for a given list of image paths.

- **Parameters**:
  - `cluster_file` (str): Path to the file containing the saved clusters.
  - `image_paths` (list): List of image paths for which to load cluster labels.

- **Returns**:
  - `labels` (numpy.ndarray): Array of cluster labels corresponding to the image paths.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the file paths for the feature file and cluster file.
2. Reviews the clusters by printing a summary.
3. Loads features and image paths from the specified file.
4. Loads cluster labels for the image paths.
5. Visualises the clusters using t-SNE.
6. Visualises the clusters using UMAP.

## Usage

Run the script using Python:

```bash
python review_clustering.py
```

## Visualisation

The script provides two methods for visualising clusters:
- **t-SNE**: A dimensionality reduction technique that visualises high-dimensional data in a two-dimensional space.
- **UMAP**: Another dimensionality reduction technique that preserves more of the global structure of the data compared to t-SNE.

Both visualisations use the `nipy_spectral` colormap to distinguish different clusters.

## Error Handling

Errors encountered during the review and visualisation process should be handled appropriately within the context of your application. Currently, the script prints progress and results for each step.

```

This documentation provides a comprehensive overview of the `review_clustering.py` script, explaining its purpose, functionality, and usage.