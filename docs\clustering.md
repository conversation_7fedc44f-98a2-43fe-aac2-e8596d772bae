# Image Clustering Script

This script clusters images based on their extracted features using the KMeans clustering algorithm. The number of clusters can be determined using the Elbow Method. The clustered images and the trained KMeans model are saved for further analysis.

## Dependencies

- `numpy`
- `sklearn.cluster` (KMeans)
- `joblib`
- `matplotlib.pyplot`

## Class: ImageClustering

### __init__(self, n_clusters=100)
Initialises the `ImageClustering` class with a specified number of clusters.

- **Parameters**:
  - `n_clusters` (int): The initial number of clusters to use for KMeans. Default is 100.

### load_features(self, feature_file)
Loads image features from a specified numpy file.

- **Parameters**:
  - `feature_file` (str): Path to the numpy file containing image features.

### determine_optimal_clusters(self, max_clusters=100)
Determines the optimal number of clusters using the Elbow Method.

- **Parameters**:
  - `max_clusters` (int): The maximum number of clusters to consider for the Elbow Method. Default is 100.

### perform_clustering(self)
Performs KMeans clustering on the loaded features.

### save_clusters(self, cluster_output_file, model_output_file)
Saves the clustered images and the KMeans model to specified files.

- **Parameters**:
  - `cluster_output_file` (str): Path to the output file where clustered images will be saved.
  - `model_output_file` (str): Path to the output file where the KMeans model will be saved.

### run_clustering(self, feature_file, cluster_output_file, model_output_file, max_clusters=100)
Runs the entire clustering process: loading features, determining optimal clusters, performing clustering, and saving the results.

- **Parameters**:
  - `feature_file` (str): Path to the numpy file containing image features.
  - `cluster_output_file` (str): Path to the output file where clustered images will be saved.
  - `model_output_file` (str): Path to the output file where the KMeans model will be saved.
  - `max_clusters` (int): The maximum number of clusters to consider for the Elbow Method. Default is 100.

## Main Script Execution

The main block of the script performs the following steps:
1. Defines the file paths for the feature file, cluster output file, and model output file.
2. Instantiates the `ImageClustering` class with the specified maximum number of clusters.
3. Runs the clustering process using the specified files and parameters.
4. Prints a message indicating the completion of the clustering process.

## Usage

Run the script using Python:

```bash
python clustering.py
```

## Visualisation

The script uses the Elbow Method to determine the optimal number of clusters. A plot is displayed to help visually identify the "elbow point" where the within-cluster sum of squares (WCSS) starts to level off.

## Logging

The script prints information about the clustering process, including the number of clusters and the paths to the saved output files.

## Error Handling

Errors encountered during the clustering process should be handled appropriately within the context of your application. Currently, the script prompts the user to input the optimal number of clusters based on the Elbow Method plot.
