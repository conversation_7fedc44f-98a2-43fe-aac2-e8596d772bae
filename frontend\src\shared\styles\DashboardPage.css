/* frontend/src/shared/styles/DashboardPage.css */

/* Import design system */
@import './design-system.css';

.dashboard-page {
    padding: var(--ds-spacing-3xl) 0;
    font-family: var(--ds-font-family);
    background-attachment: fixed;
    position: relative;
}

/* Subtle background pattern */
.dashboard-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: var(--ds-pattern-dots);
    opacity: 0.5;
    z-index: -1;
    pointer-events: none;
}

.dashboard-header {
    margin-bottom: var(--ds-spacing-4xl);
    position: relative;
    padding-bottom: var(--ds-spacing-lg);
}

.dashboard-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: var(--ds-border-width-accent);
    background: var(--ds-accent-1);
    border-radius: 2px;
}

.dashboard-header h1 {
    color: var(--ds-text);
    font-size: var(--ds-font-size-4xl);
    margin-bottom: var(--ds-spacing-md);
    font-weight: var(--ds-font-weight-bold);
    letter-spacing: var(--ds-letter-spacing-tight);
    position: relative;
    display: inline-block;
}

.dashboard-subtitle {
    color: var(--ds-text-secondary);
    font-size: var(--ds-font-size-lg);
    line-height: var(--ds-line-height-normal);
    max-width: 700px;
    font-weight: var(--ds-font-weight-regular);
}

.dashboard-sections {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--ds-spacing-3xl);
    perspective: 1000px; /* For 3D effect on hover */
}

.section-card {
    background-color: var(--ds-card);
    border-radius: var(--ds-border-radius-lg);
    box-shadow: var(--ds-card-shadow);
    overflow: hidden;
    transition: all var(--ds-transition-normal);
    border: var(--ds-card-border-subtle);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
    transform-style: preserve-3d;
    transform: translateZ(0);
}

.section-card:hover {
    box-shadow: var(--ds-card-shadow-hover);
    transform: translateY(-5px) scale(1.01);
    border: var(--ds-card-border-accent);
    z-index: 2;
}

/* Section-specific accent colors */
.section-card:nth-child(1) {
    border-top: var(--ds-border-width-accent) solid var(--ds-accent-1);
}

.section-card:nth-child(2) {
    border-top: var(--ds-border-width-accent) solid var(--ds-accent-2);
}

.section-card:nth-child(3) {
    border-top: var(--ds-border-width-accent) solid var(--ds-accent-3);
}

.section-header {
    padding: var(--ds-spacing-2xl) var(--ds-spacing-2xl) 0;
    position: relative;
}

.section-header h2 {
    color: var(--ds-text);
    font-size: var(--ds-font-size-2xl);
    margin: 0;
    font-weight: var(--ds-font-weight-semibold);
    display: flex;
    align-items: center;
    letter-spacing: var(--ds-letter-spacing-tight);
}

.section-header h2::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--ds-spacing-md);
    background-color: var(--ds-accent-1);
    box-shadow: 0 0 0 4px rgba(0, 120, 212, 0.1);
}

.section-card:nth-child(2) .section-header h2::before {
    background-color: var(--ds-accent-2);
    box-shadow: 0 0 0 4px rgba(0, 150, 136, 0.1);
}

.section-card:nth-child(3) .section-header h2::before {
    background-color: var(--ds-accent-3);
    box-shadow: 0 0 0 4px rgba(156, 39, 176, 0.1);
}

.section-content {
    padding: var(--ds-spacing-xl) var(--ds-spacing-2xl) var(--ds-spacing-2xl);
}

.section-content p {
    color: var(--ds-text-secondary);
    margin-bottom: var(--ds-spacing-2xl);
    line-height: var(--ds-line-height-relaxed);
    font-size: var(--ds-font-size-sm);
}

.section-links {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-md);
}

.section-link {
    display: flex;
    align-items: center;
    padding: var(--ds-spacing-lg) var(--ds-spacing-xl);
    border-radius: var(--ds-border-radius-md);
    text-decoration: none;
    color: var(--ds-text);
    background-color: var(--ds-background-light);
    transition: all var(--ds-transition-normal);
    border: var(--ds-border-width) solid transparent;
    position: relative;
    overflow: hidden;
}

/* Shine effect for section links */
.section-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--ds-gradient-shine);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.section-link:hover {
    background-color: var(--ds-background-hover);
    border-color: var(--ds-border-light);
}

.section-link:hover::before {
    transform: translateX(100%);
    /* Make the animation more noticeable */
    transition: transform var(--ds-transition-shine);
}

.section-link:active {
    transform: translateX(4px) scale(0.98);
}

.section-icon {
    margin-right: var(--ds-spacing-lg);
    font-size: var(--ds-font-size-xl);
    color: var(--ds-primary);
    transition: transform var(--ds-transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: var(--ds-border-radius-md);
    flex-shrink: 0;
}

.section-card:nth-child(2) .section-icon {
    color: var(--ds-accent-2);
}

.section-card:nth-child(3) .section-icon {
    color: var(--ds-accent-3);
}

.section-link:hover .section-icon {
    transform: scale(1.1);
}

.section-link span {
    font-weight: var(--ds-font-weight-medium);
    font-size: var(--ds-font-size-sm);
    transition: color var(--ds-transition-normal);
}

.section-link:hover span {
    color: var(--ds-primary);
}

.arrow-icon {
    margin-left: auto;
    font-size: var(--ds-font-size-sm);
    color: var(--ds-text-secondary);
    transition: all var(--ds-transition-normal);
    opacity: 0.7;
}

.section-link:hover .arrow-icon {
    transform: translateX(5px);
    color: var(--ds-primary);
    opacity: 1;
}

.loading-state {
    animation: ds-pulse 1.5s infinite;
}

.dashboard-header {
    animation: ds-fade-in-up 0.6s ease-out;
}

.section-card {
    opacity: 0;
    animation: ds-fade-in-up 0.6s ease-out forwards;
}

.section-card:nth-child(1) {
    animation-delay: 0.1s;
}

.section-card:nth-child(2) {
    animation-delay: 0.2s;
}

.section-card:nth-child(3) {
    animation-delay: 0.3s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-sections {
        grid-template-columns: 1fr;
    }

    .dashboard-header h1 {
        font-size: var(--ds-font-size-3xl);
    }

    .dashboard-subtitle {
        font-size: var(--ds-font-size-md);
    }

    .section-card {
        animation-delay: 0.1s !important;
    }
}

/* Dark mode enhancements */
html.dark-mode .section-card {
    backdrop-filter: blur(5px);
    background-color: rgba(30, 30, 30, 0.8);
}
