# app/__init__.py

import logging
from flask import Flask
from flask_cors import CORS
from .config import Config
from .routes import register_blueprints
from .utils.logger import setup_logging
from .extensions import db

import os


def create_app(config_class=Config):
    # Create Flask app instance
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Attach SQLAlchemy to this app
    db.init_app(app)

    # Initialise logging
    setup_logging(
        log_level=app.config.get("LOG_LEVEL"),
        log_dir=app.config.get("LOG_DIR", "logs"),
        log_file_name=app.config.get("LOG_FILE_APP", "app.log"),
        max_bytes=app.config.get("LOG_MAX_BYTES", 5 * 1024 * 1024),
        backup_count=app.config.get("LOG_BACKUP_COUNT", 3),
    )

    # Set higher logging level for httpx used by Supabase
    logging.getLogger("supabase").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)

    # Enable CORS for all routes
    allowed_origins = app.config.get("ALLOWED_ORIGINS")
    origins = [origin.strip() for origin in allowed_origins.split(",")]
    CORS(app, resources={r"/*": {"origins": origins}})

    # Register blueprints for API endpoints
    register_blueprints(app)

    # Perform app-context initialisations
    with app.app_context():
        # Import models to register them with SQLAlchemy
        from .models_db import pdf, image, annotation, order

        # Create necessary directories
        os.makedirs(app.config["UPLOAD_FOLDER"], exist_ok=True)
        os.makedirs(app.config["OUTPUT_FOLDER"], exist_ok=True)

    return app
