# backend/app/services/supabase.py
import os
import logging
from supabase import create_client, Client
from supabase.client import ClientOptions
from flask import current_app
from ..auth.jwt_auth import get_current_token

logger = logging.getLogger(__name__)


def get_request_supabase_client() -> Client:
    """
    Creates a Supabase client instance configured for the current request's user.
    Uses the ANON key for initialization but overrides auth with the user's token.
    """
    token = get_current_token()  # Get token stored in g by @requires_auth
    if not token:
        # This should not happen if @requires_auth is used correctly
        logger.error(
            "Attempted to get request-specific Supabase client without token in context."
        )
        raise Exception("Authentication token not found in request context.")

    # Use ANON key for base client creation, as the Authorization header will override it
    # Ensure ANON key is available in config/env
    url = current_app.config.get("SUPABASE_URL")
    anon_key = current_app.config.get("SUPABASE_KEY")

    if not url or not anon_key:
        logger.error(
            "SUPABASE_URL or SUPABASE_ANON_KEY not configured for request client."
        )
        raise ValueError("Supabase URL and Anon Key must be configured.")

    # Create headers with the user's token
    headers = {"Authorization": f"Bearer {token}"}

    # Create a ClientOptions instance with headers
    client_options = ClientOptions(headers=headers)

    logger.debug(
        f"Creating Supabase client for request with custom Authorization header."
    )
    # Create and return the Supabase client with custom headers
    return create_client(url, anon_key, options=client_options)
