# Inference Script for LayoutLMv3

This script performs inference on annotated image data using a pretrained LayoutLMv3 model for token classification. It processes the data, makes predictions, and decodes the predictions to return meaningful results.

## Dependencies

- `torch`
- `torch.utils.data`
- `transformers`
- `PIL`
- `collections.Counter`

## Classes

### InferenceDataset

A custom dataset class for processing the annotated image data for inference.

#### __init__(self, data, processor)
Initialises the dataset with data and processor.

- **Parameters**:
  - `data` (list): List of annotated image data.
  - `processor` (LayoutLMv3Processor): Processor for the LayoutLMv3 model.

#### __len__(self)
Returns the length of the dataset.

- **Returns**:
  - `length` (int): Length of the dataset.

#### __getitem__(self, idx)
Retrieves an item by index, processes the image and annotations, and returns the encoded inputs.

- **Parameters**:
  - `idx` (int): Index of the item.

- **Returns**:
  - `encoding` (dict): Encoded inputs for the model.
  - `words` (list): List of words in the image.
  - `boxes` (list): List of bounding boxes for the words.
  - `image` (PIL.Image): Image object.

### ModelInference

A class for performing inference with the LayoutLMv3 model.

#### __init__(self, model_path, processor_path)
Initialises the inference class with the paths to the model and processor.

- **Parameters**:
  - `model_path` (str): Path to the pretrained model.
  - `processor_path` (str): Path to the pretrained processor.

#### predict(self, data)
Performs inference on the input data and returns the predictions.

- **Parameters**:
  - `data` (list): List of annotated image data.

- **Returns**:
  - `predictions` (numpy.ndarray): Predictions from the model.
  - `sample` (dict): Encoded inputs for the model.
  - `words` (list): List of words in the image.
  - `boxes` (list): List of bounding boxes for the words.
  - `image` (PIL.Image): Image object.

#### decode_predictions(self, predictions, sample, words, boxes)
Decodes the model predictions to return meaningful results.

- **Parameters**:
  - `predictions` (numpy.ndarray): Predictions from the model.
  - `sample` (dict): Encoded inputs for the model.
  - `words` (list): List of words in the image.
  - `boxes` (list): List of bounding boxes for the words.

- **Returns**:
  - `final_results` (list): List of dictionaries containing words, labels, and bounding boxes.

## Important Notes

- The script assumes that the `InferenceDataset` and `ModelInference` classes are initialised correctly with the appropriate paths and data.
- The script processes the annotations extracted from the image, performs inference using the pretrained LayoutLMv3 model, and decodes the predictions to return meaningful results.
