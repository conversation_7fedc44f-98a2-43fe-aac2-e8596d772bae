// frontend/src/sphere/components/DeleteConfirmationModal.jsx
import React from 'react';
import { FaExclamationTriangle, FaTrashAlt, FaTimes } from 'react-icons/fa';
import '../styles/DeleteConfirmationModal.css';

/**
 * A custom modal component for confirming product deletion
 *
 * @param {Object} props Component props
 * @param {boolean} props.isOpen Whether the modal is open
 * @param {Function} props.onClose Function to close the modal
 * @param {Function} props.onConfirm Function to confirm the deletion
 * @param {Object} props.product The product to be deleted (optional)
 * @param {number} props.rowIndex The index of the product row to be deleted
 */
const DeleteConfirmationModal = ({
    isOpen,
    onClose,
    onConfirm,
    product,
    rowIndex
}) => {
    if (!isOpen) return null;

    // Get product name or code for display
    const productIdentifier = product ?
        (product.Product_Name || product.Product_Code || `Row ${rowIndex + 1}`) :
        `Row ${rowIndex + 1}`;

    const handleConfirm = () => {
        onConfirm(rowIndex);
        onClose();
    };

    return (
        <div className="delete-confirmation-modal-overlay" onClick={onClose}>
            <div className="delete-confirmation-modal" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>
                        <FaExclamationTriangle className="warning-icon" />
                        Confirm Deletion
                    </h2>
                    <button className="close-button" onClick={onClose}>
                        <FaTimes />
                    </button>
                </div>
                <div className="modal-body">
                    <p className="delete-warning">
                        Are you sure you want to remove this product row?
                    </p>
                    <div className="product-to-delete">
                        <FaTrashAlt className="delete-icon" />
                        <span>{productIdentifier}</span>
                    </div>
                    <p className="delete-note">
                        This action cannot be undone.
                    </p>
                </div>
                <div className="modal-footer">
                    <button
                        className="cancel-button"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                    <button
                        className="confirm-button"
                        onClick={handleConfirm}
                    >
                        <FaTrashAlt style={{ marginRight: '6px' }} /> Delete
                    </button>
                </div>
            </div>
        </div>
    );
};

export default DeleteConfirmationModal;
