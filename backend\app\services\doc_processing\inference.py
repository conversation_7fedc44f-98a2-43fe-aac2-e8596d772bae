# backend/app/services/doc_processing/inference.py

import logging
import torch
from transformers import LayoutLMv3Processor, LayoutLMv3ForTokenClassification
from collections import Counter

logger = logging.getLogger(__name__)


class ModelInference:
    def __init__(self, model_path, processor_path):
        try:
            self.processor = LayoutLMv3Processor.from_pretrained(processor_path)
            self.model = LayoutLMv3ForTokenClassification.from_pretrained(model_path)
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.model.to(self.device)
            self.model.eval()

            self.label_mapping = {
                "PO_Number": 1,
                "Contact_Name": 2,
                "Contact_Phone": 3,
                "Contact_Email": 4,
                "Delivery_Address": 5,
                "Invoice_Address": 6,
                "Product_Code": 7,
                "Product_Qty": 8,
                "Invoice_Email": 9,
                "Product_Name": 0,
                "Unit_Cost": 10,
                "Line_Value": 11,
                "Total_Value": 12,
                "Ignore_Text": 13,
                "Order_Date": 14,
                "Invoice_Phone": 15,
            }

            self.id_to_label = {v: k for k, v in self.label_mapping.items()}
            logger.info("Model and processor successfully loaded.")
        except Exception as e:
            logger.error(f"Error initializing ModelInference: {e}")
            raise

    def predict(self, data, image):
        try:
            width, height = data[0]["width"], data[0]["height"]
            annotations = data[0]["annotations"]
            boxes = []
            words = []

            for ann in annotations:
                boxes.append([int(b * 1000) for b in ann["box"]])
                words.append(ann["text"])

            image = image.convert("RGB")
            encoding = self.processor(
                images=image,
                text=words,
                boxes=boxes,
                padding="max_length",
                max_length=512,  # Match training config
                stride=128,  # Match training config
                truncation=True,
                return_tensors="pt",
                return_offsets_mapping=True,
                return_overflowing_tokens=True,
            )

            # Stack pixel_values if it's a list
            if isinstance(encoding["pixel_values"], list):
                encoding["pixel_values"] = torch.stack(encoding["pixel_values"])

            with torch.no_grad():
                # Explicitly specify inputs to avoid non-tensor values
                inputs = {
                    "input_ids": encoding["input_ids"].to(self.device),
                    "attention_mask": encoding["attention_mask"].to(self.device),
                    "bbox": encoding["bbox"].to(self.device),
                    "pixel_values": encoding["pixel_values"].to(self.device),
                }
                outputs = self.model(**inputs)
                predictions = outputs.logits.argmax(dim=-1).cpu().numpy()

            logger.info("Prediction completed successfully.")
            return predictions, encoding, words, boxes, image
        except Exception as e:
            logger.error(f"Error during prediction: {e}")
            raise

    def decode_predictions(self, predictions, encoding, words, boxes):
        try:
            word_to_labels = {}

            num_chunks = predictions.shape[0]

            for chunk_idx in range(num_chunks):
                pred_labels = predictions[chunk_idx]
                word_ids = encoding.word_ids(batch_index=chunk_idx)

                for idx, label_id in enumerate(pred_labels):
                    word_idx = word_ids[idx]
                    if word_idx is None:
                        continue  # Skip special tokens
                    if word_idx not in word_to_labels:
                        word_to_labels[word_idx] = []
                    word_to_labels[word_idx].append(label_id)

            final_results = []
            for word_idx in range(len(words)):
                labels = word_to_labels.get(word_idx, [])
                if labels:
                    most_common_label = Counter(labels).most_common(1)[0][0]
                else:
                    most_common_label = 0  # Default label if no labels
                word = words[word_idx]
                if word.strip():
                    final_results.append(
                        {
                            "word": word,
                            "label": self.id_to_label[most_common_label],
                            "box": boxes[word_idx],
                        }
                    )

            logger.info("Decoding predictions completed successfully.")
            return final_results
        except Exception as e:
            logger.error(f"Error decoding predictions: {e}")
            raise
